<div class='progress-msg'><PERSON><PERSON>, 13 Aug 2024 18:35:42 +0000</div>
<div class='progress-msg'>[18:35:42] Composing titles...</div>
<div class='progress-msg'>[18:35:42] Composing excerpts...</div>
<div class='progress-msg'>[18:35:42] Composing duplicate indicators...</div>
<div class='progress-msg'>[18:35:42] Composing authors...</div>
<div class='progress-msg'>[18:35:42] Composing slugs...</div>
<div class='progress-msg'>[18:35:42] Composing menu order...</div>
<div class='progress-msg'>[18:35:42] Composing contents...</div>
<div class='progress-msg'>[18:35:42] Composing dates...</div>
<div class='progress-msg'>[18:35:42] Composing terms for `Categorías del producto` taxonomy...</div>
<div class='progress-msg'>[18:35:42] Composing terms for `Etiquetas del producto` taxonomy...</div>
<div class='progress-msg'>[18:35:42] Composing custom parameters...</div>
<div class='progress-msg'>[18:35:42] Composing URLs for images...</div>
<div class='progress-msg'>[18:35:42] Composing URLs for attachments files...</div>
<div class='progress-msg'>[18:35:42] Composing post comments...</div>
<div class='progress-msg'>[18:35:42] Composing unique keys...</div>
<div class='progress-msg'>[18:35:42] Processing posts...</div>
<div class='progress-msg'>[18:35:42] Data parsing via add-ons...</div>
<div class='progress-msg'>[18:35:42] Composición de datos de productos...</div>
<div class='progress-msg'>[18:35:42] ---</div>
<div class='progress-msg'>[18:35:42] Record #1</div>
<div class='progress-msg'>[18:35:42] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:42] Combine all data for post `POLLO PANAMA ENTERO`...</div>
<div class='progress-msg'>[18:35:42] Find corresponding article among database for post `POLLO PANAMA ENTERO`...</div>
<div class='progress-msg'>[18:35:42] Duplicate post wasn't found for post `POLLO PANAMA ENTERO`...</div>
<div class='progress-msg'>[18:35:42] Applying filter `pmxi_article_data` for `POLLO PANAMA ENTERO`</div>
<div class='progress-msg'>[18:35:42] <b>CREATING</b> `POLLO PANAMA ENTERO` `Producto`</div>
<div class='progress-msg'>[18:35:43] Associate post `POLLO PANAMA ENTERO` with current import ...</div>
<div class='progress-msg'>[18:35:43] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:43] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:43] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:43] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `sku` updated with value `bf433b86238e`</div>
<div class='progress-msg'>[18:35:43] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:43] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:43] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:43] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:43] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:43] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:43] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:43] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:43] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:43] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:43] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:43] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/POLLO-PANAMA-ENTERO.jpg` for `POLLO PANAMA ENTERO` ...</div>
<div class='progress-msg'>[18:35:43] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/POLLO-PANAMA-ENTERO.jpg` by URL...</div>
<div class='progress-msg'>[18:35:43] - <b>WARNING</b>: Image POLLO-PANAMA-ENTERO.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:35:43] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/POLLO-PANAMA-ENTERO.jpg`</div>
<div class='progress-msg'>[18:35:43] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/POLLO-PANAMA-ENTERO.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:43] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/POLLO-PANAMA-ENTERO.jpg`</div>
<div class='progress-msg'>[18:35:43] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:43] - Attachment with ID: `19049` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/POLLO-PANAMA-ENTERO.jpg`</div>
<div class='progress-msg'>[18:35:43] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:43] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:43] - Creating parent Producto product_cat `CARNES Y AVES` ...</div>
<div class='progress-msg'>[18:35:43] - Creating child Producto product_cat for CARNES Y AVES named `POLLO` ...</div>
<div class='progress-msg'>[18:35:43] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:43] <b>CREATED</b> `POLLO PANAMA ENTERO` `Producto` (ID: 19048)</div>
<div class='progress-msg'>[18:35:44] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:44] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:44] ---</div>
<div class='progress-msg'>[18:35:44] Record #2</div>
<div class='progress-msg'>[18:35:44] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:44] Combine all data for post `ALITAS`...</div>
<div class='progress-msg'>[18:35:44] Find corresponding article among database for post `ALITAS`...</div>
<div class='progress-msg'>[18:35:44] Duplicate post wasn't found for post `ALITAS`...</div>
<div class='progress-msg'>[18:35:44] Applying filter `pmxi_article_data` for `ALITAS`</div>
<div class='progress-msg'>[18:35:44] <b>CREATING</b> `ALITAS` `Producto`</div>
<div class='progress-msg'>[18:35:44] Associate post `ALITAS` with current import ...</div>
<div class='progress-msg'>[18:35:44] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:44] Property `price` updated with value `1.75`</div>
<div class='progress-msg'>[18:35:44] Property `regular_price` updated with value `1.75`</div>
<div class='progress-msg'>[18:35:44] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:44] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:44] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `sku` updated with value `84c816876a28`</div>
<div class='progress-msg'>[18:35:44] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:44] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:44] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:44] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:44] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:44] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:44] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:44] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:44] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:44] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:44] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:44] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ALITAS-DE-POLLO-1.webp` for `ALITAS` ...</div>
<div class='progress-msg'>[18:35:44] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ALITAS-DE-POLLO-1.webp` by URL...</div>
<div class='progress-msg'>[18:35:44] - <b>WARNING</b>: Image ALITAS-DE-POLLO-1.webp not found in media gallery.</div>
<div class='progress-msg'>[18:35:44] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ALITAS-DE-POLLO-1.webp`</div>
<div class='progress-msg'>[18:35:44] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ALITAS-DE-POLLO-1.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:44] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/ALITAS-DE-POLLO-1.webp`</div>
<div class='progress-msg'>[18:35:44] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:44] - Attachment with ID: `19051` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/ALITAS-DE-POLLO-1.webp`</div>
<div class='progress-msg'>[18:35:44] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:44] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:44] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:44] - Attempted to create child Producto product_cat `POLLO`, duplicate detected. Importing Producto to existing `POLLO` product_cat, ID 3099, slug `pollo` ...</div>
<div class='progress-msg'>[18:35:44] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:44] <b>CREATED</b> `ALITAS` `Producto` (ID: 19050)</div>
<div class='progress-msg'>[18:35:44] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:44] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:44] ---</div>
<div class='progress-msg'>[18:35:44] Record #3</div>
<div class='progress-msg'>[18:35:44] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:44] Combine all data for post `MUSLO ENCUENTRO`...</div>
<div class='progress-msg'>[18:35:44] Find corresponding article among database for post `MUSLO ENCUENTRO`...</div>
<div class='progress-msg'>[18:35:44] Duplicate post wasn't found for post `MUSLO ENCUENTRO`...</div>
<div class='progress-msg'>[18:35:44] Applying filter `pmxi_article_data` for `MUSLO ENCUENTRO`</div>
<div class='progress-msg'>[18:35:44] <b>CREATING</b> `MUSLO ENCUENTRO` `Producto`</div>
<div class='progress-msg'>[18:35:45] Associate post `MUSLO ENCUENTRO` with current import ...</div>
<div class='progress-msg'>[18:35:45] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:45] Property `price` updated with value `1.45`</div>
<div class='progress-msg'>[18:35:45] Property `regular_price` updated with value `1.45`</div>
<div class='progress-msg'>[18:35:45] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:45] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:45] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `sku` updated with value `09929c195488`</div>
<div class='progress-msg'>[18:35:45] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:45] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:45] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:45] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:45] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:45] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:45] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:45] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:45] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:45] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:45] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MUSLOENCUENTRO-DE-POLLO.jpg` for `MUSLO ENCUENTRO` ...</div>
<div class='progress-msg'>[18:35:45] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MUSLOENCUENTRO-DE-POLLO.jpg` by URL...</div>
<div class='progress-msg'>[18:35:45] - <b>WARNING</b>: Image MUSLOENCUENTRO-DE-POLLO.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:35:45] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MUSLOENCUENTRO-DE-POLLO.jpg`</div>
<div class='progress-msg'>[18:35:45] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MUSLOENCUENTRO-DE-POLLO.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:45] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/MUSLOENCUENTRO-DE-POLLO.jpg`</div>
<div class='progress-msg'>[18:35:45] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:45] - Attachment with ID: `19053` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/MUSLOENCUENTRO-DE-POLLO.jpg`</div>
<div class='progress-msg'>[18:35:45] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:45] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:45] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:45] - Attempted to create child Producto product_cat `POLLO`, duplicate detected. Importing Producto to existing `POLLO` product_cat, ID 3099, slug `pollo` ...</div>
<div class='progress-msg'>[18:35:45] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:45] <b>CREATED</b> `MUSLO ENCUENTRO` `Producto` (ID: 19052)</div>
<div class='progress-msg'>[18:35:45] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:45] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:45] ---</div>
<div class='progress-msg'>[18:35:45] Record #4</div>
<div class='progress-msg'>[18:35:45] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:45] Combine all data for post `PECHUGA`...</div>
<div class='progress-msg'>[18:35:45] Find corresponding article among database for post `PECHUGA`...</div>
<div class='progress-msg'>[18:35:45] Duplicate post wasn't found for post `PECHUGA`...</div>
<div class='progress-msg'>[18:35:45] Applying filter `pmxi_article_data` for `PECHUGA`</div>
<div class='progress-msg'>[18:35:45] <b>CREATING</b> `PECHUGA` `Producto`</div>
<div class='progress-msg'>[18:35:45] Associate post `PECHUGA` with current import ...</div>
<div class='progress-msg'>[18:35:45] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:45] Property `price` updated with value `1.6`</div>
<div class='progress-msg'>[18:35:45] Property `regular_price` updated with value `1.6`</div>
<div class='progress-msg'>[18:35:45] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:45] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:45] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `sku` updated with value `97b1b3a8d110`</div>
<div class='progress-msg'>[18:35:45] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:45] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:45] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:45] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:45] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:45] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:45] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:45] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:45] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:45] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:46] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:46] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PECHUGAS-DE-POLLO.webp` for `PECHUGA` ...</div>
<div class='progress-msg'>[18:35:46] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PECHUGAS-DE-POLLO.webp` by URL...</div>
<div class='progress-msg'>[18:35:46] - <b>WARNING</b>: Image PECHUGAS-DE-POLLO.webp not found in media gallery.</div>
<div class='progress-msg'>[18:35:46] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PECHUGAS-DE-POLLO.webp`</div>
<div class='progress-msg'>[18:35:46] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PECHUGAS-DE-POLLO.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:46] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PECHUGAS-DE-POLLO.webp`</div>
<div class='progress-msg'>[18:35:46] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:46] - Attachment with ID: `19055` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PECHUGAS-DE-POLLO.webp`</div>
<div class='progress-msg'>[18:35:46] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:46] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:46] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:46] - Attempted to create child Producto product_cat `POLLO`, duplicate detected. Importing Producto to existing `POLLO` product_cat, ID 3099, slug `pollo` ...</div>
<div class='progress-msg'>[18:35:46] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:46] <b>CREATED</b> `PECHUGA` `Producto` (ID: 19054)</div>
<div class='progress-msg'>[18:35:46] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:46] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:46] ---</div>
<div class='progress-msg'>[18:35:46] Record #5</div>
<div class='progress-msg'>[18:35:46] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:46] Combine all data for post `PATITAS DE POLLO`...</div>
<div class='progress-msg'>[18:35:46] Find corresponding article among database for post `PATITAS DE POLLO`...</div>
<div class='progress-msg'>[18:35:46] Duplicate post wasn't found for post `PATITAS DE POLLO`...</div>
<div class='progress-msg'>[18:35:46] Applying filter `pmxi_article_data` for `PATITAS DE POLLO`</div>
<div class='progress-msg'>[18:35:46] <b>CREATING</b> `PATITAS DE POLLO` `Producto`</div>
<div class='progress-msg'>[18:35:46] Associate post `PATITAS DE POLLO` with current import ...</div>
<div class='progress-msg'>[18:35:46] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:46] Property `price` updated with value `1`</div>
<div class='progress-msg'>[18:35:46] Property `regular_price` updated with value `1`</div>
<div class='progress-msg'>[18:35:46] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:46] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:46] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `sku` updated with value `02055b1cc4de`</div>
<div class='progress-msg'>[18:35:46] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:46] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:46] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:46] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:46] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:46] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:46] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:46] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:46] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:46] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:46] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:46] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Patitas-de-Pollo.jpg` for `PATITAS DE POLLO` ...</div>
<div class='progress-msg'>[18:35:46] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Patitas-de-Pollo.jpg` by URL...</div>
<div class='progress-msg'>[18:35:46] - <b>WARNING</b>: Image Patitas-de-Pollo.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:35:46] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Patitas-de-Pollo.jpg`</div>
<div class='progress-msg'>[18:35:46] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Patitas-de-Pollo.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:46] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/Patitas-de-Pollo.jpg`</div>
<div class='progress-msg'>[18:35:47] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:47] - Attachment with ID: `19057` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/Patitas-de-Pollo.jpg`</div>
<div class='progress-msg'>[18:35:47] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:47] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:47] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:47] - Attempted to create child Producto product_cat `POLLO`, duplicate detected. Importing Producto to existing `POLLO` product_cat, ID 3099, slug `pollo` ...</div>
<div class='progress-msg'>[18:35:47] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:47] <b>CREATED</b> `PATITAS DE POLLO` `Producto` (ID: 19056)</div>
<div class='progress-msg'>[18:35:47] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:47] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:47] ---</div>
<div class='progress-msg'>[18:35:47] Record #6</div>
<div class='progress-msg'>[18:35:47] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:47] Combine all data for post `HIGADO DE POLLO`...</div>
<div class='progress-msg'>[18:35:47] Find corresponding article among database for post `HIGADO DE POLLO`...</div>
<div class='progress-msg'>[18:35:47] Duplicate post wasn't found for post `HIGADO DE POLLO`...</div>
<div class='progress-msg'>[18:35:47] Applying filter `pmxi_article_data` for `HIGADO DE POLLO`</div>
<div class='progress-msg'>[18:35:47] <b>CREATING</b> `HIGADO DE POLLO` `Producto`</div>
<div class='progress-msg'>[18:35:47] Associate post `HIGADO DE POLLO` with current import ...</div>
<div class='progress-msg'>[18:35:47] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:47] Property `price` updated with value `1.1`</div>
<div class='progress-msg'>[18:35:47] Property `regular_price` updated with value `1.1`</div>
<div class='progress-msg'>[18:35:47] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:47] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:47] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `sku` updated with value `4d0c4944b2d7`</div>
<div class='progress-msg'>[18:35:47] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:47] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:47] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:47] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:47] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:47] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:47] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:47] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:47] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:47] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:47] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:47] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Higados-de-Pollo.jpg` for `HIGADO DE POLLO` ...</div>
<div class='progress-msg'>[18:35:47] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Higados-de-Pollo.jpg` by URL...</div>
<div class='progress-msg'>[18:35:47] - <b>WARNING</b>: Image Higados-de-Pollo.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:35:47] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Higados-de-Pollo.jpg`</div>
<div class='progress-msg'>[18:35:47] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Higados-de-Pollo.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:47] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/Higados-de-Pollo.jpg`</div>
<div class='progress-msg'>[18:35:47] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:47] - Attachment with ID: `19059` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/Higados-de-Pollo.jpg`</div>
<div class='progress-msg'>[18:35:47] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:47] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:47] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:47] - Attempted to create child Producto product_cat `POLLO`, duplicate detected. Importing Producto to existing `POLLO` product_cat, ID 3099, slug `pollo` ...</div>
<div class='progress-msg'>[18:35:47] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:47] <b>CREATED</b> `HIGADO DE POLLO` `Producto` (ID: 19058)</div>
<div class='progress-msg'>[18:35:47] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:47] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:47] ---</div>
<div class='progress-msg'>[18:35:47] Record #7</div>
<div class='progress-msg'>[18:35:47] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:47] Combine all data for post `PESCUEZO`...</div>
<div class='progress-msg'>[18:35:47] Find corresponding article among database for post `PESCUEZO`...</div>
<div class='progress-msg'>[18:35:47] Duplicate post wasn't found for post `PESCUEZO`...</div>
<div class='progress-msg'>[18:35:47] Applying filter `pmxi_article_data` for `PESCUEZO`</div>
<div class='progress-msg'>[18:35:47] <b>CREATING</b> `PESCUEZO` `Producto`</div>
<div class='progress-msg'>[18:35:48] Associate post `PESCUEZO` with current import ...</div>
<div class='progress-msg'>[18:35:48] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:48] Property `price` updated with value `1`</div>
<div class='progress-msg'>[18:35:48] Property `regular_price` updated with value `1`</div>
<div class='progress-msg'>[18:35:48] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:48] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:48] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `sku` updated with value `0b7277f8738d`</div>
<div class='progress-msg'>[18:35:48] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:48] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:48] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:48] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:48] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:48] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:48] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:48] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:48] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:48] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:48] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Pescuezo-de-Pollo.jpg` for `PESCUEZO` ...</div>
<div class='progress-msg'>[18:35:48] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Pescuezo-de-Pollo.jpg` by URL...</div>
<div class='progress-msg'>[18:35:48] - <b>WARNING</b>: Image Pescuezo-de-Pollo.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:35:48] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Pescuezo-de-Pollo.jpg`</div>
<div class='progress-msg'>[18:35:48] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Pescuezo-de-Pollo.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:48] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/Pescuezo-de-Pollo.jpg`</div>
<div class='progress-msg'>[18:35:48] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:48] - Attachment with ID: `19061` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/Pescuezo-de-Pollo.jpg`</div>
<div class='progress-msg'>[18:35:48] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:48] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:48] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:48] - Attempted to create child Producto product_cat `POLLO`, duplicate detected. Importing Producto to existing `POLLO` product_cat, ID 3099, slug `pollo` ...</div>
<div class='progress-msg'>[18:35:48] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:48] <b>CREATED</b> `PESCUEZO` `Producto` (ID: 19060)</div>
<div class='progress-msg'>[18:35:48] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:48] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:48] ---</div>
<div class='progress-msg'>[18:35:48] Record #8</div>
<div class='progress-msg'>[18:35:48] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:48] Combine all data for post `JARRETE`...</div>
<div class='progress-msg'>[18:35:48] Find corresponding article among database for post `JARRETE`...</div>
<div class='progress-msg'>[18:35:48] Duplicate post wasn't found for post `JARRETE`...</div>
<div class='progress-msg'>[18:35:48] Applying filter `pmxi_article_data` for `JARRETE`</div>
<div class='progress-msg'>[18:35:48] <b>CREATING</b> `JARRETE` `Producto`</div>
<div class='progress-msg'>[18:35:48] Associate post `JARRETE` with current import ...</div>
<div class='progress-msg'>[18:35:48] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:48] Property `price` updated with value `2.85`</div>
<div class='progress-msg'>[18:35:48] Property `regular_price` updated with value `2.85`</div>
<div class='progress-msg'>[18:35:48] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:48] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:48] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `sku` updated with value `d15f66caf16b`</div>
<div class='progress-msg'>[18:35:48] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:48] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:48] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:48] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:48] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:48] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:48] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:48] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:48] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:48] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:49] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:49] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Jarrete-de-Res.jpg` for `JARRETE` ...</div>
<div class='progress-msg'>[18:35:49] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Jarrete-de-Res.jpg` by URL...</div>
<div class='progress-msg'>[18:35:49] - <b>WARNING</b>: Image Jarrete-de-Res.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:35:49] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Jarrete-de-Res.jpg`</div>
<div class='progress-msg'>[18:35:49] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/Jarrete-de-Res.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:49] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/Jarrete-de-Res.jpg`</div>
<div class='progress-msg'>[18:35:49] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:49] - Attachment with ID: `19063` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/Jarrete-de-Res.jpg`</div>
<div class='progress-msg'>[18:35:49] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:49] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:49] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:49] - Creating child Producto product_cat for CARNES Y AVES named `RES` ...</div>
<div class='progress-msg'>[18:35:49] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:49] <b>CREATED</b> `JARRETE` `Producto` (ID: 19062)</div>
<div class='progress-msg'>[18:35:49] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:49] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:49] ---</div>
<div class='progress-msg'>[18:35:49] Record #9</div>
<div class='progress-msg'>[18:35:49] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:49] Combine all data for post `FALDA GRUESA`...</div>
<div class='progress-msg'>[18:35:49] Find corresponding article among database for post `FALDA GRUESA`...</div>
<div class='progress-msg'>[18:35:49] Duplicate post wasn't found for post `FALDA GRUESA`...</div>
<div class='progress-msg'>[18:35:49] Applying filter `pmxi_article_data` for `FALDA GRUESA`</div>
<div class='progress-msg'>[18:35:49] <b>CREATING</b> `FALDA GRUESA` `Producto`</div>
<div class='progress-msg'>[18:35:49] Associate post `FALDA GRUESA` with current import ...</div>
<div class='progress-msg'>[18:35:49] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:49] Property `price` updated with value `3.15`</div>
<div class='progress-msg'>[18:35:49] Property `regular_price` updated with value `3.15`</div>
<div class='progress-msg'>[18:35:49] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:49] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:49] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `sku` updated with value `f5cc4014e857`</div>
<div class='progress-msg'>[18:35:49] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:49] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:49] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:49] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:49] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:49] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:49] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:49] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:49] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:49] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:49] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:49] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/FALDA-GRUESA-DE-RES.webp` for `FALDA GRUESA` ...</div>
<div class='progress-msg'>[18:35:49] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/FALDA-GRUESA-DE-RES.webp` by URL...</div>
<div class='progress-msg'>[18:35:49] - <b>WARNING</b>: Image FALDA-GRUESA-DE-RES.webp not found in media gallery.</div>
<div class='progress-msg'>[18:35:49] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/FALDA-GRUESA-DE-RES.webp`</div>
<div class='progress-msg'>[18:35:49] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/FALDA-GRUESA-DE-RES.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:49] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/FALDA-GRUESA-DE-RES.webp`</div>
<div class='progress-msg'>[18:35:50] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:50] - Attachment with ID: `19065` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/FALDA-GRUESA-DE-RES.webp`</div>
<div class='progress-msg'>[18:35:50] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:50] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:50] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:50] - Attempted to create child Producto product_cat `RES`, duplicate detected. Importing Producto to existing `RES` product_cat, ID 3100, slug `res` ...</div>
<div class='progress-msg'>[18:35:50] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:50] <b>CREATED</b> `FALDA GRUESA` `Producto` (ID: 19064)</div>
<div class='progress-msg'>[18:35:50] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:50] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:50] ---</div>
<div class='progress-msg'>[18:35:50] Record #10</div>
<div class='progress-msg'>[18:35:50] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:50] Combine all data for post `ROPA VIEJA`...</div>
<div class='progress-msg'>[18:35:50] Find corresponding article among database for post `ROPA VIEJA`...</div>
<div class='progress-msg'>[18:35:50] Duplicate post wasn't found for post `ROPA VIEJA`...</div>
<div class='progress-msg'>[18:35:50] Applying filter `pmxi_article_data` for `ROPA VIEJA`</div>
<div class='progress-msg'>[18:35:50] <b>CREATING</b> `ROPA VIEJA` `Producto`</div>
<div class='progress-msg'>[18:35:50] Associate post `ROPA VIEJA` with current import ...</div>
<div class='progress-msg'>[18:35:50] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:50] Property `price` updated with value `3.25`</div>
<div class='progress-msg'>[18:35:50] Property `regular_price` updated with value `3.25`</div>
<div class='progress-msg'>[18:35:50] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:50] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:50] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `sku` updated with value `72e094cce344`</div>
<div class='progress-msg'>[18:35:50] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:50] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:50] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:50] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:50] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:50] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:50] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:50] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:50] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:50] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:50] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:50] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ROPA-VIEJA-O-CARNE-MECHADA.jpg` for `ROPA VIEJA` ...</div>
<div class='progress-msg'>[18:35:50] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ROPA-VIEJA-O-CARNE-MECHADA.jpg` by URL...</div>
<div class='progress-msg'>[18:35:50] - <b>WARNING</b>: Image ROPA-VIEJA-O-CARNE-MECHADA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:35:50] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ROPA-VIEJA-O-CARNE-MECHADA.jpg`</div>
<div class='progress-msg'>[18:35:50] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ROPA-VIEJA-O-CARNE-MECHADA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:50] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/ROPA-VIEJA-O-CARNE-MECHADA.jpg`</div>
<div class='progress-msg'>[18:35:50] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:50] - Attachment with ID: `19067` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/ROPA-VIEJA-O-CARNE-MECHADA.jpg`</div>
<div class='progress-msg'>[18:35:50] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:50] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:50] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:50] - Attempted to create child Producto product_cat `RES`, duplicate detected. Importing Producto to existing `RES` product_cat, ID 3100, slug `res` ...</div>
<div class='progress-msg'>[18:35:50] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:50] <b>CREATED</b> `ROPA VIEJA` `Producto` (ID: 19066)</div>
<div class='progress-msg'>[18:35:51] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:51] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:51] ---</div>
<div class='progress-msg'>[18:35:51] Record #11</div>
<div class='progress-msg'>[18:35:51] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:51] Combine all data for post `CARNE MOLIDA ESPECIAL`...</div>
<div class='progress-msg'>[18:35:51] Find corresponding article among database for post `CARNE MOLIDA ESPECIAL`...</div>
<div class='progress-msg'>[18:35:51] Duplicate post wasn't found for post `CARNE MOLIDA ESPECIAL`...</div>
<div class='progress-msg'>[18:35:51] Applying filter `pmxi_article_data` for `CARNE MOLIDA ESPECIAL`</div>
<div class='progress-msg'>[18:35:51] <b>CREATING</b> `CARNE MOLIDA ESPECIAL` `Producto`</div>
<div class='progress-msg'>[18:35:51] Associate post `CARNE MOLIDA ESPECIAL` with current import ...</div>
<div class='progress-msg'>[18:35:51] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:51] Property `price` updated with value `3.5`</div>
<div class='progress-msg'>[18:35:51] Property `regular_price` updated with value `3.5`</div>
<div class='progress-msg'>[18:35:51] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:51] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:51] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `sku` updated with value `4af37367d143`</div>
<div class='progress-msg'>[18:35:51] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:51] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:51] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:51] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:51] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:51] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:51] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:51] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:51] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:51] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:51] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CARNE-MOLIDA-ESPECIAL.png` for `CARNE MOLIDA ESPECIAL` ...</div>
<div class='progress-msg'>[18:35:51] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CARNE-MOLIDA-ESPECIAL.png` by URL...</div>
<div class='progress-msg'>[18:35:51] - <b>WARNING</b>: Image CARNE-MOLIDA-ESPECIAL.png not found in media gallery.</div>
<div class='progress-msg'>[18:35:51] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CARNE-MOLIDA-ESPECIAL.png`</div>
<div class='progress-msg'>[18:35:51] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CARNE-MOLIDA-ESPECIAL.png` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:51] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CARNE-MOLIDA-ESPECIAL.png`</div>
<div class='progress-msg'>[18:35:51] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:51] - Attachment with ID: `19069` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CARNE-MOLIDA-ESPECIAL.png`</div>
<div class='progress-msg'>[18:35:51] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:51] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:51] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:51] - Attempted to create child Producto product_cat `RES`, duplicate detected. Importing Producto to existing `RES` product_cat, ID 3100, slug `res` ...</div>
<div class='progress-msg'>[18:35:51] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:51] <b>CREATED</b> `CARNE MOLIDA ESPECIAL` `Producto` (ID: 19068)</div>
<div class='progress-msg'>[18:35:51] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:51] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:51] ---</div>
<div class='progress-msg'>[18:35:51] Record #12</div>
<div class='progress-msg'>[18:35:51] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:51] Combine all data for post `PULPA NEGRA`...</div>
<div class='progress-msg'>[18:35:51] Find corresponding article among database for post `PULPA NEGRA`...</div>
<div class='progress-msg'>[18:35:51] Duplicate post wasn't found for post `PULPA NEGRA`...</div>
<div class='progress-msg'>[18:35:51] Applying filter `pmxi_article_data` for `PULPA NEGRA`</div>
<div class='progress-msg'>[18:35:51] <b>CREATING</b> `PULPA NEGRA` `Producto`</div>
<div class='progress-msg'>[18:35:51] Associate post `PULPA NEGRA` with current import ...</div>
<div class='progress-msg'>[18:35:51] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:51] Property `price` updated with value `3.85`</div>
<div class='progress-msg'>[18:35:51] Property `regular_price` updated with value `3.85`</div>
<div class='progress-msg'>[18:35:51] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:51] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:51] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `sku` updated with value `297b66fb8d80`</div>
<div class='progress-msg'>[18:35:51] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:51] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:51] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:51] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:51] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:51] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:51] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:51] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:51] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:51] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:52] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:52] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PULPA-NEGRA.webp` for `PULPA NEGRA` ...</div>
<div class='progress-msg'>[18:35:52] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PULPA-NEGRA.webp` by URL...</div>
<div class='progress-msg'>[18:35:52] - <b>WARNING</b>: Image PULPA-NEGRA.webp not found in media gallery.</div>
<div class='progress-msg'>[18:35:52] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PULPA-NEGRA.webp`</div>
<div class='progress-msg'>[18:35:52] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PULPA-NEGRA.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:52] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PULPA-NEGRA.webp`</div>
<div class='progress-msg'>[18:35:52] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:52] - Attachment with ID: `19071` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PULPA-NEGRA.webp`</div>
<div class='progress-msg'>[18:35:52] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:52] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:52] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:52] - Attempted to create child Producto product_cat `RES`, duplicate detected. Importing Producto to existing `RES` product_cat, ID 3100, slug `res` ...</div>
<div class='progress-msg'>[18:35:52] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:52] <b>CREATED</b> `PULPA NEGRA` `Producto` (ID: 19070)</div>
<div class='progress-msg'>[18:35:52] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:52] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:52] ---</div>
<div class='progress-msg'>[18:35:52] Record #13</div>
<div class='progress-msg'>[18:35:52] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:52] Combine all data for post `PUNTA PALOMILLA`...</div>
<div class='progress-msg'>[18:35:52] Find corresponding article among database for post `PUNTA PALOMILLA`...</div>
<div class='progress-msg'>[18:35:52] Duplicate post wasn't found for post `PUNTA PALOMILLA`...</div>
<div class='progress-msg'>[18:35:52] Applying filter `pmxi_article_data` for `PUNTA PALOMILLA`</div>
<div class='progress-msg'>[18:35:52] <b>CREATING</b> `PUNTA PALOMILLA` `Producto`</div>
<div class='progress-msg'>[18:35:52] Associate post `PUNTA PALOMILLA` with current import ...</div>
<div class='progress-msg'>[18:35:52] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:52] Property `price` updated with value `3.85`</div>
<div class='progress-msg'>[18:35:52] Property `regular_price` updated with value `3.85`</div>
<div class='progress-msg'>[18:35:52] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:52] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:52] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `sku` updated with value `63507cb32ffc`</div>
<div class='progress-msg'>[18:35:52] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:52] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:52] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:52] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:52] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:52] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:52] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:52] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:52] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:52] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:53] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:53] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PUNTA-PALOMILLA.png` for `PUNTA PALOMILLA` ...</div>
<div class='progress-msg'>[18:35:53] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PUNTA-PALOMILLA.png` by URL...</div>
<div class='progress-msg'>[18:35:53] - <b>WARNING</b>: Image PUNTA-PALOMILLA.png not found in media gallery.</div>
<div class='progress-msg'>[18:35:53] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PUNTA-PALOMILLA.png`</div>
<div class='progress-msg'>[18:35:53] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PUNTA-PALOMILLA.png` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:53] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PUNTA-PALOMILLA.png`</div>
<div class='progress-msg'>[18:35:53] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:53] - Attachment with ID: `19073` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PUNTA-PALOMILLA.png`</div>
<div class='progress-msg'>[18:35:53] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:53] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:53] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:53] - Attempted to create child Producto product_cat `RES`, duplicate detected. Importing Producto to existing `RES` product_cat, ID 3100, slug `res` ...</div>
<div class='progress-msg'>[18:35:53] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:53] <b>CREATED</b> `PUNTA PALOMILLA` `Producto` (ID: 19072)</div>
<div class='progress-msg'>[18:35:53] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:53] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:53] ---</div>
<div class='progress-msg'>[18:35:53] Record #14</div>
<div class='progress-msg'>[18:35:53] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:53] Combine all data for post `PULPA BLANCA`...</div>
<div class='progress-msg'>[18:35:53] Find corresponding article among database for post `PULPA BLANCA`...</div>
<div class='progress-msg'>[18:35:53] Duplicate post wasn't found for post `PULPA BLANCA`...</div>
<div class='progress-msg'>[18:35:53] Applying filter `pmxi_article_data` for `PULPA BLANCA`</div>
<div class='progress-msg'>[18:35:53] <b>CREATING</b> `PULPA BLANCA` `Producto`</div>
<div class='progress-msg'>[18:35:53] Associate post `PULPA BLANCA` with current import ...</div>
<div class='progress-msg'>[18:35:53] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:53] Property `price` updated with value `3.85`</div>
<div class='progress-msg'>[18:35:53] Property `regular_price` updated with value `3.85`</div>
<div class='progress-msg'>[18:35:53] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:53] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:53] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `sku` updated with value `0d5d90a5a2e6`</div>
<div class='progress-msg'>[18:35:53] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:53] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:53] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:53] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:53] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:53] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:53] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:53] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:53] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:53] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:53] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:53] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PULPA-BLANCA.jpg` for `PULPA BLANCA` ...</div>
<div class='progress-msg'>[18:35:53] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PULPA-BLANCA.jpg` by URL...</div>
<div class='progress-msg'>[18:35:53] - <b>WARNING</b>: Image PULPA-BLANCA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:35:53] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PULPA-BLANCA.jpg`</div>
<div class='progress-msg'>[18:35:53] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PULPA-BLANCA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:53] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PULPA-BLANCA.jpg`</div>
<div class='progress-msg'>[18:35:54] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:54] - Attachment with ID: `19075` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PULPA-BLANCA.jpg`</div>
<div class='progress-msg'>[18:35:54] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:54] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:54] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:54] - Attempted to create child Producto product_cat `RES`, duplicate detected. Importing Producto to existing `RES` product_cat, ID 3100, slug `res` ...</div>
<div class='progress-msg'>[18:35:54] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:54] <b>CREATED</b> `PULPA BLANCA` `Producto` (ID: 19074)</div>
<div class='progress-msg'>[18:35:54] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:54] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:54] ---</div>
<div class='progress-msg'>[18:35:54] Record #15</div>
<div class='progress-msg'>[18:35:54] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:54] Combine all data for post `HUESO DE RES`...</div>
<div class='progress-msg'>[18:35:54] Find corresponding article among database for post `HUESO DE RES`...</div>
<div class='progress-msg'>[18:35:54] Duplicate post wasn't found for post `HUESO DE RES`...</div>
<div class='progress-msg'>[18:35:54] Applying filter `pmxi_article_data` for `HUESO DE RES`</div>
<div class='progress-msg'>[18:35:54] <b>CREATING</b> `HUESO DE RES` `Producto`</div>
<div class='progress-msg'>[18:35:54] Associate post `HUESO DE RES` with current import ...</div>
<div class='progress-msg'>[18:35:54] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:54] Property `price` updated with value `1.35`</div>
<div class='progress-msg'>[18:35:54] Property `regular_price` updated with value `1.35`</div>
<div class='progress-msg'>[18:35:54] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:54] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:54] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `sku` updated with value `babb9aed70ab`</div>
<div class='progress-msg'>[18:35:54] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:54] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:54] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:54] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:54] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:54] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:54] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:54] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:54] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:54] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:54] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:54] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/HUESOS-ROJOS-DE-RES.png` for `HUESO DE RES` ...</div>
<div class='progress-msg'>[18:35:54] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/HUESOS-ROJOS-DE-RES.png` by URL...</div>
<div class='progress-msg'>[18:35:54] - <b>WARNING</b>: Image HUESOS-ROJOS-DE-RES.png not found in media gallery.</div>
<div class='progress-msg'>[18:35:54] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/HUESOS-ROJOS-DE-RES.png`</div>
<div class='progress-msg'>[18:35:54] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/HUESOS-ROJOS-DE-RES.png` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:54] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/HUESOS-ROJOS-DE-RES.png`</div>
<div class='progress-msg'>[18:35:54] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:54] - Attachment with ID: `19077` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/HUESOS-ROJOS-DE-RES.png`</div>
<div class='progress-msg'>[18:35:54] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:54] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:54] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:54] - Attempted to create child Producto product_cat `RES`, duplicate detected. Importing Producto to existing `RES` product_cat, ID 3100, slug `res` ...</div>
<div class='progress-msg'>[18:35:54] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:54] <b>CREATED</b> `HUESO DE RES` `Producto` (ID: 19076)</div>
<div class='progress-msg'>[18:35:54] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:54] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:54] ---</div>
<div class='progress-msg'>[18:35:54] Record #16</div>
<div class='progress-msg'>[18:35:54] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:54] Combine all data for post `COSTILLA DE RES`...</div>
<div class='progress-msg'>[18:35:54] Find corresponding article among database for post `COSTILLA DE RES`...</div>
<div class='progress-msg'>[18:35:54] Duplicate post wasn't found for post `COSTILLA DE RES`...</div>
<div class='progress-msg'>[18:35:54] Applying filter `pmxi_article_data` for `COSTILLA DE RES`</div>
<div class='progress-msg'>[18:35:54] <b>CREATING</b> `COSTILLA DE RES` `Producto`</div>
<div class='progress-msg'>[18:35:54] Associate post `COSTILLA DE RES` with current import ...</div>
<div class='progress-msg'>[18:35:54] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:55] Property `price` updated with value `2.45`</div>
<div class='progress-msg'>[18:35:55] Property `regular_price` updated with value `2.45`</div>
<div class='progress-msg'>[18:35:55] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:55] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:55] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `sku` updated with value `dc307cb6b968`</div>
<div class='progress-msg'>[18:35:55] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:55] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:55] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:55] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:55] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:55] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:55] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:55] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:55] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:55] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:55] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/COSTILLA-DE-RES.png` for `COSTILLA DE RES` ...</div>
<div class='progress-msg'>[18:35:55] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/COSTILLA-DE-RES.png` by URL...</div>
<div class='progress-msg'>[18:35:55] - <b>WARNING</b>: Image COSTILLA-DE-RES.png not found in media gallery.</div>
<div class='progress-msg'>[18:35:55] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/COSTILLA-DE-RES.png`</div>
<div class='progress-msg'>[18:35:55] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/COSTILLA-DE-RES.png` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:55] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/COSTILLA-DE-RES.png`</div>
<div class='progress-msg'>[18:35:55] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:55] - Attachment with ID: `19079` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/COSTILLA-DE-RES.png`</div>
<div class='progress-msg'>[18:35:55] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:55] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:55] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:55] - Attempted to create child Producto product_cat `RES`, duplicate detected. Importing Producto to existing `RES` product_cat, ID 3100, slug `res` ...</div>
<div class='progress-msg'>[18:35:55] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:55] <b>CREATED</b> `COSTILLA DE RES` `Producto` (ID: 19078)</div>
<div class='progress-msg'>[18:35:55] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:55] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:55] ---</div>
<div class='progress-msg'>[18:35:55] Record #17</div>
<div class='progress-msg'>[18:35:55] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:55] Combine all data for post `BISTEC DE CINTA`...</div>
<div class='progress-msg'>[18:35:55] Find corresponding article among database for post `BISTEC DE CINTA`...</div>
<div class='progress-msg'>[18:35:55] Duplicate post wasn't found for post `BISTEC DE CINTA`...</div>
<div class='progress-msg'>[18:35:55] Applying filter `pmxi_article_data` for `BISTEC DE CINTA`</div>
<div class='progress-msg'>[18:35:55] <b>CREATING</b> `BISTEC DE CINTA` `Producto`</div>
<div class='progress-msg'>[18:35:55] Associate post `BISTEC DE CINTA` with current import ...</div>
<div class='progress-msg'>[18:35:55] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:55] Property `price` updated with value `2.55`</div>
<div class='progress-msg'>[18:35:55] Property `regular_price` updated with value `2.55`</div>
<div class='progress-msg'>[18:35:55] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:55] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:55] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `sku` updated with value `c8375c99387e`</div>
<div class='progress-msg'>[18:35:55] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:55] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:55] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:55] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:55] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:55] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:55] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:55] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:55] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:55] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:56] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:56] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/BISTEC-DE-CINTA.webp` for `BISTEC DE CINTA` ...</div>
<div class='progress-msg'>[18:35:56] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/BISTEC-DE-CINTA.webp` by URL...</div>
<div class='progress-msg'>[18:35:56] - <b>WARNING</b>: Image BISTEC-DE-CINTA.webp not found in media gallery.</div>
<div class='progress-msg'>[18:35:56] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/BISTEC-DE-CINTA.webp`</div>
<div class='progress-msg'>[18:35:56] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/BISTEC-DE-CINTA.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:56] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/BISTEC-DE-CINTA.webp`</div>
<div class='progress-msg'>[18:35:56] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:56] - Attachment with ID: `19081` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/BISTEC-DE-CINTA.webp`</div>
<div class='progress-msg'>[18:35:56] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:56] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:56] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:56] - Attempted to create child Producto product_cat `RES`, duplicate detected. Importing Producto to existing `RES` product_cat, ID 3100, slug `res` ...</div>
<div class='progress-msg'>[18:35:56] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:56] <b>CREATED</b> `BISTEC DE CINTA` `Producto` (ID: 19080)</div>
<div class='progress-msg'>[18:35:56] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:56] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:56] ---</div>
<div class='progress-msg'>[18:35:56] Record #18</div>
<div class='progress-msg'>[18:35:56] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:56] Combine all data for post `HIGADO DE RES`...</div>
<div class='progress-msg'>[18:35:56] Find corresponding article among database for post `HIGADO DE RES`...</div>
<div class='progress-msg'>[18:35:56] Duplicate post wasn't found for post `HIGADO DE RES`...</div>
<div class='progress-msg'>[18:35:56] Applying filter `pmxi_article_data` for `HIGADO DE RES`</div>
<div class='progress-msg'>[18:35:56] <b>CREATING</b> `HIGADO DE RES` `Producto`</div>
<div class='progress-msg'>[18:35:56] Associate post `HIGADO DE RES` with current import ...</div>
<div class='progress-msg'>[18:35:56] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:56] Property `price` updated with value `1.9`</div>
<div class='progress-msg'>[18:35:56] Property `regular_price` updated with value `1.9`</div>
<div class='progress-msg'>[18:35:56] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:56] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:56] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `sku` updated with value `0a03c257f9e9`</div>
<div class='progress-msg'>[18:35:56] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:56] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:56] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:56] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:56] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:56] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:56] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:56] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:56] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:56] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:57] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:57] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/HIGADO-DE-RES.png` for `HIGADO DE RES` ...</div>
<div class='progress-msg'>[18:35:57] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/HIGADO-DE-RES.png` by URL...</div>
<div class='progress-msg'>[18:35:57] - <b>WARNING</b>: Image HIGADO-DE-RES.png not found in media gallery.</div>
<div class='progress-msg'>[18:35:57] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/HIGADO-DE-RES.png`</div>
<div class='progress-msg'>[18:35:57] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/HIGADO-DE-RES.png` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:57] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/HIGADO-DE-RES.png`</div>
<div class='progress-msg'>[18:35:57] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:57] - Attachment with ID: `19083` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/HIGADO-DE-RES.png`</div>
<div class='progress-msg'>[18:35:57] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:57] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:57] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:57] - Attempted to create child Producto product_cat `RES`, duplicate detected. Importing Producto to existing `RES` product_cat, ID 3100, slug `res` ...</div>
<div class='progress-msg'>[18:35:57] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:57] <b>CREATED</b> `HIGADO DE RES` `Producto` (ID: 19082)</div>
<div class='progress-msg'>[18:35:57] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:57] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:57] ---</div>
<div class='progress-msg'>[18:35:57] Record #19</div>
<div class='progress-msg'>[18:35:57] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:57] Combine all data for post `CHULETA AHUMADA`...</div>
<div class='progress-msg'>[18:35:57] Find corresponding article among database for post `CHULETA AHUMADA`...</div>
<div class='progress-msg'>[18:35:57] Duplicate post wasn't found for post `CHULETA AHUMADA`...</div>
<div class='progress-msg'>[18:35:57] Applying filter `pmxi_article_data` for `CHULETA AHUMADA`</div>
<div class='progress-msg'>[18:35:57] <b>CREATING</b> `CHULETA AHUMADA` `Producto`</div>
<div class='progress-msg'>[18:35:57] Associate post `CHULETA AHUMADA` with current import ...</div>
<div class='progress-msg'>[18:35:57] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:57] Property `price` updated with value `3.45`</div>
<div class='progress-msg'>[18:35:57] Property `regular_price` updated with value `3.45`</div>
<div class='progress-msg'>[18:35:57] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:57] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:57] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `sku` updated with value `97330b92efb5`</div>
<div class='progress-msg'>[18:35:57] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:57] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:57] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:57] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:57] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:57] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:57] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:57] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:57] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:57] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:57] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:57] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CHULETA-AHUMADA.jpg` for `CHULETA AHUMADA` ...</div>
<div class='progress-msg'>[18:35:57] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CHULETA-AHUMADA.jpg` by URL...</div>
<div class='progress-msg'>[18:35:57] - <b>WARNING</b>: Image CHULETA-AHUMADA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:35:57] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CHULETA-AHUMADA.jpg`</div>
<div class='progress-msg'>[18:35:57] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CHULETA-AHUMADA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:57] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CHULETA-AHUMADA.jpg`</div>
<div class='progress-msg'>[18:35:57] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:57] - Attachment with ID: `19085` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CHULETA-AHUMADA.jpg`</div>
<div class='progress-msg'>[18:35:57] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:57] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:57] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:58] - Creating child Producto product_cat for CARNES Y AVES named `CERDO` ...</div>
<div class='progress-msg'>[18:35:58] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:58] <b>CREATED</b> `CHULETA AHUMADA` `Producto` (ID: 19084)</div>
<div class='progress-msg'>[18:35:58] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:58] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:58] ---</div>
<div class='progress-msg'>[18:35:58] Record #20</div>
<div class='progress-msg'>[18:35:58] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:58] Combine all data for post `CHULETA FRESCA`...</div>
<div class='progress-msg'>[18:35:58] Find corresponding article among database for post `CHULETA FRESCA`...</div>
<div class='progress-msg'>[18:35:58] Duplicate post wasn't found for post `CHULETA FRESCA`...</div>
<div class='progress-msg'>[18:35:58] Applying filter `pmxi_article_data` for `CHULETA FRESCA`</div>
<div class='progress-msg'>[18:35:58] <b>CREATING</b> `CHULETA FRESCA` `Producto`</div>
<div class='progress-msg'>[18:35:58] Associate post `CHULETA FRESCA` with current import ...</div>
<div class='progress-msg'>[18:35:58] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:58] Property `price` updated with value `2.75`</div>
<div class='progress-msg'>[18:35:58] Property `regular_price` updated with value `2.75`</div>
<div class='progress-msg'>[18:35:58] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:58] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:58] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `sku` updated with value `e9f05200e14a`</div>
<div class='progress-msg'>[18:35:58] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:58] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:58] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:58] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:58] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:58] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:58] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:58] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:58] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:58] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:35:58] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:35:58] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CHULETA-FRESCA.jpg` for `CHULETA FRESCA` ...</div>
<div class='progress-msg'>[18:35:58] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CHULETA-FRESCA.jpg` by URL...</div>
<div class='progress-msg'>[18:35:58] - <b>WARNING</b>: Image CHULETA-FRESCA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:35:58] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CHULETA-FRESCA.jpg`</div>
<div class='progress-msg'>[18:35:58] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CHULETA-FRESCA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:35:58] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CHULETA-FRESCA.jpg`</div>
<div class='progress-msg'>[18:35:58] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:35:58] - Attachment with ID: `19087` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CHULETA-FRESCA.jpg`</div>
<div class='progress-msg'>[18:35:58] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:35:58] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:35:58] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:35:58] - Attempted to create child Producto product_cat `CERDO`, duplicate detected. Importing Producto to existing `CERDO` product_cat, ID 3101, slug `cerdo` ...</div>
<div class='progress-msg'>[18:35:58] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:35:58] <b>CREATED</b> `CHULETA FRESCA` `Producto` (ID: 19086)</div>
<div class='progress-msg'>[18:35:58] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:35:58] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:35:59] ---</div>
<div class='progress-msg'>[18:35:59] Record #21</div>
<div class='progress-msg'>[18:35:59] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:35:59] Combine all data for post `COSTILLITA DE PUERCO`...</div>
<div class='progress-msg'>[18:35:59] Find corresponding article among database for post `COSTILLITA DE PUERCO`...</div>
<div class='progress-msg'>[18:35:59] Duplicate post wasn't found for post `COSTILLITA DE PUERCO`...</div>
<div class='progress-msg'>[18:35:59] Applying filter `pmxi_article_data` for `COSTILLITA DE PUERCO`</div>
<div class='progress-msg'>[18:35:59] <b>CREATING</b> `COSTILLITA DE PUERCO` `Producto`</div>
<div class='progress-msg'>[18:35:59] Associate post `COSTILLITA DE PUERCO` with current import ...</div>
<div class='progress-msg'>[18:35:59] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:35:59] Property `price` updated with value `3.25`</div>
<div class='progress-msg'>[18:35:59] Property `regular_price` updated with value `3.25`</div>
<div class='progress-msg'>[18:35:59] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:35:59] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:35:59] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `sku` updated with value `22a484bc24c2`</div>
<div class='progress-msg'>[18:35:59] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:35:59] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:35:59] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:35:59] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:35:59] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:35:59] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:59] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:59] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:35:59] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:35:59] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:00] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:00] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/COSTILLITA-DE-PUERCO.jpg` for `COSTILLITA DE PUERCO` ...</div>
<div class='progress-msg'>[18:36:00] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/COSTILLITA-DE-PUERCO.jpg` by URL...</div>
<div class='progress-msg'>[18:36:00] - <b>WARNING</b>: Image COSTILLITA-DE-PUERCO.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:00] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/COSTILLITA-DE-PUERCO.jpg`</div>
<div class='progress-msg'>[18:36:00] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/COSTILLITA-DE-PUERCO.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:00] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/COSTILLITA-DE-PUERCO.jpg`</div>
<div class='progress-msg'>[18:36:00] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:00] - Attachment with ID: `19089` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/COSTILLITA-DE-PUERCO.jpg`</div>
<div class='progress-msg'>[18:36:00] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:00] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:00] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:36:00] - Attempted to create child Producto product_cat `CERDO`, duplicate detected. Importing Producto to existing `CERDO` product_cat, ID 3101, slug `cerdo` ...</div>
<div class='progress-msg'>[18:36:00] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:00] <b>CREATED</b> `COSTILLITA DE PUERCO` `Producto` (ID: 19088)</div>
<div class='progress-msg'>[18:36:00] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:00] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:00] ---</div>
<div class='progress-msg'>[18:36:00] Record #22</div>
<div class='progress-msg'>[18:36:00] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:00] Combine all data for post `PUERCO LISO`...</div>
<div class='progress-msg'>[18:36:00] Find corresponding article among database for post `PUERCO LISO`...</div>
<div class='progress-msg'>[18:36:00] Duplicate post wasn't found for post `PUERCO LISO`...</div>
<div class='progress-msg'>[18:36:00] Applying filter `pmxi_article_data` for `PUERCO LISO`</div>
<div class='progress-msg'>[18:36:00] <b>CREATING</b> `PUERCO LISO` `Producto`</div>
<div class='progress-msg'>[18:36:00] Associate post `PUERCO LISO` with current import ...</div>
<div class='progress-msg'>[18:36:00] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:00] Property `price` updated with value `2.8`</div>
<div class='progress-msg'>[18:36:00] Property `regular_price` updated with value `2.8`</div>
<div class='progress-msg'>[18:36:00] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:00] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:00] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `sku` updated with value `8be141077ae8`</div>
<div class='progress-msg'>[18:36:00] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:00] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:00] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:00] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:00] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:00] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:00] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:00] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:00] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:00] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:00] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:00] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PUERCO-LISO.png` for `PUERCO LISO` ...</div>
<div class='progress-msg'>[18:36:00] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PUERCO-LISO.png` by URL...</div>
<div class='progress-msg'>[18:36:00] - <b>WARNING</b>: Image PUERCO-LISO.png not found in media gallery.</div>
<div class='progress-msg'>[18:36:00] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PUERCO-LISO.png`</div>
<div class='progress-msg'>[18:36:00] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PUERCO-LISO.png` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:00] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PUERCO-LISO.png`</div>
<div class='progress-msg'>[18:36:00] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:00] - Attachment with ID: `19091` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PUERCO-LISO.png`</div>
<div class='progress-msg'>[18:36:00] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:00] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:00] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:36:00] - Attempted to create child Producto product_cat `CERDO`, duplicate detected. Importing Producto to existing `CERDO` product_cat, ID 3101, slug `cerdo` ...</div>
<div class='progress-msg'>[18:36:01] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:01] <b>CREATED</b> `PUERCO LISO` `Producto` (ID: 19090)</div>
<div class='progress-msg'>[18:36:01] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:01] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:01] ---</div>
<div class='progress-msg'>[18:36:01] Record #23</div>
<div class='progress-msg'>[18:36:01] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:01] Combine all data for post `PAPADA`...</div>
<div class='progress-msg'>[18:36:01] Find corresponding article among database for post `PAPADA`...</div>
<div class='progress-msg'>[18:36:01] Duplicate post wasn't found for post `PAPADA`...</div>
<div class='progress-msg'>[18:36:01] Applying filter `pmxi_article_data` for `PAPADA`</div>
<div class='progress-msg'>[18:36:01] <b>CREATING</b> `PAPADA` `Producto`</div>
<div class='progress-msg'>[18:36:01] Associate post `PAPADA` with current import ...</div>
<div class='progress-msg'>[18:36:01] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:01] Property `price` updated with value `1.9`</div>
<div class='progress-msg'>[18:36:01] Property `regular_price` updated with value `1.9`</div>
<div class='progress-msg'>[18:36:01] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:01] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:01] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `sku` updated with value `4dcc81d9f857`</div>
<div class='progress-msg'>[18:36:01] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:01] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:01] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:01] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:01] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:01] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:01] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:01] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:01] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:01] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:01] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PAPADA-DE-CERDO.jpg` for `PAPADA` ...</div>
<div class='progress-msg'>[18:36:01] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PAPADA-DE-CERDO.jpg` by URL...</div>
<div class='progress-msg'>[18:36:01] - <b>WARNING</b>: Image PAPADA-DE-CERDO.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:01] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PAPADA-DE-CERDO.jpg`</div>
<div class='progress-msg'>[18:36:01] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PAPADA-DE-CERDO.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:01] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PAPADA-DE-CERDO.jpg`</div>
<div class='progress-msg'>[18:36:01] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:01] - Attachment with ID: `19093` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PAPADA-DE-CERDO.jpg`</div>
<div class='progress-msg'>[18:36:01] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:01] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:01] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:36:01] - Attempted to create child Producto product_cat `CERDO`, duplicate detected. Importing Producto to existing `CERDO` product_cat, ID 3101, slug `cerdo` ...</div>
<div class='progress-msg'>[18:36:01] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:01] <b>CREATED</b> `PAPADA` `Producto` (ID: 19092)</div>
<div class='progress-msg'>[18:36:01] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:01] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:01] ---</div>
<div class='progress-msg'>[18:36:01] Record #24</div>
<div class='progress-msg'>[18:36:01] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:01] Combine all data for post `TOCINO`...</div>
<div class='progress-msg'>[18:36:01] Find corresponding article among database for post `TOCINO`...</div>
<div class='progress-msg'>[18:36:01] Duplicate post wasn't found for post `TOCINO`...</div>
<div class='progress-msg'>[18:36:01] Applying filter `pmxi_article_data` for `TOCINO`</div>
<div class='progress-msg'>[18:36:01] <b>CREATING</b> `TOCINO` `Producto`</div>
<div class='progress-msg'>[18:36:01] Associate post `TOCINO` with current import ...</div>
<div class='progress-msg'>[18:36:01] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:01] Property `price` updated with value `1.25`</div>
<div class='progress-msg'>[18:36:01] Property `regular_price` updated with value `1.25`</div>
<div class='progress-msg'>[18:36:01] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:01] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:01] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `sku` updated with value `adefe22efc09`</div>
<div class='progress-msg'>[18:36:01] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:01] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:01] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:01] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:01] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:01] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:01] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:01] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:01] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:01] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:02] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:02] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TOCINO-DE-CERDO.webp` for `TOCINO` ...</div>
<div class='progress-msg'>[18:36:02] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TOCINO-DE-CERDO.webp` by URL...</div>
<div class='progress-msg'>[18:36:02] - <b>WARNING</b>: Image TOCINO-DE-CERDO.webp not found in media gallery.</div>
<div class='progress-msg'>[18:36:02] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TOCINO-DE-CERDO.webp`</div>
<div class='progress-msg'>[18:36:02] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TOCINO-DE-CERDO.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:02] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/TOCINO-DE-CERDO.webp`</div>
<div class='progress-msg'>[18:36:02] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:02] - Attachment with ID: `19095` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/TOCINO-DE-CERDO.webp`</div>
<div class='progress-msg'>[18:36:02] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:02] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:02] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:36:02] - Attempted to create child Producto product_cat `CERDO`, duplicate detected. Importing Producto to existing `CERDO` product_cat, ID 3101, slug `cerdo` ...</div>
<div class='progress-msg'>[18:36:02] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:02] <b>CREATED</b> `TOCINO` `Producto` (ID: 19094)</div>
<div class='progress-msg'>[18:36:02] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:02] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:02] ---</div>
<div class='progress-msg'>[18:36:02] Record #25</div>
<div class='progress-msg'>[18:36:02] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:02] Combine all data for post `PATITA DE PUERCO`...</div>
<div class='progress-msg'>[18:36:02] Find corresponding article among database for post `PATITA DE PUERCO`...</div>
<div class='progress-msg'>[18:36:02] Duplicate post wasn't found for post `PATITA DE PUERCO`...</div>
<div class='progress-msg'>[18:36:02] Applying filter `pmxi_article_data` for `PATITA DE PUERCO`</div>
<div class='progress-msg'>[18:36:02] <b>CREATING</b> `PATITA DE PUERCO` `Producto`</div>
<div class='progress-msg'>[18:36:02] Associate post `PATITA DE PUERCO` with current import ...</div>
<div class='progress-msg'>[18:36:02] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:02] Property `price` updated with value `1.5`</div>
<div class='progress-msg'>[18:36:02] Property `regular_price` updated with value `1.5`</div>
<div class='progress-msg'>[18:36:02] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:02] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:02] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `sku` updated with value `2ffad541bc9b`</div>
<div class='progress-msg'>[18:36:02] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:02] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:02] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:02] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:02] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:02] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:02] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:02] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:02] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:02] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:03] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:03] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PATITA-DE-PUERCO.webp` for `PATITA DE PUERCO` ...</div>
<div class='progress-msg'>[18:36:03] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PATITA-DE-PUERCO.webp` by URL...</div>
<div class='progress-msg'>[18:36:03] - <b>WARNING</b>: Image PATITA-DE-PUERCO.webp not found in media gallery.</div>
<div class='progress-msg'>[18:36:03] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PATITA-DE-PUERCO.webp`</div>
<div class='progress-msg'>[18:36:03] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PATITA-DE-PUERCO.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:03] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PATITA-DE-PUERCO.webp`</div>
<div class='progress-msg'>[18:36:03] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:03] - Attachment with ID: `19097` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PATITA-DE-PUERCO.webp`</div>
<div class='progress-msg'>[18:36:03] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:03] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:03] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:36:03] - Attempted to create child Producto product_cat `CERDO`, duplicate detected. Importing Producto to existing `CERDO` product_cat, ID 3101, slug `cerdo` ...</div>
<div class='progress-msg'>[18:36:03] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:03] <b>CREATED</b> `PATITA DE PUERCO` `Producto` (ID: 19096)</div>
<div class='progress-msg'>[18:36:03] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:03] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:03] ---</div>
<div class='progress-msg'>[18:36:03] Record #26</div>
<div class='progress-msg'>[18:36:03] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:03] Combine all data for post `CABEZA DE PUERCO SOLO LA MITAD`...</div>
<div class='progress-msg'>[18:36:03] Find corresponding article among database for post `CABEZA DE PUERCO SOLO LA MITAD`...</div>
<div class='progress-msg'>[18:36:03] Duplicate post wasn't found for post `CABEZA DE PUERCO SOLO LA MITAD`...</div>
<div class='progress-msg'>[18:36:03] Applying filter `pmxi_article_data` for `CABEZA DE PUERCO SOLO LA MITAD`</div>
<div class='progress-msg'>[18:36:03] <b>CREATING</b> `CABEZA DE PUERCO SOLO LA MITAD` `Producto`</div>
<div class='progress-msg'>[18:36:03] Associate post `CABEZA DE PUERCO SOLO LA MITAD` with current import ...</div>
<div class='progress-msg'>[18:36:03] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:03] Property `price` updated with value `1.75`</div>
<div class='progress-msg'>[18:36:03] Property `regular_price` updated with value `1.75`</div>
<div class='progress-msg'>[18:36:03] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:03] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:03] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `sku` updated with value `a3c6b706e330`</div>
<div class='progress-msg'>[18:36:03] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:03] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:03] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:03] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:03] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:03] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:03] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:03] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:03] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:03] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:03] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:03] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CABEZA-DE-PUERCO-SOLO-LA-MITAD.webp` for `CABEZA DE PUERCO SOLO LA MITAD` ...</div>
<div class='progress-msg'>[18:36:03] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CABEZA-DE-PUERCO-SOLO-LA-MITAD.webp` by URL...</div>
<div class='progress-msg'>[18:36:03] - <b>WARNING</b>: Image CABEZA-DE-PUERCO-SOLO-LA-MITAD.webp not found in media gallery.</div>
<div class='progress-msg'>[18:36:03] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CABEZA-DE-PUERCO-SOLO-LA-MITAD.webp`</div>
<div class='progress-msg'>[18:36:03] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CABEZA-DE-PUERCO-SOLO-LA-MITAD.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:03] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CABEZA-DE-PUERCO-SOLO-LA-MITAD.webp`</div>
<div class='progress-msg'>[18:36:04] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:04] - Attachment with ID: `19099` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CABEZA-DE-PUERCO-SOLO-LA-MITAD.webp`</div>
<div class='progress-msg'>[18:36:04] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:04] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:04] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:36:04] - Attempted to create child Producto product_cat `CERDO`, duplicate detected. Importing Producto to existing `CERDO` product_cat, ID 3101, slug `cerdo` ...</div>
<div class='progress-msg'>[18:36:04] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:04] <b>CREATED</b> `CABEZA DE PUERCO SOLO LA MITAD` `Producto` (ID: 19098)</div>
<div class='progress-msg'>[18:36:04] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:04] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:04] ---</div>
<div class='progress-msg'>[18:36:04] Record #27</div>
<div class='progress-msg'>[18:36:04] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:04] Combine all data for post `RABITO DE PUERCO`...</div>
<div class='progress-msg'>[18:36:04] Find corresponding article among database for post `RABITO DE PUERCO`...</div>
<div class='progress-msg'>[18:36:04] Duplicate post wasn't found for post `RABITO DE PUERCO`...</div>
<div class='progress-msg'>[18:36:04] Applying filter `pmxi_article_data` for `RABITO DE PUERCO`</div>
<div class='progress-msg'>[18:36:04] <b>CREATING</b> `RABITO DE PUERCO` `Producto`</div>
<div class='progress-msg'>[18:36:04] Associate post `RABITO DE PUERCO` with current import ...</div>
<div class='progress-msg'>[18:36:04] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:04] Property `price` updated with value `2.85`</div>
<div class='progress-msg'>[18:36:04] Property `regular_price` updated with value `2.85`</div>
<div class='progress-msg'>[18:36:04] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:04] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:04] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `sku` updated with value `36f0d22ee7be`</div>
<div class='progress-msg'>[18:36:04] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:04] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:04] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:04] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:04] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:04] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:04] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:04] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:04] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:04] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:04] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:04] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RABITO-DE-PUERCO.png` for `RABITO DE PUERCO` ...</div>
<div class='progress-msg'>[18:36:04] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RABITO-DE-PUERCO.png` by URL...</div>
<div class='progress-msg'>[18:36:04] - <b>WARNING</b>: Image RABITO-DE-PUERCO.png not found in media gallery.</div>
<div class='progress-msg'>[18:36:04] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RABITO-DE-PUERCO.png`</div>
<div class='progress-msg'>[18:36:04] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RABITO-DE-PUERCO.png` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:04] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/RABITO-DE-PUERCO.png`</div>
<div class='progress-msg'>[18:36:04] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:04] - Attachment with ID: `19101` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/RABITO-DE-PUERCO.png`</div>
<div class='progress-msg'>[18:36:04] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:04] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:04] - Attempted to create parent Producto product_cat `CARNES Y AVES`, duplicate detected. Importing Producto to existing `CARNES Y AVES` product_cat, ID 3098, slug `carnes-y-aves` ...</div>
<div class='progress-msg'>[18:36:04] - Attempted to create child Producto product_cat `CERDO`, duplicate detected. Importing Producto to existing `CERDO` product_cat, ID 3101, slug `cerdo` ...</div>
<div class='progress-msg'>[18:36:04] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:04] <b>CREATED</b> `RABITO DE PUERCO` `Producto` (ID: 19100)</div>
<div class='progress-msg'>[18:36:04] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:04] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:04] ---</div>
<div class='progress-msg'>[18:36:04] Record #28</div>
<div class='progress-msg'>[18:36:04] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:04] Combine all data for post `TOMATE PERITA`...</div>
<div class='progress-msg'>[18:36:04] Find corresponding article among database for post `TOMATE PERITA`...</div>
<div class='progress-msg'>[18:36:04] Duplicate post wasn't found for post `TOMATE PERITA`...</div>
<div class='progress-msg'>[18:36:04] Applying filter `pmxi_article_data` for `TOMATE PERITA`</div>
<div class='progress-msg'>[18:36:04] <b>CREATING</b> `TOMATE PERITA` `Producto`</div>
<div class='progress-msg'>[18:36:04] Associate post `TOMATE PERITA` with current import ...</div>
<div class='progress-msg'>[18:36:04] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:05] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:05] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:05] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `sku` updated with value `7c90ea96c72c`</div>
<div class='progress-msg'>[18:36:05] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:05] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:05] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:05] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:05] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:05] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:05] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:05] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:05] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:05] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:05] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TOMATE-PERITA.jpg` for `TOMATE PERITA` ...</div>
<div class='progress-msg'>[18:36:05] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TOMATE-PERITA.jpg` by URL...</div>
<div class='progress-msg'>[18:36:05] - <b>WARNING</b>: Image TOMATE-PERITA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:05] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TOMATE-PERITA.jpg`</div>
<div class='progress-msg'>[18:36:05] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TOMATE-PERITA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:05] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/TOMATE-PERITA.jpg`</div>
<div class='progress-msg'>[18:36:05] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:05] - Attachment with ID: `19103` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/TOMATE-PERITA.jpg`</div>
<div class='progress-msg'>[18:36:05] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:05] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:05] - Creating parent Producto product_cat `LEGUMBRES Y VERDURAS` ...</div>
<div class='progress-msg'>[18:36:05] - Creating child Producto product_cat for LEGUMBRES Y VERDURAS named `VEGETALES/TUBERCULOS` ...</div>
<div class='progress-msg'>[18:36:05] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:05] <b>CREATED</b> `TOMATE PERITA` `Producto` (ID: 19102)</div>
<div class='progress-msg'>[18:36:05] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:05] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:05] ---</div>
<div class='progress-msg'>[18:36:05] Record #29</div>
<div class='progress-msg'>[18:36:05] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:05] Combine all data for post `TOMATE DE ENSALADA`...</div>
<div class='progress-msg'>[18:36:05] Find corresponding article among database for post `TOMATE DE ENSALADA`...</div>
<div class='progress-msg'>[18:36:05] Duplicate post wasn't found for post `TOMATE DE ENSALADA`...</div>
<div class='progress-msg'>[18:36:05] Applying filter `pmxi_article_data` for `TOMATE DE ENSALADA`</div>
<div class='progress-msg'>[18:36:05] <b>CREATING</b> `TOMATE DE ENSALADA` `Producto`</div>
<div class='progress-msg'>[18:36:05] Associate post `TOMATE DE ENSALADA` with current import ...</div>
<div class='progress-msg'>[18:36:05] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:05] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:05] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:05] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `sku` updated with value `fafd6528417d`</div>
<div class='progress-msg'>[18:36:05] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:05] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:05] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:05] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:05] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:05] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:05] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:05] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:05] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:05] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:06] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:06] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TOMATE-DE-ENSALADA.jpeg` for `TOMATE DE ENSALADA` ...</div>
<div class='progress-msg'>[18:36:06] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TOMATE-DE-ENSALADA.jpeg` by URL...</div>
<div class='progress-msg'>[18:36:06] - <b>WARNING</b>: Image TOMATE-DE-ENSALADA.jpeg not found in media gallery.</div>
<div class='progress-msg'>[18:36:06] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TOMATE-DE-ENSALADA.jpeg`</div>
<div class='progress-msg'>[18:36:06] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TOMATE-DE-ENSALADA.jpeg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:06] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/TOMATE-DE-ENSALADA.jpeg`</div>
<div class='progress-msg'>[18:36:06] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:06] - Attachment with ID: `19105` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/TOMATE-DE-ENSALADA.jpeg`</div>
<div class='progress-msg'>[18:36:06] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:06] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:06] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:06] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:06] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:06] <b>CREATED</b> `TOMATE DE ENSALADA` `Producto` (ID: 19104)</div>
<div class='progress-msg'>[18:36:06] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:06] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:06] ---</div>
<div class='progress-msg'>[18:36:06] Record #30</div>
<div class='progress-msg'>[18:36:06] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:06] Combine all data for post `CULANTRO`...</div>
<div class='progress-msg'>[18:36:06] Find corresponding article among database for post `CULANTRO`...</div>
<div class='progress-msg'>[18:36:06] Duplicate post was found for post `CULANTRO`...</div>
<div class='progress-msg'>[18:36:06] Deleting attachments for `CULANTRO`</div>
<div class='progress-msg'>[18:36:06] Deleting images for `CULANTRO`</div>
<div class='progress-msg'>[18:36:06] Applying filter `pmxi_article_data` for `CULANTRO`</div>
<div class='progress-msg'>[18:36:06] <b>UPDATING</b> `CULANTRO` `Producto`</div>
<div class='progress-msg'>[18:36:06] Associate post `CULANTRO` with current import ...</div>
<div class='progress-msg'>[18:36:06] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:06] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:06] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:06] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `sku` updated with value `925e542a77db`</div>
<div class='progress-msg'>[18:36:06] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:06] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:06] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:06] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:06] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:06] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:06] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:06] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:06] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:06] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:07] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:07] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CULANTRO.jpg` for `CULANTRO` ...</div>
<div class='progress-msg'>[18:36:07] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CULANTRO.jpg` by URL...</div>
<div class='progress-msg'>[18:36:07] - <b>WARNING</b>: Image CULANTRO.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:07] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CULANTRO.jpg`</div>
<div class='progress-msg'>[18:36:07] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CULANTRO.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:07] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CULANTRO.jpg`</div>
<div class='progress-msg'>[18:36:07] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:07] - Attachment with ID: `19106` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CULANTRO.jpg`</div>
<div class='progress-msg'>[18:36:07] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:07] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:07] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:07] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:07] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:07] <b>UPDATED</b> `CULANTRO` `Producto` (ID: 18035)</div>
<div class='progress-msg'>[18:36:07] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:07] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:07] ---</div>
<div class='progress-msg'>[18:36:07] Record #31</div>
<div class='progress-msg'>[18:36:07] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:07] Combine all data for post `PEREJIL`...</div>
<div class='progress-msg'>[18:36:07] Find corresponding article among database for post `PEREJIL`...</div>
<div class='progress-msg'>[18:36:07] Duplicate post wasn't found for post `PEREJIL`...</div>
<div class='progress-msg'>[18:36:07] Applying filter `pmxi_article_data` for `PEREJIL`</div>
<div class='progress-msg'>[18:36:07] <b>CREATING</b> `PEREJIL` `Producto`</div>
<div class='progress-msg'>[18:36:07] Associate post `PEREJIL` with current import ...</div>
<div class='progress-msg'>[18:36:07] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:07] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:07] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:07] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `sku` updated with value `059db7281b31`</div>
<div class='progress-msg'>[18:36:07] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:07] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:07] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:07] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:07] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:07] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:07] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:07] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:07] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:07] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:08] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:08] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PEREJIL.jpg` for `PEREJIL` ...</div>
<div class='progress-msg'>[18:36:08] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PEREJIL.jpg` by URL...</div>
<div class='progress-msg'>[18:36:08] - <b>WARNING</b>: Image PEREJIL.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:08] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PEREJIL.jpg`</div>
<div class='progress-msg'>[18:36:08] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PEREJIL.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:08] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PEREJIL.jpg`</div>
<div class='progress-msg'>[18:36:08] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:08] - Attachment with ID: `19108` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PEREJIL.jpg`</div>
<div class='progress-msg'>[18:36:08] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:08] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:08] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:08] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:08] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:08] <b>CREATED</b> `PEREJIL` `Producto` (ID: 19107)</div>
<div class='progress-msg'>[18:36:08] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:08] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:08] ---</div>
<div class='progress-msg'>[18:36:08] Record #32</div>
<div class='progress-msg'>[18:36:08] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:08] Combine all data for post `RECADO VERDE`...</div>
<div class='progress-msg'>[18:36:08] Find corresponding article among database for post `RECADO VERDE`...</div>
<div class='progress-msg'>[18:36:08] Duplicate post wasn't found for post `RECADO VERDE`...</div>
<div class='progress-msg'>[18:36:08] Applying filter `pmxi_article_data` for `RECADO VERDE`</div>
<div class='progress-msg'>[18:36:08] <b>CREATING</b> `RECADO VERDE` `Producto`</div>
<div class='progress-msg'>[18:36:08] Associate post `RECADO VERDE` with current import ...</div>
<div class='progress-msg'>[18:36:08] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:08] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:08] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:08] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `sku` updated with value `ba22892b1835`</div>
<div class='progress-msg'>[18:36:08] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:08] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:08] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:08] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:08] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:08] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:08] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:08] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:08] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:08] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:08] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:08] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RECADO-VERDE-1.webp` for `RECADO VERDE` ...</div>
<div class='progress-msg'>[18:36:08] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RECADO-VERDE-1.webp` by URL...</div>
<div class='progress-msg'>[18:36:08] - <b>WARNING</b>: Image RECADO-VERDE-1.webp not found in media gallery.</div>
<div class='progress-msg'>[18:36:08] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RECADO-VERDE-1.webp`</div>
<div class='progress-msg'>[18:36:08] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RECADO-VERDE-1.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:08] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/RECADO-VERDE-1.webp`</div>
<div class='progress-msg'>[18:36:08] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:08] - Attachment with ID: `19110` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/RECADO-VERDE-1.webp`</div>
<div class='progress-msg'>[18:36:08] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:08] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:08] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:08] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:08] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:08] <b>CREATED</b> `RECADO VERDE` `Producto` (ID: 19109)</div>
<div class='progress-msg'>[18:36:09] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:09] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:09] ---</div>
<div class='progress-msg'>[18:36:09] Record #33</div>
<div class='progress-msg'>[18:36:09] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:09] Combine all data for post `REPOLLO VERDE`...</div>
<div class='progress-msg'>[18:36:09] Find corresponding article among database for post `REPOLLO VERDE`...</div>
<div class='progress-msg'>[18:36:09] Duplicate post wasn't found for post `REPOLLO VERDE`...</div>
<div class='progress-msg'>[18:36:09] Applying filter `pmxi_article_data` for `REPOLLO VERDE`</div>
<div class='progress-msg'>[18:36:09] <b>CREATING</b> `REPOLLO VERDE` `Producto`</div>
<div class='progress-msg'>[18:36:09] Associate post `REPOLLO VERDE` with current import ...</div>
<div class='progress-msg'>[18:36:09] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:09] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:09] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:09] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `sku` updated with value `bee6f4c70a69`</div>
<div class='progress-msg'>[18:36:09] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:09] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:09] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:09] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:09] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:09] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:09] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:09] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:09] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:09] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:09] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/REPOLLO-VERDE.jpg` for `REPOLLO VERDE` ...</div>
<div class='progress-msg'>[18:36:09] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/REPOLLO-VERDE.jpg` by URL...</div>
<div class='progress-msg'>[18:36:09] - <b>WARNING</b>: Image REPOLLO-VERDE.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:09] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/REPOLLO-VERDE.jpg`</div>
<div class='progress-msg'>[18:36:09] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/REPOLLO-VERDE.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:09] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/REPOLLO-VERDE.jpg`</div>
<div class='progress-msg'>[18:36:09] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:09] - Attachment with ID: `19112` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/REPOLLO-VERDE.jpg`</div>
<div class='progress-msg'>[18:36:09] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:09] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:09] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:09] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:09] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:09] <b>CREATED</b> `REPOLLO VERDE` `Producto` (ID: 19111)</div>
<div class='progress-msg'>[18:36:09] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:09] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:09] ---</div>
<div class='progress-msg'>[18:36:09] Record #34</div>
<div class='progress-msg'>[18:36:09] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:09] Combine all data for post `REPOLLO MORADO`...</div>
<div class='progress-msg'>[18:36:09] Find corresponding article among database for post `REPOLLO MORADO`...</div>
<div class='progress-msg'>[18:36:09] Duplicate post wasn't found for post `REPOLLO MORADO`...</div>
<div class='progress-msg'>[18:36:09] Applying filter `pmxi_article_data` for `REPOLLO MORADO`</div>
<div class='progress-msg'>[18:36:09] <b>CREATING</b> `REPOLLO MORADO` `Producto`</div>
<div class='progress-msg'>[18:36:09] Associate post `REPOLLO MORADO` with current import ...</div>
<div class='progress-msg'>[18:36:09] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:09] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:09] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:09] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `sku` updated with value `7b55c66d3324`</div>
<div class='progress-msg'>[18:36:09] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:09] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:09] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:09] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:09] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:09] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:09] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:09] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:09] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:09] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:10] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:10] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/REPOLLO-MORADO.webp` for `REPOLLO MORADO` ...</div>
<div class='progress-msg'>[18:36:10] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/REPOLLO-MORADO.webp` by URL...</div>
<div class='progress-msg'>[18:36:10] - <b>WARNING</b>: Image REPOLLO-MORADO.webp not found in media gallery.</div>
<div class='progress-msg'>[18:36:10] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/REPOLLO-MORADO.webp`</div>
<div class='progress-msg'>[18:36:10] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/REPOLLO-MORADO.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:10] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/REPOLLO-MORADO.webp`</div>
<div class='progress-msg'>[18:36:10] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:10] - Attachment with ID: `19114` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/REPOLLO-MORADO.webp`</div>
<div class='progress-msg'>[18:36:10] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:10] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:10] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:10] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:10] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:10] <b>CREATED</b> `REPOLLO MORADO` `Producto` (ID: 19113)</div>
<div class='progress-msg'>[18:36:10] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:10] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:10] ---</div>
<div class='progress-msg'>[18:36:10] Record #35</div>
<div class='progress-msg'>[18:36:10] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:10] Combine all data for post `REMOLACHA`...</div>
<div class='progress-msg'>[18:36:10] Find corresponding article among database for post `REMOLACHA`...</div>
<div class='progress-msg'>[18:36:10] Duplicate post wasn't found for post `REMOLACHA`...</div>
<div class='progress-msg'>[18:36:10] Applying filter `pmxi_article_data` for `REMOLACHA`</div>
<div class='progress-msg'>[18:36:10] <b>CREATING</b> `REMOLACHA` `Producto`</div>
<div class='progress-msg'>[18:36:10] Associate post `REMOLACHA` with current import ...</div>
<div class='progress-msg'>[18:36:10] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:10] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:10] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:10] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `sku` updated with value `8b01075f4a9a`</div>
<div class='progress-msg'>[18:36:10] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:10] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:10] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:10] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:10] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:10] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:10] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:10] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:10] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:10] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:10] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:10] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/REMOLACHA.png` for `REMOLACHA` ...</div>
<div class='progress-msg'>[18:36:10] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/REMOLACHA.png` by URL...</div>
<div class='progress-msg'>[18:36:10] - <b>WARNING</b>: Image REMOLACHA.png not found in media gallery.</div>
<div class='progress-msg'>[18:36:10] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/REMOLACHA.png`</div>
<div class='progress-msg'>[18:36:10] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/REMOLACHA.png` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:10] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/REMOLACHA.png`</div>
<div class='progress-msg'>[18:36:11] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:11] - Attachment with ID: `19116` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/REMOLACHA.png`</div>
<div class='progress-msg'>[18:36:11] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:11] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:11] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:11] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:11] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:11] <b>CREATED</b> `REMOLACHA` `Producto` (ID: 19115)</div>
<div class='progress-msg'>[18:36:11] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:11] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:11] ---</div>
<div class='progress-msg'>[18:36:11] Record #36</div>
<div class='progress-msg'>[18:36:11] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:11] Combine all data for post `LECHUGA`...</div>
<div class='progress-msg'>[18:36:11] Find corresponding article among database for post `LECHUGA`...</div>
<div class='progress-msg'>[18:36:11] Duplicate post wasn't found for post `LECHUGA`...</div>
<div class='progress-msg'>[18:36:11] Applying filter `pmxi_article_data` for `LECHUGA`</div>
<div class='progress-msg'>[18:36:11] <b>CREATING</b> `LECHUGA` `Producto`</div>
<div class='progress-msg'>[18:36:11] Associate post `LECHUGA` with current import ...</div>
<div class='progress-msg'>[18:36:11] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:11] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:11] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:11] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `sku` updated with value `e2c3bcb85872`</div>
<div class='progress-msg'>[18:36:11] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:11] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:11] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:11] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:11] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:11] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:11] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:11] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:11] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:11] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:11] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:11] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/LECHUGA.jpg` for `LECHUGA` ...</div>
<div class='progress-msg'>[18:36:11] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/LECHUGA.jpg` by URL...</div>
<div class='progress-msg'>[18:36:11] - <b>WARNING</b>: Image LECHUGA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:11] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/LECHUGA.jpg`</div>
<div class='progress-msg'>[18:36:11] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/LECHUGA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:11] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/LECHUGA.jpg`</div>
<div class='progress-msg'>[18:36:11] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:11] - Attachment with ID: `19118` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/LECHUGA.jpg`</div>
<div class='progress-msg'>[18:36:11] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:11] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:11] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:11] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:11] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:11] <b>CREATED</b> `LECHUGA` `Producto` (ID: 19117)</div>
<div class='progress-msg'>[18:36:11] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:11] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:11] ---</div>
<div class='progress-msg'>[18:36:11] Record #37</div>
<div class='progress-msg'>[18:36:11] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:11] Combine all data for post `ENSALADA DE REPOLLO CON ZANAHORIA`...</div>
<div class='progress-msg'>[18:36:11] Find corresponding article among database for post `ENSALADA DE REPOLLO CON ZANAHORIA`...</div>
<div class='progress-msg'>[18:36:11] Duplicate post wasn't found for post `ENSALADA DE REPOLLO CON ZANAHORIA`...</div>
<div class='progress-msg'>[18:36:11] Applying filter `pmxi_article_data` for `ENSALADA DE REPOLLO CON ZANAHORIA`</div>
<div class='progress-msg'>[18:36:11] <b>CREATING</b> `ENSALADA DE REPOLLO CON ZANAHORIA` `Producto`</div>
<div class='progress-msg'>[18:36:12] Associate post `ENSALADA DE REPOLLO CON ZANAHORIA` with current import ...</div>
<div class='progress-msg'>[18:36:12] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:12] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:12] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:12] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `sku` updated with value `21a405021ff8`</div>
<div class='progress-msg'>[18:36:12] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:12] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:12] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:12] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:12] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:12] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:12] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:12] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:12] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:12] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:12] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ENSALADA-DE-REPOLLO-CON-ZANAHORIA.jpg` for `ENSALADA DE REPOLLO CON ZANAHORIA` ...</div>
<div class='progress-msg'>[18:36:12] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ENSALADA-DE-REPOLLO-CON-ZANAHORIA.jpg` by URL...</div>
<div class='progress-msg'>[18:36:12] - <b>WARNING</b>: Image ENSALADA-DE-REPOLLO-CON-ZANAHORIA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:12] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ENSALADA-DE-REPOLLO-CON-ZANAHORIA.jpg`</div>
<div class='progress-msg'>[18:36:12] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ENSALADA-DE-REPOLLO-CON-ZANAHORIA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:12] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/ENSALADA-DE-REPOLLO-CON-ZANAHORIA.jpg`</div>
<div class='progress-msg'>[18:36:12] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:12] - Attachment with ID: `19120` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/ENSALADA-DE-REPOLLO-CON-ZANAHORIA.jpg`</div>
<div class='progress-msg'>[18:36:12] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:12] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:12] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:12] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:12] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:12] <b>CREATED</b> `ENSALADA DE REPOLLO CON ZANAHORIA` `Producto` (ID: 19119)</div>
<div class='progress-msg'>[18:36:12] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:12] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:12] ---</div>
<div class='progress-msg'>[18:36:12] Record #38</div>
<div class='progress-msg'>[18:36:12] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:12] Combine all data for post `MAZORCA`...</div>
<div class='progress-msg'>[18:36:12] Find corresponding article among database for post `MAZORCA`...</div>
<div class='progress-msg'>[18:36:12] Duplicate post wasn't found for post `MAZORCA`...</div>
<div class='progress-msg'>[18:36:12] Applying filter `pmxi_article_data` for `MAZORCA`</div>
<div class='progress-msg'>[18:36:12] <b>CREATING</b> `MAZORCA` `Producto`</div>
<div class='progress-msg'>[18:36:12] Associate post `MAZORCA` with current import ...</div>
<div class='progress-msg'>[18:36:12] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:12] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:12] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:12] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `sku` updated with value `58bf99c4119a`</div>
<div class='progress-msg'>[18:36:12] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:12] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:12] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:12] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:12] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:12] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:12] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:12] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:12] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:12] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:13] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:13] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MAZORCA.jpg` for `MAZORCA` ...</div>
<div class='progress-msg'>[18:36:13] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MAZORCA.jpg` by URL...</div>
<div class='progress-msg'>[18:36:13] - <b>WARNING</b>: Image MAZORCA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:13] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MAZORCA.jpg`</div>
<div class='progress-msg'>[18:36:13] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MAZORCA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:13] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/MAZORCA.jpg`</div>
<div class='progress-msg'>[18:36:13] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:13] - Attachment with ID: `19122` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/MAZORCA.jpg`</div>
<div class='progress-msg'>[18:36:13] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:13] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:13] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:13] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:13] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:13] <b>CREATED</b> `MAZORCA` `Producto` (ID: 19121)</div>
<div class='progress-msg'>[18:36:13] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:13] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:13] ---</div>
<div class='progress-msg'>[18:36:13] Record #39</div>
<div class='progress-msg'>[18:36:13] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:13] Combine all data for post `AJI VERDE`...</div>
<div class='progress-msg'>[18:36:13] Find corresponding article among database for post `AJI VERDE`...</div>
<div class='progress-msg'>[18:36:13] Duplicate post wasn't found for post `AJI VERDE`...</div>
<div class='progress-msg'>[18:36:13] Applying filter `pmxi_article_data` for `AJI VERDE`</div>
<div class='progress-msg'>[18:36:13] <b>CREATING</b> `AJI VERDE` `Producto`</div>
<div class='progress-msg'>[18:36:13] Associate post `AJI VERDE` with current import ...</div>
<div class='progress-msg'>[18:36:13] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:13] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:13] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:13] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `sku` updated with value `c8552d089168`</div>
<div class='progress-msg'>[18:36:13] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:13] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:13] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:13] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:13] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:13] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:13] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:13] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:13] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:13] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:13] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:13] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/AJI-VERDE.webp` for `AJI VERDE` ...</div>
<div class='progress-msg'>[18:36:13] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/AJI-VERDE.webp` by URL...</div>
<div class='progress-msg'>[18:36:13] - <b>WARNING</b>: Image AJI-VERDE.webp not found in media gallery.</div>
<div class='progress-msg'>[18:36:13] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/AJI-VERDE.webp`</div>
<div class='progress-msg'>[18:36:13] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/AJI-VERDE.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:13] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/AJI-VERDE.webp`</div>
<div class='progress-msg'>[18:36:13] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:13] - Attachment with ID: `19124` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/AJI-VERDE.webp`</div>
<div class='progress-msg'>[18:36:14] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:14] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:14] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:14] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:14] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:14] <b>CREATED</b> `AJI VERDE` `Producto` (ID: 19123)</div>
<div class='progress-msg'>[18:36:14] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:14] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:14] ---</div>
<div class='progress-msg'>[18:36:14] Record #40</div>
<div class='progress-msg'>[18:36:14] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:14] Combine all data for post `APIO`...</div>
<div class='progress-msg'>[18:36:14] Find corresponding article among database for post `APIO`...</div>
<div class='progress-msg'>[18:36:14] Duplicate post wasn't found for post `APIO`...</div>
<div class='progress-msg'>[18:36:14] Applying filter `pmxi_article_data` for `APIO`</div>
<div class='progress-msg'>[18:36:14] <b>CREATING</b> `APIO` `Producto`</div>
<div class='progress-msg'>[18:36:14] Associate post `APIO` with current import ...</div>
<div class='progress-msg'>[18:36:14] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:14] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:14] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:14] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `sku` updated with value `a6d636febaa3`</div>
<div class='progress-msg'>[18:36:14] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:14] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:14] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:14] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:14] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:14] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:14] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:14] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:14] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:14] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:14] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:14] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/APIO.jpg` for `APIO` ...</div>
<div class='progress-msg'>[18:36:14] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/APIO.jpg` by URL...</div>
<div class='progress-msg'>[18:36:14] - <b>WARNING</b>: Image APIO.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:14] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/APIO.jpg`</div>
<div class='progress-msg'>[18:36:14] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/APIO.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:14] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/APIO.jpg`</div>
<div class='progress-msg'>[18:36:14] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:14] - Attachment with ID: `19126` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/APIO.jpg`</div>
<div class='progress-msg'>[18:36:14] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:14] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:14] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:14] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:14] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:14] <b>CREATED</b> `APIO` `Producto` (ID: 19125)</div>
<div class='progress-msg'>[18:36:14] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:14] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:16] ---</div>
<div class='progress-msg'>[18:36:16] Record #41</div>
<div class='progress-msg'>[18:36:16] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:16] Combine all data for post `PEPINO`...</div>
<div class='progress-msg'>[18:36:16] Find corresponding article among database for post `PEPINO`...</div>
<div class='progress-msg'>[18:36:16] Duplicate post wasn't found for post `PEPINO`...</div>
<div class='progress-msg'>[18:36:16] Applying filter `pmxi_article_data` for `PEPINO`</div>
<div class='progress-msg'>[18:36:16] <b>CREATING</b> `PEPINO` `Producto`</div>
<div class='progress-msg'>[18:36:16] Associate post `PEPINO` with current import ...</div>
<div class='progress-msg'>[18:36:16] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:16] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:16] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:16] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `sku` updated with value `59ff89a409ad`</div>
<div class='progress-msg'>[18:36:16] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:16] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:16] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:16] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:16] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:16] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:16] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:16] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:16] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:16] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:17] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:17] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PEPINO.jpeg` for `PEPINO` ...</div>
<div class='progress-msg'>[18:36:17] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PEPINO.jpeg` by URL...</div>
<div class='progress-msg'>[18:36:17] - <b>WARNING</b>: Image PEPINO.jpeg not found in media gallery.</div>
<div class='progress-msg'>[18:36:17] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PEPINO.jpeg`</div>
<div class='progress-msg'>[18:36:17] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PEPINO.jpeg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:17] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PEPINO.jpeg`</div>
<div class='progress-msg'>[18:36:17] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:17] - Attachment with ID: `19128` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PEPINO.jpeg`</div>
<div class='progress-msg'>[18:36:17] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:17] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:17] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:17] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:17] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:17] <b>CREATED</b> `PEPINO` `Producto` (ID: 19127)</div>
<div class='progress-msg'>[18:36:17] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:17] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:17] ---</div>
<div class='progress-msg'>[18:36:17] Record #42</div>
<div class='progress-msg'>[18:36:17] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:17] Combine all data for post `ZANAHORIA`...</div>
<div class='progress-msg'>[18:36:17] Find corresponding article among database for post `ZANAHORIA`...</div>
<div class='progress-msg'>[18:36:17] Duplicate post wasn't found for post `ZANAHORIA`...</div>
<div class='progress-msg'>[18:36:17] Applying filter `pmxi_article_data` for `ZANAHORIA`</div>
<div class='progress-msg'>[18:36:17] <b>CREATING</b> `ZANAHORIA` `Producto`</div>
<div class='progress-msg'>[18:36:17] Associate post `ZANAHORIA` with current import ...</div>
<div class='progress-msg'>[18:36:17] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:17] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:17] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:17] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `sku` updated with value `b9ac633ebd3c`</div>
<div class='progress-msg'>[18:36:17] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:17] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:17] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:17] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:17] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:17] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:17] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:17] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:17] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:17] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:17] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:17] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ZANAHORIA.png` for `ZANAHORIA` ...</div>
<div class='progress-msg'>[18:36:17] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ZANAHORIA.png` by URL...</div>
<div class='progress-msg'>[18:36:17] - <b>WARNING</b>: Image ZANAHORIA.png not found in media gallery.</div>
<div class='progress-msg'>[18:36:17] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ZANAHORIA.png`</div>
<div class='progress-msg'>[18:36:17] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ZANAHORIA.png` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:17] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/ZANAHORIA.png`</div>
<div class='progress-msg'>[18:36:18] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:18] - Attachment with ID: `19130` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/ZANAHORIA.png`</div>
<div class='progress-msg'>[18:36:18] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:18] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:18] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:18] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:18] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:18] <b>CREATED</b> `ZANAHORIA` `Producto` (ID: 19129)</div>
<div class='progress-msg'>[18:36:18] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:18] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:18] ---</div>
<div class='progress-msg'>[18:36:18] Record #43</div>
<div class='progress-msg'>[18:36:18] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:18] Combine all data for post `ZAPALLO`...</div>
<div class='progress-msg'>[18:36:18] Find corresponding article among database for post `ZAPALLO`...</div>
<div class='progress-msg'>[18:36:18] Duplicate post wasn't found for post `ZAPALLO`...</div>
<div class='progress-msg'>[18:36:18] Applying filter `pmxi_article_data` for `ZAPALLO`</div>
<div class='progress-msg'>[18:36:18] <b>CREATING</b> `ZAPALLO` `Producto`</div>
<div class='progress-msg'>[18:36:18] Associate post `ZAPALLO` with current import ...</div>
<div class='progress-msg'>[18:36:18] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:18] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:18] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:18] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `sku` updated with value `59b8d1fdf16e`</div>
<div class='progress-msg'>[18:36:18] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:18] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:18] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:18] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:18] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:18] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:18] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:18] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:18] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:18] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:18] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:18] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ZAPALLO.jpg` for `ZAPALLO` ...</div>
<div class='progress-msg'>[18:36:18] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ZAPALLO.jpg` by URL...</div>
<div class='progress-msg'>[18:36:18] - <b>WARNING</b>: Image ZAPALLO.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:18] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ZAPALLO.jpg`</div>
<div class='progress-msg'>[18:36:18] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/ZAPALLO.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:18] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/ZAPALLO.jpg`</div>
<div class='progress-msg'>[18:36:18] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:18] - Attachment with ID: `19132` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/ZAPALLO.jpg`</div>
<div class='progress-msg'>[18:36:18] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:18] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:18] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:18] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:18] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:18] <b>CREATED</b> `ZAPALLO` `Producto` (ID: 19131)</div>
<div class='progress-msg'>[18:36:19] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:19] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:19] ---</div>
<div class='progress-msg'>[18:36:19] Record #44</div>
<div class='progress-msg'>[18:36:19] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:19] Combine all data for post `MANZANA ROJA`...</div>
<div class='progress-msg'>[18:36:19] Find corresponding article among database for post `MANZANA ROJA`...</div>
<div class='progress-msg'>[18:36:19] Duplicate post wasn't found for post `MANZANA ROJA`...</div>
<div class='progress-msg'>[18:36:19] Applying filter `pmxi_article_data` for `MANZANA ROJA`</div>
<div class='progress-msg'>[18:36:19] <b>CREATING</b> `MANZANA ROJA` `Producto`</div>
<div class='progress-msg'>[18:36:19] Associate post `MANZANA ROJA` with current import ...</div>
<div class='progress-msg'>[18:36:19] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:19] Property `price` updated with value `0.5`</div>
<div class='progress-msg'>[18:36:19] Property `regular_price` updated with value `0.5`</div>
<div class='progress-msg'>[18:36:19] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:19] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:19] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `sku` updated with value `cbc9f1941356`</div>
<div class='progress-msg'>[18:36:19] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:19] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:19] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:19] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:19] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:19] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:19] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:19] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:19] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:19] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:19] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MANZANA-ROJA.jpg` for `MANZANA ROJA` ...</div>
<div class='progress-msg'>[18:36:19] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MANZANA-ROJA.jpg` by URL...</div>
<div class='progress-msg'>[18:36:19] - <b>WARNING</b>: Image MANZANA-ROJA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:19] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MANZANA-ROJA.jpg`</div>
<div class='progress-msg'>[18:36:19] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MANZANA-ROJA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:19] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/MANZANA-ROJA.jpg`</div>
<div class='progress-msg'>[18:36:19] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:19] - Attachment with ID: `19134` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/MANZANA-ROJA.jpg`</div>
<div class='progress-msg'>[18:36:19] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:19] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:19] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:19] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:19] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:19] <b>CREATED</b> `MANZANA ROJA` `Producto` (ID: 19133)</div>
<div class='progress-msg'>[18:36:19] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:19] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:19] ---</div>
<div class='progress-msg'>[18:36:19] Record #45</div>
<div class='progress-msg'>[18:36:19] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:19] Combine all data for post `MANZANA VERDE`...</div>
<div class='progress-msg'>[18:36:19] Find corresponding article among database for post `MANZANA VERDE`...</div>
<div class='progress-msg'>[18:36:19] Duplicate post wasn't found for post `MANZANA VERDE`...</div>
<div class='progress-msg'>[18:36:19] Applying filter `pmxi_article_data` for `MANZANA VERDE`</div>
<div class='progress-msg'>[18:36:19] <b>CREATING</b> `MANZANA VERDE` `Producto`</div>
<div class='progress-msg'>[18:36:19] Associate post `MANZANA VERDE` with current import ...</div>
<div class='progress-msg'>[18:36:19] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:19] Property `price` updated with value `0.6`</div>
<div class='progress-msg'>[18:36:19] Property `regular_price` updated with value `0.6`</div>
<div class='progress-msg'>[18:36:19] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:19] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:19] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `sku` updated with value `0c140c6ce1c7`</div>
<div class='progress-msg'>[18:36:19] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:19] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:19] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:19] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:19] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:19] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:19] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:19] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:19] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:19] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:20] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:20] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MANZANA-VERDE-scaled.jpg` for `MANZANA VERDE` ...</div>
<div class='progress-msg'>[18:36:20] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MANZANA-VERDE-scaled.jpg` by URL...</div>
<div class='progress-msg'>[18:36:20] - <b>WARNING</b>: Image MANZANA-VERDE-scaled.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:20] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MANZANA-VERDE-scaled.jpg`</div>
<div class='progress-msg'>[18:36:20] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MANZANA-VERDE-scaled.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:20] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/MANZANA-VERDE-scaled.jpg`</div>
<div class='progress-msg'>[18:36:20] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:20] - Attachment with ID: `19136` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/MANZANA-VERDE-scaled.jpg`</div>
<div class='progress-msg'>[18:36:20] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:20] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:20] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:20] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:20] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:20] <b>CREATED</b> `MANZANA VERDE` `Producto` (ID: 19135)</div>
<div class='progress-msg'>[18:36:20] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:20] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:20] ---</div>
<div class='progress-msg'>[18:36:20] Record #46</div>
<div class='progress-msg'>[18:36:20] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:20] Combine all data for post `UVA CON SEMILLA`...</div>
<div class='progress-msg'>[18:36:20] Find corresponding article among database for post `UVA CON SEMILLA`...</div>
<div class='progress-msg'>[18:36:20] Duplicate post wasn't found for post `UVA CON SEMILLA`...</div>
<div class='progress-msg'>[18:36:20] Applying filter `pmxi_article_data` for `UVA CON SEMILLA`</div>
<div class='progress-msg'>[18:36:20] <b>CREATING</b> `UVA CON SEMILLA` `Producto`</div>
<div class='progress-msg'>[18:36:20] Associate post `UVA CON SEMILLA` with current import ...</div>
<div class='progress-msg'>[18:36:20] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:20] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:20] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:20] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `sku` updated with value `6cf78034a710`</div>
<div class='progress-msg'>[18:36:20] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:20] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:20] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:20] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:20] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:20] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:20] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:20] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:20] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:20] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:21] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:21] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/UVA-CON-SEMILLA.webp` for `UVA CON SEMILLA` ...</div>
<div class='progress-msg'>[18:36:21] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/UVA-CON-SEMILLA.webp` by URL...</div>
<div class='progress-msg'>[18:36:21] - <b>WARNING</b>: Image UVA-CON-SEMILLA.webp not found in media gallery.</div>
<div class='progress-msg'>[18:36:21] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/UVA-CON-SEMILLA.webp`</div>
<div class='progress-msg'>[18:36:24] - <b>WARNING</b>: File https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/UVA-CON-SEMILLA.webp can not be downloaded, response </div>
<div class='progress-msg'>[18:36:24] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:24] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:24] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:24] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:24] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:24] <b>CREATED</b> `UVA CON SEMILLA` `Producto` (ID: 19137)</div>
<div class='progress-msg'>[18:36:24] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:24] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:24] ---</div>
<div class='progress-msg'>[18:36:24] Record #47</div>
<div class='progress-msg'>[18:36:24] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:24] Combine all data for post `MANDARINA`...</div>
<div class='progress-msg'>[18:36:24] Find corresponding article among database for post `MANDARINA`...</div>
<div class='progress-msg'>[18:36:24] Duplicate post wasn't found for post `MANDARINA`...</div>
<div class='progress-msg'>[18:36:24] Applying filter `pmxi_article_data` for `MANDARINA`</div>
<div class='progress-msg'>[18:36:24] <b>CREATING</b> `MANDARINA` `Producto`</div>
<div class='progress-msg'>[18:36:24] Associate post `MANDARINA` with current import ...</div>
<div class='progress-msg'>[18:36:24] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:25] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:25] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:25] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `sku` updated with value `4bd7b7591bae`</div>
<div class='progress-msg'>[18:36:25] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:25] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:25] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:25] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:25] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:25] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:25] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:25] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:25] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:25] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:25] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MANDARINA.jpg` for `MANDARINA` ...</div>
<div class='progress-msg'>[18:36:25] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MANDARINA.jpg` by URL...</div>
<div class='progress-msg'>[18:36:25] - <b>WARNING</b>: Image MANDARINA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:25] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MANDARINA.jpg`</div>
<div class='progress-msg'>[18:36:25] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/MANDARINA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:25] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/MANDARINA.jpg`</div>
<div class='progress-msg'>[18:36:25] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:25] - Attachment with ID: `19139` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/MANDARINA.jpg`</div>
<div class='progress-msg'>[18:36:25] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:25] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:25] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:25] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:25] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:25] <b>CREATED</b> `MANDARINA` `Producto` (ID: 19138)</div>
<div class='progress-msg'>[18:36:25] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:25] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:25] ---</div>
<div class='progress-msg'>[18:36:25] Record #48</div>
<div class='progress-msg'>[18:36:25] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:25] Combine all data for post `LIMON`...</div>
<div class='progress-msg'>[18:36:25] Find corresponding article among database for post `LIMON`...</div>
<div class='progress-msg'>[18:36:25] Duplicate post wasn't found for post `LIMON`...</div>
<div class='progress-msg'>[18:36:25] Applying filter `pmxi_article_data` for `LIMON`</div>
<div class='progress-msg'>[18:36:25] <b>CREATING</b> `LIMON` `Producto`</div>
<div class='progress-msg'>[18:36:25] Associate post `LIMON` with current import ...</div>
<div class='progress-msg'>[18:36:25] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:25] Property `price` updated with value `0.2`</div>
<div class='progress-msg'>[18:36:25] Property `regular_price` updated with value `0.2`</div>
<div class='progress-msg'>[18:36:25] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:25] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:25] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `sku` updated with value `3bb0ac236ec2`</div>
<div class='progress-msg'>[18:36:25] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:25] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:25] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:25] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:25] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:25] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:25] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:25] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:25] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:25] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:26] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:26] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/UVAS-CON-SEMILLAS.jpg` for `LIMON` ...</div>
<div class='progress-msg'>[18:36:26] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/UVAS-CON-SEMILLAS.jpg` by URL...</div>
<div class='progress-msg'>[18:36:26] - <b>WARNING</b>: Image UVAS-CON-SEMILLAS.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:26] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/UVAS-CON-SEMILLAS.jpg`</div>
<div class='progress-msg'>[18:36:26] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/UVAS-CON-SEMILLAS.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:26] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/UVAS-CON-SEMILLAS.jpg`</div>
<div class='progress-msg'>[18:36:26] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:26] - Attachment with ID: `19141` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/UVAS-CON-SEMILLAS.jpg`</div>
<div class='progress-msg'>[18:36:26] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:26] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:26] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:26] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:26] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:26] <b>CREATED</b> `LIMON` `Producto` (ID: 19140)</div>
<div class='progress-msg'>[18:36:26] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:26] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:26] ---</div>
<div class='progress-msg'>[18:36:26] Record #49</div>
<div class='progress-msg'>[18:36:26] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:26] Combine all data for post `PLATANO VERDE Y MADURO`...</div>
<div class='progress-msg'>[18:36:26] Find corresponding article among database for post `PLATANO VERDE Y MADURO`...</div>
<div class='progress-msg'>[18:36:26] Duplicate post wasn't found for post `PLATANO VERDE Y MADURO`...</div>
<div class='progress-msg'>[18:36:26] Applying filter `pmxi_article_data` for `PLATANO VERDE Y MADURO`</div>
<div class='progress-msg'>[18:36:26] <b>CREATING</b> `PLATANO VERDE Y MADURO` `Producto`</div>
<div class='progress-msg'>[18:36:26] Associate post `PLATANO VERDE Y MADURO` with current import ...</div>
<div class='progress-msg'>[18:36:26] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:26] Property `price` updated with value `0.3`</div>
<div class='progress-msg'>[18:36:26] Property `regular_price` updated with value `0.3`</div>
<div class='progress-msg'>[18:36:26] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:26] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:26] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `sku` updated with value `81cfd7f64edc`</div>
<div class='progress-msg'>[18:36:26] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:26] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:26] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:26] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:26] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:26] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:26] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:26] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:26] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:26] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:26] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:26] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PLATANO-VERDE-Y-MADURO.jpeg` for `PLATANO VERDE Y MADURO` ...</div>
<div class='progress-msg'>[18:36:26] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PLATANO-VERDE-Y-MADURO.jpeg` by URL...</div>
<div class='progress-msg'>[18:36:26] - <b>WARNING</b>: Image PLATANO-VERDE-Y-MADURO.jpeg not found in media gallery.</div>
<div class='progress-msg'>[18:36:26] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PLATANO-VERDE-Y-MADURO.jpeg`</div>
<div class='progress-msg'>[18:36:26] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PLATANO-VERDE-Y-MADURO.jpeg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:26] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PLATANO-VERDE-Y-MADURO.jpeg`</div>
<div class='progress-msg'>[18:36:27] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:27] - Attachment with ID: `19143` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PLATANO-VERDE-Y-MADURO.jpeg`</div>
<div class='progress-msg'>[18:36:27] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:27] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:27] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:27] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:27] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:27] <b>CREATED</b> `PLATANO VERDE Y MADURO` `Producto` (ID: 19142)</div>
<div class='progress-msg'>[18:36:27] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:27] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:27] ---</div>
<div class='progress-msg'>[18:36:27] Record #50</div>
<div class='progress-msg'>[18:36:27] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:27] Combine all data for post `YUCA`...</div>
<div class='progress-msg'>[18:36:27] Find corresponding article among database for post `YUCA`...</div>
<div class='progress-msg'>[18:36:27] Duplicate post wasn't found for post `YUCA`...</div>
<div class='progress-msg'>[18:36:27] Applying filter `pmxi_article_data` for `YUCA`</div>
<div class='progress-msg'>[18:36:27] <b>CREATING</b> `YUCA` `Producto`</div>
<div class='progress-msg'>[18:36:27] Associate post `YUCA` with current import ...</div>
<div class='progress-msg'>[18:36:27] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:27] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:27] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:27] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `sku` updated with value `1a8e333aa4c2`</div>
<div class='progress-msg'>[18:36:27] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:27] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:27] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:27] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:27] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:27] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:27] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:27] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:27] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:27] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:27] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:27] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/YUCA.jpg` for `YUCA` ...</div>
<div class='progress-msg'>[18:36:27] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/YUCA.jpg` by URL...</div>
<div class='progress-msg'>[18:36:27] - <b>WARNING</b>: Image YUCA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:27] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/YUCA.jpg`</div>
<div class='progress-msg'>[18:36:27] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/YUCA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:27] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/YUCA.jpg`</div>
<div class='progress-msg'>[18:36:27] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:28] - Attachment with ID: `19145` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/YUCA.jpg`</div>
<div class='progress-msg'>[18:36:28] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:28] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:28] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:28] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:28] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:28] <b>CREATED</b> `YUCA` `Producto` (ID: 19144)</div>
<div class='progress-msg'>[18:36:28] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:28] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:28] ---</div>
<div class='progress-msg'>[18:36:28] Record #51</div>
<div class='progress-msg'>[18:36:28] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:28] Combine all data for post `AGUACATE`...</div>
<div class='progress-msg'>[18:36:28] Find corresponding article among database for post `AGUACATE`...</div>
<div class='progress-msg'>[18:36:28] Duplicate post wasn't found for post `AGUACATE`...</div>
<div class='progress-msg'>[18:36:28] Applying filter `pmxi_article_data` for `AGUACATE`</div>
<div class='progress-msg'>[18:36:28] <b>CREATING</b> `AGUACATE` `Producto`</div>
<div class='progress-msg'>[18:36:28] Associate post `AGUACATE` with current import ...</div>
<div class='progress-msg'>[18:36:28] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:28] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:28] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:28] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `sku` updated with value `9d579ad5319c`</div>
<div class='progress-msg'>[18:36:28] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:28] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:28] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:28] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:28] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:28] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:28] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:28] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:28] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:28] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:28] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:28] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/AGUACATE.png` for `AGUACATE` ...</div>
<div class='progress-msg'>[18:36:28] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/AGUACATE.png` by URL...</div>
<div class='progress-msg'>[18:36:28] - <b>WARNING</b>: Image AGUACATE.png not found in media gallery.</div>
<div class='progress-msg'>[18:36:28] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/AGUACATE.png`</div>
<div class='progress-msg'>[18:36:28] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/AGUACATE.png` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:28] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/AGUACATE.png`</div>
<div class='progress-msg'>[18:36:28] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:28] - Attachment with ID: `19147` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/AGUACATE.png`</div>
<div class='progress-msg'>[18:36:28] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:28] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:28] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:28] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:28] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:28] <b>CREATED</b> `AGUACATE` `Producto` (ID: 19146)</div>
<div class='progress-msg'>[18:36:29] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:29] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:29] ---</div>
<div class='progress-msg'>[18:36:29] Record #52</div>
<div class='progress-msg'>[18:36:29] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:29] Combine all data for post `GUINEO MADURO`...</div>
<div class='progress-msg'>[18:36:29] Find corresponding article among database for post `GUINEO MADURO`...</div>
<div class='progress-msg'>[18:36:29] Duplicate post wasn't found for post `GUINEO MADURO`...</div>
<div class='progress-msg'>[18:36:29] Applying filter `pmxi_article_data` for `GUINEO MADURO`</div>
<div class='progress-msg'>[18:36:29] <b>CREATING</b> `GUINEO MADURO` `Producto`</div>
<div class='progress-msg'>[18:36:29] Associate post `GUINEO MADURO` with current import ...</div>
<div class='progress-msg'>[18:36:29] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:29] Property `price` updated with value `0.25`</div>
<div class='progress-msg'>[18:36:29] Property `regular_price` updated with value `0.25`</div>
<div class='progress-msg'>[18:36:29] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:29] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:29] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `sku` updated with value `e54ea35589e1`</div>
<div class='progress-msg'>[18:36:29] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:29] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:29] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:29] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:29] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:29] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:29] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:29] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:29] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:29] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:29] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:29] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/GUINEO-MADURO.jpg` for `GUINEO MADURO` ...</div>
<div class='progress-msg'>[18:36:29] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/GUINEO-MADURO.jpg` by URL...</div>
<div class='progress-msg'>[18:36:29] - <b>WARNING</b>: Image GUINEO-MADURO.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:29] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/GUINEO-MADURO.jpg`</div>
<div class='progress-msg'>[18:36:29] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/GUINEO-MADURO.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:29] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/GUINEO-MADURO.jpg`</div>
<div class='progress-msg'>[18:36:29] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:29] - Attachment with ID: `19149` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/GUINEO-MADURO.jpg`</div>
<div class='progress-msg'>[18:36:29] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:29] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:29] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:29] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:29] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:29] <b>CREATED</b> `GUINEO MADURO` `Producto` (ID: 19148)</div>
<div class='progress-msg'>[18:36:29] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:29] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:29] ---</div>
<div class='progress-msg'>[18:36:29] Record #53</div>
<div class='progress-msg'>[18:36:29] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:29] Combine all data for post `PAPAS`...</div>
<div class='progress-msg'>[18:36:29] Find corresponding article among database for post `PAPAS`...</div>
<div class='progress-msg'>[18:36:29] Duplicate post wasn't found for post `PAPAS`...</div>
<div class='progress-msg'>[18:36:29] Applying filter `pmxi_article_data` for `PAPAS`</div>
<div class='progress-msg'>[18:36:29] <b>CREATING</b> `PAPAS` `Producto`</div>
<div class='progress-msg'>[18:36:29] Associate post `PAPAS` with current import ...</div>
<div class='progress-msg'>[18:36:29] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:30] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:30] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:30] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `sku` updated with value `c5d75b011549`</div>
<div class='progress-msg'>[18:36:30] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:30] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:30] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:30] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:30] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:30] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:30] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:30] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:30] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:30] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:30] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PAPAS.webp` for `PAPAS` ...</div>
<div class='progress-msg'>[18:36:30] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PAPAS.webp` by URL...</div>
<div class='progress-msg'>[18:36:30] - <b>WARNING</b>: Image PAPAS.webp not found in media gallery.</div>
<div class='progress-msg'>[18:36:30] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PAPAS.webp`</div>
<div class='progress-msg'>[18:36:30] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PAPAS.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:30] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PAPAS.webp`</div>
<div class='progress-msg'>[18:36:30] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:30] - Attachment with ID: `19151` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PAPAS.webp`</div>
<div class='progress-msg'>[18:36:30] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:30] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:30] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:30] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:30] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:30] <b>CREATED</b> `PAPAS` `Producto` (ID: 19150)</div>
<div class='progress-msg'>[18:36:30] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:30] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:30] ---</div>
<div class='progress-msg'>[18:36:30] Record #54</div>
<div class='progress-msg'>[18:36:30] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:30] Combine all data for post `OTOE`...</div>
<div class='progress-msg'>[18:36:30] Find corresponding article among database for post `OTOE`...</div>
<div class='progress-msg'>[18:36:30] Duplicate post wasn't found for post `OTOE`...</div>
<div class='progress-msg'>[18:36:30] Applying filter `pmxi_article_data` for `OTOE`</div>
<div class='progress-msg'>[18:36:30] <b>CREATING</b> `OTOE` `Producto`</div>
<div class='progress-msg'>[18:36:30] Associate post `OTOE` with current import ...</div>
<div class='progress-msg'>[18:36:30] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:30] Property `price` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `regular_price` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:30] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:30] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `sku` updated with value `2f64b85245b6`</div>
<div class='progress-msg'>[18:36:30] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:30] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:30] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:30] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:30] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:30] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:30] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:30] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:30] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:30] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:31] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:31] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/OTOE-e1719696344744.webp` for `OTOE` ...</div>
<div class='progress-msg'>[18:36:31] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/OTOE-e1719696344744.webp` by URL...</div>
<div class='progress-msg'>[18:36:31] - <b>WARNING</b>: Image OTOE-e1719696344744.webp not found in media gallery.</div>
<div class='progress-msg'>[18:36:31] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/OTOE-e1719696344744.webp`</div>
<div class='progress-msg'>[18:36:31] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/OTOE-e1719696344744.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:31] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/OTOE-e1719696344744.webp`</div>
<div class='progress-msg'>[18:36:31] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:31] - Attachment with ID: `19153` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/OTOE-e1719696344744.webp`</div>
<div class='progress-msg'>[18:36:31] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:31] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:31] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:31] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:31] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:31] <b>CREATED</b> `OTOE` `Producto` (ID: 19152)</div>
<div class='progress-msg'>[18:36:31] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:31] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:31] ---</div>
<div class='progress-msg'>[18:36:31] Record #55</div>
<div class='progress-msg'>[18:36:31] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:31] Combine all data for post `RASPADURA INDIVIDUAL ARTESANAL`...</div>
<div class='progress-msg'>[18:36:31] Find corresponding article among database for post `RASPADURA INDIVIDUAL ARTESANAL`...</div>
<div class='progress-msg'>[18:36:31] Duplicate post wasn't found for post `RASPADURA INDIVIDUAL ARTESANAL`...</div>
<div class='progress-msg'>[18:36:31] Applying filter `pmxi_article_data` for `RASPADURA INDIVIDUAL ARTESANAL`</div>
<div class='progress-msg'>[18:36:31] <b>CREATING</b> `RASPADURA INDIVIDUAL ARTESANAL` `Producto`</div>
<div class='progress-msg'>[18:36:31] Associate post `RASPADURA INDIVIDUAL ARTESANAL` with current import ...</div>
<div class='progress-msg'>[18:36:31] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:31] Property `price` updated with value `0.85`</div>
<div class='progress-msg'>[18:36:31] Property `regular_price` updated with value `0.85`</div>
<div class='progress-msg'>[18:36:31] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:31] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:31] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `sku` updated with value `44eb069fa572`</div>
<div class='progress-msg'>[18:36:31] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:31] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:31] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:31] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:31] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:31] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:31] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:31] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:31] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:31] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:32] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:32] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RASPADURA-INDIVIDUAL-ARTESANAL.jpeg` for `RASPADURA INDIVIDUAL ARTESANAL` ...</div>
<div class='progress-msg'>[18:36:32] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RASPADURA-INDIVIDUAL-ARTESANAL.jpeg` by URL...</div>
<div class='progress-msg'>[18:36:32] - <b>WARNING</b>: Image RASPADURA-INDIVIDUAL-ARTESANAL.jpeg not found in media gallery.</div>
<div class='progress-msg'>[18:36:32] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RASPADURA-INDIVIDUAL-ARTESANAL.jpeg`</div>
<div class='progress-msg'>[18:36:32] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RASPADURA-INDIVIDUAL-ARTESANAL.jpeg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:32] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/RASPADURA-INDIVIDUAL-ARTESANAL.jpeg`</div>
<div class='progress-msg'>[18:36:32] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:32] - Attachment with ID: `19155` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/RASPADURA-INDIVIDUAL-ARTESANAL.jpeg`</div>
<div class='progress-msg'>[18:36:32] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:32] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:32] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:32] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:32] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:32] <b>CREATED</b> `RASPADURA INDIVIDUAL ARTESANAL` `Producto` (ID: 19154)</div>
<div class='progress-msg'>[18:36:32] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:32] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:32] ---</div>
<div class='progress-msg'>[18:36:32] Record #56</div>
<div class='progress-msg'>[18:36:32] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:32] Combine all data for post `RASPADURA POR PAQUETE`...</div>
<div class='progress-msg'>[18:36:32] Find corresponding article among database for post `RASPADURA POR PAQUETE`...</div>
<div class='progress-msg'>[18:36:32] Duplicate post wasn't found for post `RASPADURA POR PAQUETE`...</div>
<div class='progress-msg'>[18:36:32] Applying filter `pmxi_article_data` for `RASPADURA POR PAQUETE`</div>
<div class='progress-msg'>[18:36:32] <b>CREATING</b> `RASPADURA POR PAQUETE` `Producto`</div>
<div class='progress-msg'>[18:36:32] Associate post `RASPADURA POR PAQUETE` with current import ...</div>
<div class='progress-msg'>[18:36:32] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:32] Property `price` updated with value `3`</div>
<div class='progress-msg'>[18:36:32] Property `regular_price` updated with value `3`</div>
<div class='progress-msg'>[18:36:32] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:32] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:32] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `sku` updated with value `4652a0a998b9`</div>
<div class='progress-msg'>[18:36:32] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:32] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:32] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:32] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:32] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:32] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:32] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:32] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:32] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:32] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:32] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:32] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RASPADURA-INDIVIDUAL-ARTESANAL.jpeg` for `RASPADURA POR PAQUETE` ...</div>
<div class='progress-msg'>[18:36:32] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/RASPADURA-INDIVIDUAL-ARTESANAL.jpeg` by URL...</div>
<div class='progress-msg'>[18:36:32] - Using existing image `RASPADURA-INDIVIDUAL-ARTESANAL.jpeg` for post `RASPADURA POR PAQUETE` ...</div>
<div class='progress-msg'>[18:36:32] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:32] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:32] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:32] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:32] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:32] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:32] <b>CREATED</b> `RASPADURA POR PAQUETE` `Producto` (ID: 19156)</div>
<div class='progress-msg'>[18:36:32] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:32] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:32] ---</div>
<div class='progress-msg'>[18:36:32] Record #57</div>
<div class='progress-msg'>[18:36:32] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:32] Combine all data for post `CEBOLLA BLANCA`...</div>
<div class='progress-msg'>[18:36:32] Find corresponding article among database for post `CEBOLLA BLANCA`...</div>
<div class='progress-msg'>[18:36:32] Duplicate post wasn't found for post `CEBOLLA BLANCA`...</div>
<div class='progress-msg'>[18:36:32] Applying filter `pmxi_article_data` for `CEBOLLA BLANCA`</div>
<div class='progress-msg'>[18:36:32] <b>CREATING</b> `CEBOLLA BLANCA` `Producto`</div>
<div class='progress-msg'>[18:36:33] Associate post `CEBOLLA BLANCA` with current import ...</div>
<div class='progress-msg'>[18:36:33] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:33] Property `price` updated with value `1.5`</div>
<div class='progress-msg'>[18:36:33] Property `regular_price` updated with value `1.5`</div>
<div class='progress-msg'>[18:36:33] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:33] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:33] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `sku` updated with value `d1e8cfffe4b3`</div>
<div class='progress-msg'>[18:36:33] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:33] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:33] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:33] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:33] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:33] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:33] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:33] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:33] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:33] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:33] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CEBOLLA-BLANCA.jpg` for `CEBOLLA BLANCA` ...</div>
<div class='progress-msg'>[18:36:33] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CEBOLLA-BLANCA.jpg` by URL...</div>
<div class='progress-msg'>[18:36:33] - <b>WARNING</b>: Image CEBOLLA-BLANCA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:33] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CEBOLLA-BLANCA.jpg`</div>
<div class='progress-msg'>[18:36:33] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CEBOLLA-BLANCA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:33] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CEBOLLA-BLANCA.jpg`</div>
<div class='progress-msg'>[18:36:33] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:33] - Attachment with ID: `19158` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CEBOLLA-BLANCA.jpg`</div>
<div class='progress-msg'>[18:36:33] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:33] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:33] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:33] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:33] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:33] <b>CREATED</b> `CEBOLLA BLANCA` `Producto` (ID: 19157)</div>
<div class='progress-msg'>[18:36:33] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:33] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:33] ---</div>
<div class='progress-msg'>[18:36:33] Record #58</div>
<div class='progress-msg'>[18:36:33] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:33] Combine all data for post `CEBOLLA MORADA`...</div>
<div class='progress-msg'>[18:36:33] Find corresponding article among database for post `CEBOLLA MORADA`...</div>
<div class='progress-msg'>[18:36:33] Duplicate post wasn't found for post `CEBOLLA MORADA`...</div>
<div class='progress-msg'>[18:36:33] Applying filter `pmxi_article_data` for `CEBOLLA MORADA`</div>
<div class='progress-msg'>[18:36:33] <b>CREATING</b> `CEBOLLA MORADA` `Producto`</div>
<div class='progress-msg'>[18:36:33] Associate post `CEBOLLA MORADA` with current import ...</div>
<div class='progress-msg'>[18:36:33] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:33] Property `price` updated with value `1.5`</div>
<div class='progress-msg'>[18:36:33] Property `regular_price` updated with value `1.5`</div>
<div class='progress-msg'>[18:36:33] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:33] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:33] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `sku` updated with value `efa6516d89c2`</div>
<div class='progress-msg'>[18:36:33] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:33] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:33] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:33] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:33] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:33] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:33] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:33] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:33] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:33] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:34] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:34] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CEBOLLA-MORADA.jpg` for `CEBOLLA MORADA` ...</div>
<div class='progress-msg'>[18:36:34] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CEBOLLA-MORADA.jpg` by URL...</div>
<div class='progress-msg'>[18:36:34] - <b>WARNING</b>: Image CEBOLLA-MORADA.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:34] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CEBOLLA-MORADA.jpg`</div>
<div class='progress-msg'>[18:36:34] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/CEBOLLA-MORADA.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:34] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CEBOLLA-MORADA.jpg`</div>
<div class='progress-msg'>[18:36:34] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:34] - Attachment with ID: `19160` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/CEBOLLA-MORADA.jpg`</div>
<div class='progress-msg'>[18:36:34] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:34] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:34] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:34] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:34] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:34] <b>CREATED</b> `CEBOLLA MORADA` `Producto` (ID: 19159)</div>
<div class='progress-msg'>[18:36:34] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:34] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:34] ---</div>
<div class='progress-msg'>[18:36:34] Record #59</div>
<div class='progress-msg'>[18:36:34] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:34] Combine all data for post `JENGIBRE`...</div>
<div class='progress-msg'>[18:36:34] Find corresponding article among database for post `JENGIBRE`...</div>
<div class='progress-msg'>[18:36:34] Duplicate post wasn't found for post `JENGIBRE`...</div>
<div class='progress-msg'>[18:36:34] Applying filter `pmxi_article_data` for `JENGIBRE`</div>
<div class='progress-msg'>[18:36:34] <b>CREATING</b> `JENGIBRE` `Producto`</div>
<div class='progress-msg'>[18:36:34] Associate post `JENGIBRE` with current import ...</div>
<div class='progress-msg'>[18:36:34] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:34] Property `price` updated with value `1.3`</div>
<div class='progress-msg'>[18:36:34] Property `regular_price` updated with value `1.3`</div>
<div class='progress-msg'>[18:36:34] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:34] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:34] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `sku` updated with value `25301b3dd0a4`</div>
<div class='progress-msg'>[18:36:34] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:34] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:34] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:34] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:34] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:34] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:34] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:34] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:34] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:34] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:34] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:34] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/JENGIBRE.webp` for `JENGIBRE` ...</div>
<div class='progress-msg'>[18:36:34] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/JENGIBRE.webp` by URL...</div>
<div class='progress-msg'>[18:36:34] - <b>WARNING</b>: Image JENGIBRE.webp not found in media gallery.</div>
<div class='progress-msg'>[18:36:34] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/JENGIBRE.webp`</div>
<div class='progress-msg'>[18:36:34] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/JENGIBRE.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:34] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/JENGIBRE.webp`</div>
<div class='progress-msg'>[18:36:35] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:35] - Attachment with ID: `19162` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/JENGIBRE.webp`</div>
<div class='progress-msg'>[18:36:35] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:35] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:35] - Attempted to create parent Producto product_cat `LEGUMBRES Y VERDURAS`, duplicate detected. Importing Producto to existing `LEGUMBRES Y VERDURAS` product_cat, ID 3102, slug `legumbres-y-verduras` ...</div>
<div class='progress-msg'>[18:36:35] - Attempted to create child Producto product_cat `VEGETALES/TUBERCULOS`, duplicate detected. Importing Producto to existing `VEGETALES/TUBERCULOS` product_cat, ID 3103, slug `vegetales-tuberculos` ...</div>
<div class='progress-msg'>[18:36:35] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:35] <b>CREATED</b> `JENGIBRE` `Producto` (ID: 19161)</div>
<div class='progress-msg'>[18:36:35] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:35] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:35] ---</div>
<div class='progress-msg'>[18:36:35] Record #60</div>
<div class='progress-msg'>[18:36:35] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:35] Combine all data for post `SIUMAI C/U`...</div>
<div class='progress-msg'>[18:36:35] Find corresponding article among database for post `SIUMAI C/U`...</div>
<div class='progress-msg'>[18:36:35] Duplicate post wasn't found for post `SIUMAI C/U`...</div>
<div class='progress-msg'>[18:36:35] Applying filter `pmxi_article_data` for `SIUMAI C/U`</div>
<div class='progress-msg'>[18:36:35] <b>CREATING</b> `SIUMAI C/U` `Producto`</div>
<div class='progress-msg'>[18:36:35] Associate post `SIUMAI C/U` with current import ...</div>
<div class='progress-msg'>[18:36:35] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:35] Property `price` updated with value `0.5`</div>
<div class='progress-msg'>[18:36:35] Property `regular_price` updated with value `0.5`</div>
<div class='progress-msg'>[18:36:35] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:35] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:35] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `sku` updated with value `94891f406f13`</div>
<div class='progress-msg'>[18:36:35] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:35] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:35] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:35] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:35] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:35] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:35] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:35] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:35] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:35] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:35] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:35] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/SIUMAI-POR-UNIDAD.jpg` for `SIUMAI C/U` ...</div>
<div class='progress-msg'>[18:36:35] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/SIUMAI-POR-UNIDAD.jpg` by URL...</div>
<div class='progress-msg'>[18:36:35] - <b>WARNING</b>: Image SIUMAI-POR-UNIDAD.jpg not found in media gallery.</div>
<div class='progress-msg'>[18:36:35] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/SIUMAI-POR-UNIDAD.jpg`</div>
<div class='progress-msg'>[18:36:35] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/SIUMAI-POR-UNIDAD.jpg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:35] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/SIUMAI-POR-UNIDAD.jpg`</div>
<div class='progress-msg'>[18:36:35] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:35] - Attachment with ID: `19164` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/SIUMAI-POR-UNIDAD.jpg`</div>
<div class='progress-msg'>[18:36:35] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:35] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:35] - Attempted to create parent Producto product_cat `CONGELADOS`, duplicate detected. Importing Producto to existing `CONGELADOS` product_cat, ID 3078, slug `congelados` ...</div>
<div class='progress-msg'>[18:36:35] - Attempted to create child Producto product_cat `CONGELADOS`, duplicate detected. Importing Producto to existing `CONGELADOS` product_cat, ID 3079, slug `congelados-congelados` ...</div>
<div class='progress-msg'>[18:36:35] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:35] <b>CREATED</b> `SIUMAI C/U` `Producto` (ID: 19163)</div>
<div class='progress-msg'>[18:36:35] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:35] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:36] ---</div>
<div class='progress-msg'>[18:36:36] Record #61</div>
<div class='progress-msg'>[18:36:36] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:36] Combine all data for post `PAQUETE DE QUESO 72 UNIDADES     NESTLE`...</div>
<div class='progress-msg'>[18:36:36] Find corresponding article among database for post `PAQUETE DE QUESO 72 UNIDADES     NESTLE`...</div>
<div class='progress-msg'>[18:36:36] Duplicate post wasn't found for post `PAQUETE DE QUESO 72 UNIDADES     NESTLE`...</div>
<div class='progress-msg'>[18:36:36] Applying filter `pmxi_article_data` for `PAQUETE DE QUESO 72 UNIDADES     NESTLE`</div>
<div class='progress-msg'>[18:36:36] <b>CREATING</b> `PAQUETE DE QUESO 72 UNIDADES     NESTLE` `Producto`</div>
<div class='progress-msg'>[18:36:36] Associate post `PAQUETE DE QUESO 72 UNIDADES     NESTLE` with current import ...</div>
<div class='progress-msg'>[18:36:36] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:36] Property `price` updated with value `12.5`</div>
<div class='progress-msg'>[18:36:36] Property `regular_price` updated with value `12.5`</div>
<div class='progress-msg'>[18:36:36] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:36] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:36] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `sku` updated with value `a5c1492d6aa5`</div>
<div class='progress-msg'>[18:36:36] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:36] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:36] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:36] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:36] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:36] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:36] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:36] Property `attributes` updated with value `a:1:{i:0;O:20:"WC_Product_Attribute":1:{s:7:"*data";a:6:{s:2:"id";i:11;s:4:"name";s:9:"pa_marcas";s:7:"options";a:1:{i:0;i:2492;}s:8:"position";i:1;s:7:"visible";b:1;s:9:"variation";b:1;}}}`</div>
<div class='progress-msg'>[18:36:36] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:36] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:37] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:37] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PAQUETE-DE-QUESO-72-UNIDADES-NESTLE.jpeg` for `PAQUETE DE QUESO 72 UNIDADES     NESTLE` ...</div>
<div class='progress-msg'>[18:36:37] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PAQUETE-DE-QUESO-72-UNIDADES-NESTLE.jpeg` by URL...</div>
<div class='progress-msg'>[18:36:37] - <b>WARNING</b>: Image PAQUETE-DE-QUESO-72-UNIDADES-NESTLE.jpeg not found in media gallery.</div>
<div class='progress-msg'>[18:36:37] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PAQUETE-DE-QUESO-72-UNIDADES-NESTLE.jpeg`</div>
<div class='progress-msg'>[18:36:37] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/PAQUETE-DE-QUESO-72-UNIDADES-NESTLE.jpeg` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:37] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PAQUETE-DE-QUESO-72-UNIDADES-NESTLE.jpeg`</div>
<div class='progress-msg'>[18:36:37] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:37] - Attachment with ID: `19166` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/PAQUETE-DE-QUESO-72-UNIDADES-NESTLE.jpeg`</div>
<div class='progress-msg'>[18:36:37] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:37] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:37] - Attempted to create parent Producto product_cat `LACTEOS Y DERIVADOS`, duplicate detected. Importing Producto to existing `LACTEOS Y DERIVADOS` product_cat, ID 2516, slug `lacteos-y-derivados` ...</div>
<div class='progress-msg'>[18:36:37] - Attempted to create child Producto product_cat `QUESOS`, duplicate detected. Importing Producto to existing `QUESOS` product_cat, ID 2802, slug `quesos` ...</div>
<div class='progress-msg'>[18:36:37] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:37] <b>CREATED</b> `PAQUETE DE QUESO 72 UNIDADES     NESTLE` `Producto` (ID: 19165)</div>
<div class='progress-msg'>[18:36:37] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:37] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:37] ---</div>
<div class='progress-msg'>[18:36:37] Record #62</div>
<div class='progress-msg'>[18:36:37] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:37] Combine all data for post `HIELO CHICO TROPICAL`...</div>
<div class='progress-msg'>[18:36:37] Find corresponding article among database for post `HIELO CHICO TROPICAL`...</div>
<div class='progress-msg'>[18:36:37] Duplicate post wasn't found for post `HIELO CHICO TROPICAL`...</div>
<div class='progress-msg'>[18:36:37] Applying filter `pmxi_article_data` for `HIELO CHICO TROPICAL`</div>
<div class='progress-msg'>[18:36:37] <b>CREATING</b> `HIELO CHICO TROPICAL` `Producto`</div>
<div class='progress-msg'>[18:36:37] Associate post `HIELO CHICO TROPICAL` with current import ...</div>
<div class='progress-msg'>[18:36:37] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:37] Property `price` updated with value `1.4`</div>
<div class='progress-msg'>[18:36:37] Property `regular_price` updated with value `1.4`</div>
<div class='progress-msg'>[18:36:37] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:37] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:37] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `sku` updated with value `a4972f8b841e`</div>
<div class='progress-msg'>[18:36:37] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:37] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:37] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:37] Property `stock_quantity` updated with value `30`</div>
<div class='progress-msg'>[18:36:37] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:37] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:37] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:37] Property `attributes` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:37] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:37] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:37] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:37] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:37] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:37] - Attempted to create parent Producto product_cat `DESPENSA`, duplicate detected. Importing Producto to existing `DESPENSA` product_cat, ID 2460, slug `despensa` ...</div>
<div class='progress-msg'>[18:36:37] - Creating child Producto product_cat for DESPENSA named `OTROS` ...</div>
<div class='progress-msg'>[18:36:37] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:37] <b>CREATED</b> `HIELO CHICO TROPICAL` `Producto` (ID: 19167)</div>
<div class='progress-msg'>[18:36:37] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:37] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:37] ---</div>
<div class='progress-msg'>[18:36:37] Record #63</div>
<div class='progress-msg'>[18:36:37] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:37] Combine all data for post `FLAUTA C/U`...</div>
<div class='progress-msg'>[18:36:37] Find corresponding article among database for post `FLAUTA C/U`...</div>
<div class='progress-msg'>[18:36:37] Duplicate post wasn't found for post `FLAUTA C/U`...</div>
<div class='progress-msg'>[18:36:37] Applying filter `pmxi_article_data` for `FLAUTA C/U`</div>
<div class='progress-msg'>[18:36:37] <b>CREATING</b> `FLAUTA C/U` `Producto`</div>
<div class='progress-msg'>[18:36:38] Associate post `FLAUTA C/U` with current import ...</div>
<div class='progress-msg'>[18:36:38] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:38] Property `price` updated with value `0.55`</div>
<div class='progress-msg'>[18:36:38] Property `regular_price` updated with value `0.55`</div>
<div class='progress-msg'>[18:36:38] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:38] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:38] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `sku` updated with value `f2d0b303d8c4`</div>
<div class='progress-msg'>[18:36:38] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:38] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:38] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:38] Property `stock_quantity` updated with value `20`</div>
<div class='progress-msg'>[18:36:38] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:38] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:38] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:38] Property `attributes` updated with value `a:1:{i:0;O:20:"WC_Product_Attribute":1:{s:7:"*data";a:6:{s:2:"id";i:11;s:4:"name";s:9:"pa_marcas";s:7:"options";a:1:{i:0;i:3105;}s:8:"position";i:1;s:7:"visible";b:1;s:9:"variation";b:1;}}}`</div>
<div class='progress-msg'>[18:36:38] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:38] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:38] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:38] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:38] - Attempted to create parent Producto product_cat `DESPENSA`, duplicate detected. Importing Producto to existing `DESPENSA` product_cat, ID 2460, slug `despensa` ...</div>
<div class='progress-msg'>[18:36:38] - Attempted to create child Producto product_cat `PANES/DULCES`, duplicate detected. Importing Producto to existing `PANES/DULCES` product_cat, ID 2784, slug `panes-dulces` ...</div>
<div class='progress-msg'>[18:36:38] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:38] <b>CREATED</b> `FLAUTA C/U` `Producto` (ID: 19168)</div>
<div class='progress-msg'>[18:36:38] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:38] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:38] ---</div>
<div class='progress-msg'>[18:36:38] Record #64</div>
<div class='progress-msg'>[18:36:38] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:38] Combine all data for post `GARRAFON DE AGUA PURISIMA`...</div>
<div class='progress-msg'>[18:36:38] Find corresponding article among database for post `GARRAFON DE AGUA PURISIMA`...</div>
<div class='progress-msg'>[18:36:38] Duplicate post wasn't found for post `GARRAFON DE AGUA PURISIMA`...</div>
<div class='progress-msg'>[18:36:38] Applying filter `pmxi_article_data` for `GARRAFON DE AGUA PURISIMA`</div>
<div class='progress-msg'>[18:36:38] <b>CREATING</b> `GARRAFON DE AGUA PURISIMA` `Producto`</div>
<div class='progress-msg'>[18:36:38] Associate post `GARRAFON DE AGUA PURISIMA` with current import ...</div>
<div class='progress-msg'>[18:36:38] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:38] Property `price` updated with value `5`</div>
<div class='progress-msg'>[18:36:38] Property `regular_price` updated with value `5`</div>
<div class='progress-msg'>[18:36:38] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:38] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:38] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `sku` updated with value `72d7b08456c4`</div>
<div class='progress-msg'>[18:36:38] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:38] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:38] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:38] Property `stock_quantity` updated with value `8`</div>
<div class='progress-msg'>[18:36:38] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:38] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:38] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:38] Property `attributes` updated with value `a:1:{i:0;O:20:"WC_Product_Attribute":1:{s:7:"*data";a:6:{s:2:"id";i:11;s:4:"name";s:9:"pa_marcas";s:7:"options";a:1:{i:0;i:3106;}s:8:"position";i:1;s:7:"visible";b:1;s:9:"variation";b:1;}}}`</div>
<div class='progress-msg'>[18:36:38] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:38] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:39] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:39] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/GARRAFON-DE-AGUA-PURISIMA.png` for `GARRAFON DE AGUA PURISIMA` ...</div>
<div class='progress-msg'>[18:36:39] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/GARRAFON-DE-AGUA-PURISIMA.png` by URL...</div>
<div class='progress-msg'>[18:36:39] - <b>WARNING</b>: Image GARRAFON-DE-AGUA-PURISIMA.png not found in media gallery.</div>
<div class='progress-msg'>[18:36:39] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/GARRAFON-DE-AGUA-PURISIMA.png`</div>
<div class='progress-msg'>[18:36:39] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/GARRAFON-DE-AGUA-PURISIMA.png` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:39] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/GARRAFON-DE-AGUA-PURISIMA.png`</div>
<div class='progress-msg'>[18:36:39] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:39] - Attachment with ID: `19170` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/GARRAFON-DE-AGUA-PURISIMA.png`</div>
<div class='progress-msg'>[18:36:39] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:39] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:39] - Attempted to create parent Producto product_cat `DESPENSA`, duplicate detected. Importing Producto to existing `DESPENSA` product_cat, ID 2460, slug `despensa` ...</div>
<div class='progress-msg'>[18:36:39] - Creating child Producto product_cat for DESPENSA named `AGUA/SABORIZADAS` ...</div>
<div class='progress-msg'>[18:36:39] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:39] <b>CREATED</b> `GARRAFON DE AGUA PURISIMA` `Producto` (ID: 19169)</div>
<div class='progress-msg'>[18:36:39] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:39] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:39] ---</div>
<div class='progress-msg'>[18:36:39] Record #65</div>
<div class='progress-msg'>[18:36:39] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:39] Combine all data for post `GARRAFON DE AGUA CRISTALINA`...</div>
<div class='progress-msg'>[18:36:39] Find corresponding article among database for post `GARRAFON DE AGUA CRISTALINA`...</div>
<div class='progress-msg'>[18:36:39] Duplicate post wasn't found for post `GARRAFON DE AGUA CRISTALINA`...</div>
<div class='progress-msg'>[18:36:39] Applying filter `pmxi_article_data` for `GARRAFON DE AGUA CRISTALINA`</div>
<div class='progress-msg'>[18:36:39] <b>CREATING</b> `GARRAFON DE AGUA CRISTALINA` `Producto`</div>
<div class='progress-msg'>[18:36:39] Associate post `GARRAFON DE AGUA CRISTALINA` with current import ...</div>
<div class='progress-msg'>[18:36:39] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:39] Property `price` updated with value `5`</div>
<div class='progress-msg'>[18:36:39] Property `regular_price` updated with value `5`</div>
<div class='progress-msg'>[18:36:39] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:39] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:39] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `sku` updated with value `c50dcfa6c8f3`</div>
<div class='progress-msg'>[18:36:39] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:39] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:39] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:39] Property `stock_quantity` updated with value `8`</div>
<div class='progress-msg'>[18:36:39] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:39] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:39] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:39] Property `attributes` updated with value `a:1:{i:0;O:20:"WC_Product_Attribute":1:{s:7:"*data";a:6:{s:2:"id";i:11;s:4:"name";s:9:"pa_marcas";s:7:"options";a:1:{i:0;i:2828;}s:8:"position";i:1;s:7:"visible";b:1;s:9:"variation";b:1;}}}`</div>
<div class='progress-msg'>[18:36:39] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:39] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:40] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:40] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/GARRAFON-DE-AGUA-CRISTALINA.png` for `GARRAFON DE AGUA CRISTALINA` ...</div>
<div class='progress-msg'>[18:36:40] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/GARRAFON-DE-AGUA-CRISTALINA.png` by URL...</div>
<div class='progress-msg'>[18:36:40] - <b>WARNING</b>: Image GARRAFON-DE-AGUA-CRISTALINA.png not found in media gallery.</div>
<div class='progress-msg'>[18:36:40] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/GARRAFON-DE-AGUA-CRISTALINA.png`</div>
<div class='progress-msg'>[18:36:40] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/GARRAFON-DE-AGUA-CRISTALINA.png` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:40] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/GARRAFON-DE-AGUA-CRISTALINA.png`</div>
<div class='progress-msg'>[18:36:40] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:40] - Attachment with ID: `19172` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/GARRAFON-DE-AGUA-CRISTALINA.png`</div>
<div class='progress-msg'>[18:36:40] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:40] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:40] - Attempted to create parent Producto product_cat `DESPENSA`, duplicate detected. Importing Producto to existing `DESPENSA` product_cat, ID 2460, slug `despensa` ...</div>
<div class='progress-msg'>[18:36:40] - Attempted to create child Producto product_cat `AGUA/SABORIZADAS`, duplicate detected. Importing Producto to existing `AGUA/SABORIZADAS` product_cat, ID 3107, slug `agua-saborizadas-despensa` ...</div>
<div class='progress-msg'>[18:36:40] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:40] <b>CREATED</b> `GARRAFON DE AGUA CRISTALINA` `Producto` (ID: 19171)</div>
<div class='progress-msg'>[18:36:40] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:40] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:40] ---</div>
<div class='progress-msg'>[18:36:40] Record #66</div>
<div class='progress-msg'>[18:36:40] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:40] Combine all data for post `TANQUE DE GAS PANAGAS DE 25 LIBRAS`...</div>
<div class='progress-msg'>[18:36:40] Find corresponding article among database for post `TANQUE DE GAS PANAGAS DE 25 LIBRAS`...</div>
<div class='progress-msg'>[18:36:40] Duplicate post wasn't found for post `TANQUE DE GAS PANAGAS DE 25 LIBRAS`...</div>
<div class='progress-msg'>[18:36:40] Applying filter `pmxi_article_data` for `TANQUE DE GAS PANAGAS DE 25 LIBRAS`</div>
<div class='progress-msg'>[18:36:40] <b>CREATING</b> `TANQUE DE GAS PANAGAS DE 25 LIBRAS` `Producto`</div>
<div class='progress-msg'>[18:36:40] Associate post `TANQUE DE GAS PANAGAS DE 25 LIBRAS` with current import ...</div>
<div class='progress-msg'>[18:36:40] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:40] Property `price` updated with value `4.65`</div>
<div class='progress-msg'>[18:36:40] Property `regular_price` updated with value `4.65`</div>
<div class='progress-msg'>[18:36:40] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:40] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:40] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `sku` updated with value `0371c5bc5f2e`</div>
<div class='progress-msg'>[18:36:40] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:40] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:40] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:40] Property `stock_quantity` updated with value `8`</div>
<div class='progress-msg'>[18:36:40] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:40] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:40] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:40] Property `attributes` updated with value `a:1:{i:0;O:20:"WC_Product_Attribute":1:{s:7:"*data";a:6:{s:2:"id";i:11;s:4:"name";s:9:"pa_marcas";s:7:"options";a:1:{i:0;i:3108;}s:8:"position";i:1;s:7:"visible";b:1;s:9:"variation";b:1;}}}`</div>
<div class='progress-msg'>[18:36:40] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:40] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:41] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:41] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TANQUE-DE-GAS-PANAGAS-DE-25-LIBRAS.webp` for `TANQUE DE GAS PANAGAS DE 25 LIBRAS` ...</div>
<div class='progress-msg'>[18:36:41] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TANQUE-DE-GAS-PANAGAS-DE-25-LIBRAS.webp` by URL...</div>
<div class='progress-msg'>[18:36:41] - <b>WARNING</b>: Image TANQUE-DE-GAS-PANAGAS-DE-25-LIBRAS.webp not found in media gallery.</div>
<div class='progress-msg'>[18:36:41] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TANQUE-DE-GAS-PANAGAS-DE-25-LIBRAS.webp`</div>
<div class='progress-msg'>[18:36:41] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TANQUE-DE-GAS-PANAGAS-DE-25-LIBRAS.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:41] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/TANQUE-DE-GAS-PANAGAS-DE-25-LIBRAS.webp`</div>
<div class='progress-msg'>[18:36:41] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:41] - Attachment with ID: `19174` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/TANQUE-DE-GAS-PANAGAS-DE-25-LIBRAS.webp`</div>
<div class='progress-msg'>[18:36:41] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:41] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:41] - Attempted to create parent Producto product_cat `DESPENSA`, duplicate detected. Importing Producto to existing `DESPENSA` product_cat, ID 2460, slug `despensa` ...</div>
<div class='progress-msg'>[18:36:41] - Attempted to create child Producto product_cat `OTROS`, duplicate detected. Importing Producto to existing `OTROS` product_cat, ID 3104, slug `otros-despensa` ...</div>
<div class='progress-msg'>[18:36:41] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:41] <b>CREATED</b> `TANQUE DE GAS PANAGAS DE 25 LIBRAS` `Producto` (ID: 19173)</div>
<div class='progress-msg'>[18:36:41] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:41] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:41] ---</div>
<div class='progress-msg'>[18:36:41] Record #67</div>
<div class='progress-msg'>[18:36:41] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:41] Combine all data for post `TANQUE DE GAS TROPIGAS DE 25 LIBRAS`...</div>
<div class='progress-msg'>[18:36:41] Find corresponding article among database for post `TANQUE DE GAS TROPIGAS DE 25 LIBRAS`...</div>
<div class='progress-msg'>[18:36:41] Duplicate post wasn't found for post `TANQUE DE GAS TROPIGAS DE 25 LIBRAS`...</div>
<div class='progress-msg'>[18:36:41] Applying filter `pmxi_article_data` for `TANQUE DE GAS TROPIGAS DE 25 LIBRAS`</div>
<div class='progress-msg'>[18:36:41] <b>CREATING</b> `TANQUE DE GAS TROPIGAS DE 25 LIBRAS` `Producto`</div>
<div class='progress-msg'>[18:36:41] Associate post `TANQUE DE GAS TROPIGAS DE 25 LIBRAS` with current import ...</div>
<div class='progress-msg'>[18:36:41] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:41] Property `price` updated with value `4.65`</div>
<div class='progress-msg'>[18:36:41] Property `regular_price` updated with value `4.65`</div>
<div class='progress-msg'>[18:36:41] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:41] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:41] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `sku` updated with value `14a978095f6a`</div>
<div class='progress-msg'>[18:36:41] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:41] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:41] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:41] Property `stock_quantity` updated with value `8`</div>
<div class='progress-msg'>[18:36:41] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:41] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:41] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:41] Property `attributes` updated with value `a:1:{i:0;O:20:"WC_Product_Attribute":1:{s:7:"*data";a:6:{s:2:"id";i:11;s:4:"name";s:9:"pa_marcas";s:7:"options";a:1:{i:0;i:3109;}s:8:"position";i:1;s:7:"visible";b:1;s:9:"variation";b:1;}}}`</div>
<div class='progress-msg'>[18:36:41] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:41] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:42] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:42] - Importing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TANQUE-DE-GAS-TROPIGAS-DE-25-LIBRAS.webp` for `TANQUE DE GAS TROPIGAS DE 25 LIBRAS` ...</div>
<div class='progress-msg'>[18:36:42] - Searching for existing image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TANQUE-DE-GAS-TROPIGAS-DE-25-LIBRAS.webp` by URL...</div>
<div class='progress-msg'>[18:36:42] - <b>WARNING</b>: Image TANQUE-DE-GAS-TROPIGAS-DE-25-LIBRAS.webp not found in media gallery.</div>
<div class='progress-msg'>[18:36:42] - Downloading image from `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TANQUE-DE-GAS-TROPIGAS-DE-25-LIBRAS.webp`</div>
<div class='progress-msg'>[18:36:42] - Image `https://develop1.webstudiopanama.com/super/wp-content/uploads/2024/06/TANQUE-DE-GAS-TROPIGAS-DE-25-LIBRAS.webp` has been successfully downloaded</div>
<div class='progress-msg'>[18:36:42] - Creating an attachment for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/TANQUE-DE-GAS-TROPIGAS-DE-25-LIBRAS.webp`</div>
<div class='progress-msg'>[18:36:42] - <b>ACTION</b>: pmxi_gallery_image</div>
<div class='progress-msg'>[18:36:42] - Attachment with ID: `19176` has been successfully created for image `https://develop1.webstudiopanama.com/caobos/wp-content/uploads/TANQUE-DE-GAS-TROPIGAS-DE-25-LIBRAS.webp`</div>
<div class='progress-msg'>[18:36:42] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:42] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:42] - Attempted to create parent Producto product_cat `DESPENSA`, duplicate detected. Importing Producto to existing `DESPENSA` product_cat, ID 2460, slug `despensa` ...</div>
<div class='progress-msg'>[18:36:42] - Attempted to create child Producto product_cat `OTROS`, duplicate detected. Importing Producto to existing `OTROS` product_cat, ID 3104, slug `otros-despensa` ...</div>
<div class='progress-msg'>[18:36:42] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:42] <b>CREATED</b> `TANQUE DE GAS TROPIGAS DE 25 LIBRAS` `Producto` (ID: 19175)</div>
<div class='progress-msg'>[18:36:42] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:42] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:42] ---</div>
<div class='progress-msg'>[18:36:42] Record #68</div>
<div class='progress-msg'>[18:36:42] <b>ACTION</b>: pmxi_before_post_import ...</div>
<div class='progress-msg'>[18:36:42] Combine all data for post `PAN BOLITA`...</div>
<div class='progress-msg'>[18:36:42] Find corresponding article among database for post `PAN BOLITA`...</div>
<div class='progress-msg'>[18:36:42] Duplicate post wasn't found for post `PAN BOLITA`...</div>
<div class='progress-msg'>[18:36:42] Applying filter `pmxi_article_data` for `PAN BOLITA`</div>
<div class='progress-msg'>[18:36:42] <b>CREATING</b> `PAN BOLITA` `Producto`</div>
<div class='progress-msg'>[18:36:42] Associate post `PAN BOLITA` with current import ...</div>
<div class='progress-msg'>[18:36:42] <strong>WooCommerce ADD-ON:</strong></div>
<div class='progress-msg'>[18:36:42] Property `price` updated with value `1`</div>
<div class='progress-msg'>[18:36:42] Property `regular_price` updated with value `1`</div>
<div class='progress-msg'>[18:36:42] Property `sale_price` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `date_on_sale_from` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `date_on_sale_to` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `downloadable` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `virtual` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `featured` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `catalog_visibility` updated with value `visible`</div>
<div class='progress-msg'>[18:36:42] Property `tax_status` updated with value `none`</div>
<div class='progress-msg'>[18:36:42] Property `tax_class` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `sku` updated with value `e9a08572f6ed`</div>
<div class='progress-msg'>[18:36:42] Property `manage_stock` updated with value `1`</div>
<div class='progress-msg'>[18:36:42] Property `backorders` updated with value `no`</div>
<div class='progress-msg'>[18:36:42] Property `stock_status` updated with value `instock`</div>
<div class='progress-msg'>[18:36:42] Property `stock_quantity` updated with value `0`</div>
<div class='progress-msg'>[18:36:42] Property `sold_individually` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `low_stock_amount` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `shipping_class_id` updated with value `1`</div>
<div class='progress-msg'>[18:36:42] Property `weight` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `length` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `width` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `height` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `upsell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:42] Property `cross_sell_ids` updated with value `a:0:{}`</div>
<div class='progress-msg'>[18:36:42] Property `attributes` updated with value `a:1:{i:0;O:20:"WC_Product_Attribute":1:{s:7:"*data";a:6:{s:2:"id";i:11;s:4:"name";s:9:"pa_marcas";s:7:"options";a:1:{i:0;i:3105;}s:8:"position";i:1;s:7:"visible";b:1;s:9:"variation";b:1;}}}`</div>
<div class='progress-msg'>[18:36:42] Property `purchase_note` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `reviews_allowed` updated with value ``</div>
<div class='progress-msg'>[18:36:42] Property `menu_order` updated with value `0`</div>
<div class='progress-msg'>[18:36:43] <b>IMAGES:</b></div>
<div class='progress-msg'>[18:36:43] <b>TAXONOMIES:</b></div>
<div class='progress-msg'>[18:36:43] - Importing taxonomy `product_cat` ...</div>
<div class='progress-msg'>[18:36:43] - Attempted to create parent Producto product_cat `DESPENSA`, duplicate detected. Importing Producto to existing `DESPENSA` product_cat, ID 2460, slug `despensa` ...</div>
<div class='progress-msg'>[18:36:43] - Attempted to create child Producto product_cat `PANES/DULCES`, duplicate detected. Importing Producto to existing `PANES/DULCES` product_cat, ID 2784, slug `panes-dulces` ...</div>
<div class='progress-msg'>[18:36:43] - Importing taxonomy `product_tag` ...</div>
<div class='progress-msg'>[18:36:43] <b>CREATED</b> `PAN BOLITA` `Producto` (ID: 19177)</div>
<div class='progress-msg'>[18:36:43] <b>ACTION</b>: pmxi_saved_post</div>
<div class='progress-msg'>[18:36:43] <b>ACTION</b>: pmxi_after_post_import</div>
<div class='progress-msg'>[18:36:43] Cleaning temporary data...</div>
