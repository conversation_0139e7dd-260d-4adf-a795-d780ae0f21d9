msgid ""
msgstr ""
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: POEditor.com\n"
"Project-Id-Version: WP All Import Pro\n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: actions/admin_menu.php:12
msgid "New Import"
msgstr "新規インポート"

#: actions/admin_menu.php:13 views/admin/import/process.php:71
#: views/admin/manage/index.php:5
msgid "Manage Imports"
msgstr "インポートを管理"

#: actions/admin_menu.php:14 views/admin/settings/index.php:7
msgid "Settings"
msgstr "設定"

#: actions/admin_menu.php:16
msgid "History"
msgstr "履歴"

#: actions/admin_menu.php:22 controllers/admin/license.php:18
#: controllers/admin/settings.php:50 views/admin/import/confirm.php:11
#: views/admin/import/element.php:8 views/admin/import/index.php:43
#: views/admin/import/options.php:18 views/admin/import/process.php:8
#: views/admin/import/template.php:9 views/admin/manage/index.php:4
#: views/admin/settings/index.php:6
msgid "WP All Import"
msgstr "WP オールインポート"

#: actions/admin_menu.php:22
msgid "All Import"
msgstr "すべてインポート"

#: actions/admin_notices.php:12
msgid "<b>%s Plugin</b>: Please update your WP All Import WooCommerce add-on to the latest version"
msgstr "<b>%s Plugin</b>: 最新バージョンへのアドオンWooCommerce WPすべてのインポートのためにを更新してください。"

#: actions/admin_notices.php:39
msgid "<b>%s Plugin</b>: Please update your WP All Import ACF add-on to the latest version"
msgstr "<b>%s Plugin</b>: WP All Import ACFアドオンを最新バージョンに更新してください"

#: actions/admin_notices.php:56
msgid "<b>%s Plugin</b>: Please update your WP All Import Linkcloak add-on to the latest version"
msgstr "<b>%s Plugin</b>: WP All Import Linkcloakアドオンを最新バージョンに更新してください"

#: actions/admin_notices.php:73
msgid "<b>%s Plugin</b>: Please update your WP All Import User add-on to the latest version"
msgstr "<b>%s Plugin</b>: WP All Import Userアドオンを最新バージョンに更新してください"

#: actions/admin_notices.php:90
msgid "<b>%s Plugin</b>: The WPML Add-On Plugin is no longer compatible with this version of WP All Import - <NAME_EMAIL> and we will supply the latest version of WP All Import that is compatible with the WPML Add-On."
msgstr "<b>%s Plugin</b>: WPMLアドオンプラグインは、このバージョンのWP All Importとの互換性がなくなりました。***********************までご連絡ください。WPMLアドオンと互換性のあるWP All Importの最新バージョンが提供されます。"

#: actions/admin_notices.php:119 controllers/admin/import.php:1547
#: controllers/admin/import.php:1789 controllers/admin/import.php:2339
msgid "<strong>Warning:</strong> your title is blank."
msgstr "<strong>警告:</strong> あなたのタイトルは空白です。"

#: actions/admin_notices.php:122 controllers/admin/import.php:1554
msgid "<strong>Warning:</strong> your content is blank."
msgstr "<strong>警告: </strong> あなたのコンテンツは空白です。"

#: actions/wp_ajax_auto_detect_cf.php:5 actions/wp_ajax_auto_detect_cf.php:9
#: actions/wp_ajax_auto_detect_sf.php:5 actions/wp_ajax_auto_detect_sf.php:9
#: actions/wp_ajax_delete_import.php:5 actions/wp_ajax_delete_import.php:9
#: actions/wp_ajax_dismiss_notifications.php:5
#: actions/wp_ajax_dismiss_notifications.php:9
#: actions/wp_ajax_get_bundle_post_type.php:6
#: actions/wp_ajax_get_bundle_post_type.php:10
#: actions/wp_ajax_import_failed.php:5 actions/wp_ajax_import_failed.php:9
#: actions/wp_ajax_nested_merge.php:6 actions/wp_ajax_nested_merge.php:10
#: actions/wp_ajax_nested_xpath.php:6 actions/wp_ajax_nested_xpath.php:10
#: actions/wp_ajax_parse_nested_file.php:10
#: actions/wp_ajax_parse_nested_file.php:14
#: actions/wp_ajax_save_import_functions.php:6
#: actions/wp_ajax_save_import_functions.php:10
#: actions/wp_ajax_test_images.php:6 actions/wp_ajax_test_images.php:10
#: actions/wp_ajax_unmerge_file.php:5 actions/wp_ajax_unmerge_file.php:9
#: actions/wp_ajax_upload_resource.php:6 actions/wp_ajax_upload_resource.php:10
#: controllers/admin/history.php:74 controllers/admin/import.php:591
#: controllers/admin/import.php:899 controllers/admin/import.php:1037
#: controllers/admin/import.php:1177 controllers/admin/import.php:1333
#: controllers/admin/import.php:2589 controllers/admin/manage.php:136
#: controllers/admin/manage.php:179 controllers/admin/manage.php:293
#: controllers/admin/manage.php:545 controllers/admin/settings.php:427
msgid "Security check"
msgstr "セキュリティーチェック"

#: actions/wp_ajax_auto_detect_cf.php:71 models/import/record.php:1572
#: views/admin/import/element.php:21
#: views/admin/import/options/_reimport_taxonomies_options.php:8
#: views/admin/import/options/_reimport_taxonomies_template.php:7
#: views/admin/import/process.php:40
#: views/admin/import/template/_custom_fields_template.php:7
#: views/admin/import/template/_other_template.php:6
#: views/admin/import/template/_term_meta_template.php:7
#: views/admin/import/template/_term_other_template.php:7
msgid "Taxonomy Term"
msgstr "タクソノミータームをセット"

#: actions/wp_ajax_auto_detect_cf.php:77
msgid "No Custom Fields are present in your database for %s"
msgstr "%sのデータベースにカスタムフィールドがありません"

#: actions/wp_ajax_auto_detect_cf.php:80
msgid "%s field was automatically detected."
msgstr "%s のフィールドが自動的に検出されました。"

#: actions/wp_ajax_auto_detect_cf.php:82
msgid "%s fields were automatically detected."
msgstr "%s のフィールドが自動的に検出されました。"

#: actions/wp_ajax_delete_import.php:32 controllers/admin/manage.php:608
msgid "Import deleted"
msgstr "インポート削除"

#: actions/wp_ajax_delete_import.php:36 controllers/admin/manage.php:612
msgid "All associated posts deleted."
msgstr "関連記事を全て削除します。"

#: actions/wp_ajax_delete_import.php:40 controllers/admin/manage.php:616
msgid "Import and all associated posts deleted."
msgstr "インポートと削除されたすべての関連記事。"

#: actions/wp_ajax_delete_import.php:44 controllers/admin/manage.php:597
msgid "Nothing to delete."
msgstr "何も削除します。"

#: actions/wp_ajax_delete_import.php:66
msgid "Import #%d - %d records deleted"
msgstr "インポート＃%d - %dのレコードが削除されました"

#: actions/wp_ajax_get_bundle_post_type.php:73
#: actions/wp_ajax_upload_resource.php:141 controllers/admin/settings.php:610
msgid "<p class=\"wpallimport-bundle-notice\">The import bundle you are using requires WooCommerce.</p><a class=\"upgrade_link\" href=\"https://wordpress.org/plugins/woocommerce/\" target=\"_blank\">Get WooCommerce</a>."
msgstr "<p class=\"wpallimport-bundle-notice\">使用しているインポート バンドルには、WooCommerce が必要です。</p><a class=\"upgrade_link\" href=\"https://wordpress.org/plugins/woocommerce/\" target=\"_blank\">WooCommerce を取得します。</a>"

#: actions/wp_ajax_get_bundle_post_type.php:79
#: actions/wp_ajax_upload_resource.php:147 controllers/admin/settings.php:616
msgid "<p class=\"wpallimport-bundle-notice\">The import bundle you are using requires the Pro version of the WooCommerce Add-On.</p><a href=\"https://www.wpallimport.com/checkout/?edd_action=add_to_cart&download_id=2707227&edd_options%5Bprice_id%5D=1\" class=\"upgrade_link\" target=\"_blank\">Purchase the WooCommerce Add-On</a>."
msgstr "<p class=\"wpallimport-bundle-notice\">使用しているインポート バンドル WooCommerce アドオンの Pro バージョンが必要です。</p><a href=\"https://www.wpallimport.com/checkout/?edd_action=add_to_cart&download_id=2707227&edd_options%5Bprice_id%5D=1\" class=\"upgrade_link\" target=\"_blank\">WooCommerce アドオンの購入</a>。"

#: actions/wp_ajax_get_bundle_post_type.php:84
#: actions/wp_ajax_upload_resource.php:152 controllers/admin/settings.php:621
msgid "<p class=\"wpallimport-bundle-notice\">The import bundle you are using requires the Pro version of the WooCommerce Add-On, but you have the free version installed.</p><a href=\"https://www.wpallimport.com/checkout/?edd_action=add_to_cart&download_id=2707227&edd_options%5Bprice_id%5D=1\" target=\"_blank\" class=\"upgrade_link\">Purchase the WooCommerce Add-On</a>."
msgstr "<p class=\"wpallimport-bundle-notice\">使用しているインポート バンドル WooCommerce アドオンの Pro バージョンが必要ですが無料版をインストールします。</p><a href=\"https://www.wpallimport.com/checkout/?edd_action=add_to_cart&download_id=2707227&edd_options%5Bprice_id%5D=1\" target=\"_blank\" class=\"upgrade_link\">WooCommerce アドオンの購入</a>。"

#: actions/wp_ajax_get_bundle_post_type.php:94
#: actions/wp_ajax_upload_resource.php:162 controllers/admin/settings.php:631
msgid "<p class=\"wpallimport-bundle-notice\">The import bundle you are using requires the User Add-On.</p><a href=\"https://www.wpallimport.com/checkout/?edd_action=add_to_cart&download_id=2707221&edd_options%5Bprice_id%5D=1\" target=\"_blank\" class=\"upgrade_link\">Purchase the User Add-On</a>."
msgstr "<p class=\"wpallimport-bundle-notice\">使用しているインポート バンドル ユーザー インポート アドオンが必要です。</p><a href=\"https://www.wpallimport.com/checkout/?edd_action=add_to_cart&download_id=2707221&edd_options%5Bprice_id%5D=1\" target=\"_blank\" class=\"upgrade_link\">ユーザー インポートのアドオンを購入します。</a>"

#: actions/wp_ajax_nested_xpath.php:51
msgid "XPath is required"
msgstr "XPathが必須"

#: actions/wp_ajax_nested_xpath.php:65
#: actions/wp_ajax_parse_nested_file.php:159
msgid "Elements found"
msgstr "要素が見つかりました"

#: actions/wp_ajax_nested_xpath.php:65
#: actions/wp_ajax_parse_nested_file.php:159
msgid "Elements not found"
msgstr "要素が見つかりません"

#: actions/wp_ajax_save_import_functions.php:31
#: actions/wp_ajax_save_import_functions.php:52
msgid "PHP code must be wrapped in \"&lt;?php\" and \"?&gt;\""
msgstr "PHP コードを囲む必要があります\"<? php「と」? >\""

#: actions/wp_ajax_save_import_functions.php:38
#: actions/wp_ajax_save_import_functions.php:61
msgid "File has been successfully updated."
msgstr "ファイルは正常に更新されました。"

#: actions/wp_ajax_test_images.php:32
msgid "Uploads folder `%s` is not writable."
msgstr "アップロードフォルダ `%s`は書き込み可能ではありません。"

#: actions/wp_ajax_test_images.php:46
msgid "Use image name instead of URL `%s`."
msgstr "URL `%s`の代わりにイメージ名を使用してください。"

#: actions/wp_ajax_test_images.php:53
msgid "File `%s` isn't readable"
msgstr "ファイル `%s`は読み込みできません"

#: actions/wp_ajax_test_images.php:57
msgid "File `%s` doesn't exist"
msgstr "ファイル '%s'は存在しません"

#: actions/wp_ajax_test_images.php:63
msgid "%d image was successfully retrieved from `%s`"
msgstr "%d個の画像は `%s`から正常に取得されました"

#: actions/wp_ajax_test_images.php:67
msgid "%d images were successfully retrieved from `%s`"
msgstr "%d個の画像が `%s`から正常に取得されました"

#: actions/wp_ajax_test_images.php:95
msgid "Image `%s` not found in media library."
msgstr "イメージ '%s' がメディア ・ ライブラリ内に見つかりません。"

#: actions/wp_ajax_test_images.php:102
msgid "%d image was successfully found in media gallery"
msgstr "%d のイメージはメディア ギャラリーに設立され正常に"

#: actions/wp_ajax_test_images.php:106
msgid "%d images were successfully found in media gallery"
msgstr "メディア ギャラリーに正常に設立された %d 画像"

#: actions/wp_ajax_test_images.php:120
msgid "URL `%s` is not valid."
msgstr "URL '%s'は無効です。"

#: actions/wp_ajax_test_images.php:135
msgid "File `%s` cannot be saved locally"
msgstr "ファイル '%s'はローカルには保存できません"

#: actions/wp_ajax_test_images.php:137
msgid "File `%s` is not a valid image."
msgstr "ファイル '%s'は有効な画像ではありません。"

#: actions/wp_ajax_test_images.php:148
msgid "%d image was successfully downloaded in %s seconds"
msgstr "%d個のイメージが%s秒で正常にダウンロードされました"

#: actions/wp_ajax_test_images.php:152
msgid "%d images were successfully downloaded in %s seconds"
msgstr "%d個の画像が%s秒間に正常にダウンロードされました"

#: actions/wp_ajax_upload_resource.php:124
msgid "Please verify that the URL returns a valid import file."
msgstr "URL が有効なインポート ファイルを返すことを確認してください。"

#: actions/wp_loaded.php:38
msgid "Cleanup completed."
msgstr "クリーンアップが完了しました。"

#: actions/wp_loaded.php:44
msgid "Missing import ID."
msgstr "インポートIDがありません。"

#: actions/wp_loaded.php:72
msgid "Other imports are currently in process [%s]."
msgstr "Other imports are currently in process [%s]."

#: actions/wp_loaded.php:82
msgid "Scheduling update is not working with \"upload\" import type. Import #%s."
msgstr "更新のスケジュール設定が「アップロード」インポートタイプで機能していません。 インポート #%s."

#: actions/wp_loaded.php:95 actions/wp_loaded.php:167
msgid "Import #%s is currently in manually process. Request skipped."
msgstr "インポート#%sは現在手動で処理されています。 リクエストはスキップされました。"

#: actions/wp_loaded.php:118 views/admin/history/index.php:170
msgid "triggered by cron"
msgstr "cronによって引き起こされる"

#: actions/wp_loaded.php:124
msgid "#%s Cron job triggered."
msgstr "#%s のCronジョブがトリガーされました。"

#: actions/wp_loaded.php:131
msgid "Import #%s currently in process. Request skipped."
msgstr "現在処理中の#%sをインポートしてください。 リクエストはスキップされました。"

#: actions/wp_loaded.php:140
msgid "Import #%s already triggered. Request skipped."
msgstr "#%s を既にインポートしました。 リクエストはスキップされました。"

#: actions/wp_loaded.php:160
msgid "Import #%s is not triggered. Request skipped."
msgstr "インポート #%s はトリガーされません。 リクエストはスキップされました。"

#: actions/wp_loaded.php:198 views/admin/history/index.php:167
msgid "cron processing"
msgstr "cron処理"

#: actions/wp_loaded.php:230 models/import/record.php:633
msgid "Import #%s complete"
msgstr "#%sのインポートを完了しました"

#: actions/wp_loaded.php:239
msgid "Records Processed %s. Records Count %s."
msgstr "レコード%sが処理されました。 %sをカウントします。"

#: actions/wp_loaded.php:251
msgid "Import #%s already processing. Request skipped."
msgstr "Importación #%s actualmente procesando. Solicitud omitida."

#: actions/wp_loaded.php:275
msgid "Import #%s canceled"
msgstr "インポート#%s キャンセル"

#: classes/api.php:113 views/admin/import/template/_other_template.php:39
#: views/admin/import/template/_other_template.php:88
#: views/admin/import/template/_other_template.php:111
#: views/admin/import/template/_other_template.php:178
#: views/admin/import/template/_other_template.php:208
#: views/admin/import/template/_other_template.php:236
#: views/admin/import/template/_other_template.php:262
msgid "Set with XPath"
msgstr "XPathで設定"

#: classes/api.php:129
#: views/admin/import/template/_custom_fields_template.php:69
#: views/admin/import/template/_custom_fields_template.php:278
#: views/admin/import/template/_custom_fields_template.php:412
#: views/admin/import/template/_term_meta_template.php:69
#: views/admin/import/template/_term_meta_template.php:278
#: views/admin/import/template/_term_meta_template.php:412
msgid "Field Options..."
msgstr "フィールドのオプション"

#: classes/api.php:132
#: views/admin/import/template/_custom_fields_template.php:75
#: views/admin/import/template/_custom_fields_template.php:284
#: views/admin/import/template/_custom_fields_template.php:418
#: views/admin/import/template/_term_meta_template.php:75
#: views/admin/import/template/_term_meta_template.php:284
#: views/admin/import/template/_term_meta_template.php:418
msgid "Mapping"
msgstr "マッピング"

#: classes/api.php:141
#: views/admin/import/template/_custom_fields_template.php:182
#: views/admin/import/template/_custom_fields_template.php:350
#: views/admin/import/template/_custom_fields_template.php:484
#: views/admin/import/template/_taxonomies_template.php:233
#: views/admin/import/template/_term_meta_template.php:182
#: views/admin/import/template/_term_meta_template.php:350
#: views/admin/import/template/_term_meta_template.php:484
msgid "In Your File"
msgstr "ファイル内"

#: classes/api.php:142
#: views/admin/import/template/_custom_fields_template.php:183
#: views/admin/import/template/_custom_fields_template.php:351
#: views/admin/import/template/_custom_fields_template.php:485
#: views/admin/import/template/_taxonomies_template.php:234
#: views/admin/import/template/_term_meta_template.php:183
#: views/admin/import/template/_term_meta_template.php:351
#: views/admin/import/template/_term_meta_template.php:485
msgid "Translated To"
msgstr "翻訳先"

#: classes/api.php:221
#: views/admin/import/template/_custom_fields_template.php:156
#: views/admin/import/template/_custom_fields_template.php:245
#: views/admin/import/template/_custom_fields_template.php:324
#: views/admin/import/template/_custom_fields_template.php:380
#: views/admin/import/template/_custom_fields_template.php:458
#: views/admin/import/template/_custom_fields_template.php:514
#: views/admin/import/template/_term_meta_template.php:156
#: views/admin/import/template/_term_meta_template.php:245
#: views/admin/import/template/_term_meta_template.php:324
#: views/admin/import/template/_term_meta_template.php:380
#: views/admin/import/template/_term_meta_template.php:458
#: views/admin/import/template/_term_meta_template.php:514
msgid "Add Another"
msgstr "情報を追加"

#: classes/api.php:227
#: views/admin/import/template/_custom_fields_template.php:251
#: views/admin/import/template/_custom_fields_template.php:386
#: views/admin/import/template/_custom_fields_template.php:520
#: views/admin/import/template/_term_meta_template.php:251
#: views/admin/import/template/_term_meta_template.php:386
#: views/admin/import/template/_term_meta_template.php:520
msgid "Save Rules"
msgstr "ルールを保存"

#: classes/api.php:258
msgid "Download image hosted elsewhere"
msgstr "他の場所でホストされている画像をダウンロードする"

#: classes/api.php:259 classes/api.php:279
#: views/admin/import/template/_featured_template.php:16
#: views/admin/import/template/_featured_template.php:26
msgid "http:// or https://"
msgstr "http:// or https://"

#: classes/api.php:264
msgid "Use image(s) currently uploaded in %s"
msgstr "現在%sにアップロードされている画像を使用する"

#: classes/api.php:278
msgid "Download file hosted elsewhere"
msgstr "他の場所でホストされているファイルをダウンロードする"

#: classes/api.php:284
msgid "Use file(s) currently uploaded in %s"
msgstr "現在%sにアップロードされているファイルを使用する"

#: classes/api.php:398 models/import/record.php:2710
msgid "- Searching for existing image `%s` in `%s` folder"
msgstr "- 既存のイメージ `%s`を`%s`フォルダ内で検索しています"

#: classes/api.php:405 classes/api.php:476 models/import/record.php:2717
#: models/import/record.php:2801 models/import/record.php:3064
msgid "- <b>WARNING</b>: Can't detect attachment file type %s"
msgstr "- <b>警告</b>: 添付ファイルの種類%sを検出できません"

#: classes/api.php:410 classes/api.php:481 models/import/record.php:2723
#: models/import/record.php:2807
msgid "- File `%s` has been successfully found"
msgstr "- ファイル `%s`が正常に見つかりました"

#: classes/api.php:416 classes/api.php:467 models/import/record.php:2631
#: models/import/record.php:2734 models/import/record.php:2760
#: models/import/record.php:2794
msgid "- <b>WARNING</b>: File %s is not a valid image and cannot be set as featured one"
msgstr "- <b>警告</b>: ファイル%sは有効な画像ではないため、特典として設定することはできません"

#: classes/api.php:419 models/import/record.php:2730
msgid "- Image `%s` has been successfully found"
msgstr "- 画像`%s` が正常に見つかりました"

#: classes/api.php:429 models/import/record.php:2743
msgid "- Downloading image from `%s`"
msgstr "- `%s`から画像をダウンロードしています"

#: classes/api.php:432
msgid "- Downloading file from `%s`"
msgstr "- `%s`からファイルをダウンロードしています"

#: classes/api.php:444 classes/api.php:471 models/import/record.php:2756
#: models/import/record.php:2792
msgid "- Image `%s` has been successfully downloaded"
msgstr "- 画像`%s`が正常にダウンロードされました"

#: classes/api.php:450 models/import/record.php:2767
msgid "- File `%s` has been successfully downloaded"
msgstr "- ファイル `%s`が正常にダウンロードされました"

#: classes/api.php:461 models/import/record.php:2784
msgid "- <b>WARNING</b>: File %s cannot be saved locally as %s"
msgstr "- <b>警告</b>: ファイル%sをローカルに%sとして保存することはできません"

#: classes/api.php:495 models/import/record.php:2845
msgid "- Creating an attachment for image `%s`"
msgstr "- 画像 `%s`の添付ファイルを作成しています"

#: classes/api.php:498
msgid "- Creating an attachment for file `%s`"
msgstr "- ファイル `%s`の添付ファイルを作成する"

#: classes/api.php:517 models/import/record.php:2872
#: models/import/record.php:3086
msgid "- <b>WARNING</b>"
msgstr "- <b>警告</b>"

#: classes/api.php:521 models/import/record.php:2932
msgid "- Attachment has been successfully created for image `%s`"
msgstr "- 画像`%s`の添付ファイルが正常に作成されました"

#: classes/render.php:68 classes/render.php:88 classes/render.php:166
#: classes/render.php:186
msgid "<strong>%s</strong> %s more"
msgstr "<strong>%s</strong> %s さらに"

#: classes/render.php:68 classes/render.php:88 classes/render.php:166
#: classes/render.php:186 views/admin/import/evaluate.php:5
#: views/admin/import/evaluate_variations.php:3
msgid "element"
msgid_plural "elements"
msgstr[0] "要素"

#: classes/render.php:94 classes/render.php:192
msgid "more"
msgstr "さらに"

#: classes/updater.php:66
msgid "View WP All Import Pro Changelog"
msgstr "WP All Import Proの変更履歴を表示します。"

#: classes/updater.php:66
msgid "Changelog"
msgstr "変更履歴"

#: classes/updater.php:261
msgid "There is a new version of %1$s available. <a target=\"_blank\" class=\"thickbox\" href=\"%2$s\">View version %3$s details</a>."
msgstr "%1$s の利用可能な新しいバージョンがあります。<a target=\"_blank\" class=\"thickbox\" href=\"%2$s\"> %3$s バージョンの詳細を表示</a>"

#: classes/updater.php:268
msgid "There is a new version of %1$s available. <a target=\"_blank\" class=\"thickbox\" href=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\">update now</a>."
msgstr "%1$s の利用可能な新しいバージョンがあります。<a target=\"_blank\" class=\"thickbox\" href=\"%2$s\">%3$s バージョンの詳細を表示</a> か <a href=\"%4$s\">今すぐ更新</a>"

#: classes/updater.php:456
msgid "You do not have permission to install plugin updates"
msgstr "あなたはプラグインの更新プログラムをインストールする権限がありません"

#: classes/updater.php:456
msgid "Error"
msgstr "エラー"

#: classes/upload.php:50
msgid "Please specify a file to import.<br/><br/>If you are uploading the file from your computer, please wait for it to finish uploading (progress bar at 100%), before trying to continue."
msgstr "インポートするファイルを指定してください。 <br/><br/>お使いのコンピュータからファイルをアップロードする場合、それは（100％でプログレスバーを）アップロードを完了するために、継続しようとする前に、お待ちください。"

#: classes/upload.php:52
msgid "Uploaded file is empty"
msgstr "アップロードされたファイルは空です"

#: classes/upload.php:54 controllers/admin/settings.php:466
msgid "Uploaded file must be XML, CSV, ZIP, GZIP, GZ, JSON, SQL, TXT, DAT or PSV"
msgstr "アップロードされたファイルは、XML、CSV、ZIP、GZIP、GZ、JSON、SQL、TXT、DATやPSVでなければなりません"

#: classes/upload.php:61 classes/upload.php:149 classes/upload.php:357
#: classes/upload.php:428 classes/upload.php:690 classes/upload.php:761
msgid "WP All Import couldn't find a file to import inside your ZIP.<br/><br/>Either the .ZIP file is broken, or doesn't contain a file with an extension of  XML, CSV, PSV, DAT, or TXT. <br/>Please attempt to unzip your .ZIP file on your computer to ensure it is a valid .ZIP file which can actually be unzipped, and that it contains a file which WP All Import can import."
msgstr "WPすべてのインポートは、あなたのZIPの内側にインポートするファイルを見つけることができませんでした。 <br/><br/>いずれかの.ZIPファイルが壊れている、またはXML、CSV、PSV、DAT、またはTXTの拡張子を持つファイルが含まれていません。 <br/>それは実際に解凍することができ、有効な.ZIPファイルであり、それはWPすべてのインポートがインポートできるファイルが含まれていることを確認するために、コンピュータ上の.ZIPファイルを解凍しようとしてください。"

#: classes/upload.php:129 classes/upload.php:169 classes/upload.php:408
#: classes/upload.php:677 classes/upload.php:741 classes/upload.php:781
#: classes/upload.php:808 classes/upload.php:847 classes/upload.php:871
#: classes/upload.php:895 classes/upload.php:967
msgid "WP All Import can't access your WordPress uploads folder."
msgstr "WPすべてのインポートは、WordPressのアップロードフォルダにアクセスすることはできません。"

#: classes/upload.php:236 classes/upload.php:500 classes/upload.php:602
#: classes/upload.php:834 classes/upload.php:992
msgid "Can not import this file. JSON to XML convertation failed."
msgstr "このファイルをインポートすることはできません。 XMLのconvertationにJSONに失敗しました。"

#: classes/upload.php:326 classes/upload.php:673
msgid "Please specify a file to import."
msgstr "インポートするファイルを指定してください。"

#: classes/upload.php:328
msgid "The URL to your file is not valid.<br/><br/>Please make sure the URL starts with http:// or https://. To import from https://, your server must have OpenSSL installed."
msgstr "ファイルへのURLが無効です。<br/><br/>URLがhttp：//またはhttps：//で始まっていることを確認してください。 https：//からインポートするには、サーバーにOpenSSLがインストールされている必要があります。"

#: classes/upload.php:330
msgid "Uploads folder "
msgstr "アップロードフォルダ%sは書き込み可能でなければなりません"

#: classes/upload.php:350
msgid "Failed upload ZIP archive"
msgstr "ZIPアーカイブアップロードが失敗した"

#: classes/upload.php:461 classes/upload.php:565
msgid "WP All Import was not able to download your file.<br/><br/>Please make sure the URL to your file is valid.<br/>You can test this by pasting it into your browser.<br/>Other reasons for this error can include some server setting on your host restricting access to this particular URL or external URLs in general, or some setting on the server hosting the file you are trying to access preventing your server from accessing it."
msgstr "WP All Importでファイルをダウンロードできませんでした。<br/> <br/>ファイルへのURLが有効であることを確認してください。<br/>これをブラウザに貼り付けることでテストできます。このエラーの原因には、ホスト上のこの特定のURLまたは外部URLへのアクセスを制限するサーバー設定や、アクセスしようとしているファイルをホストしているサーバーの設定などがあります。"

#: controllers/admin/addons.php:21
msgid "WooCommerce Addon"
msgstr "WooCommerceアドオン"

#: controllers/admin/addons.php:22 controllers/admin/addons.php:76
msgid "Import Products from any XML or CSV to WooCommerce"
msgstr "製品のインポートWooCommerceに任意のXMLまたはCSVから"

#: controllers/admin/addons.php:32
msgid "ACF Addon"
msgstr "ACFアドオン"

#: controllers/admin/addons.php:33
msgid "Import to advanced custom fields"
msgstr "高度なカスタムフィールドへのインポート"

#: controllers/admin/addons.php:43
msgid "WPML Addon"
msgstr "WPMLアドオン"

#: controllers/admin/addons.php:44
msgid "Import to WPML"
msgstr "WPMLインポート"

#: controllers/admin/addons.php:54
msgid "User Addon"
msgstr "ユーザーアドオン"

#: controllers/admin/addons.php:55
msgid "Import Users"
msgstr "ユーザーのインポート"

#: controllers/admin/addons.php:65
msgid "Link cloaking Addon"
msgstr "リンククローキングアドオン"

#: controllers/admin/addons.php:66
msgid "Affiliate link cloaking"
msgstr "アフィリエイトリンククローキング"

#: controllers/admin/addons.php:75
msgid "WooCommerce Addon - free edition"
msgstr "WooCommerceアドオン - 無料版"

#: controllers/admin/addons.php:84
msgid "WooCommerce Tabs Addon"
msgstr "WooCommerceタブのアドオン"

#: controllers/admin/addons.php:85
msgid "Import data to WooCommerce tabs"
msgstr "WooCommerceのタブにデータをインポートする"

#: controllers/admin/history.php:31
msgid "Import is not specified."
msgstr "インポートは指定されていません。"

#: controllers/admin/history.php:57 controllers/admin/manage.php:60
msgid "&laquo;"
msgstr "&laquo;"

#: controllers/admin/history.php:58 controllers/admin/manage.php:61
msgid "&raquo;"
msgstr "&raquo;"

#: controllers/admin/history.php:92
msgid "Log file does not exists."
msgstr "ログファイルは存在しません。"

#: controllers/admin/history.php:112
msgid "History deleted"
msgstr "履歴が削除されました"

#: controllers/admin/history.php:139 controllers/admin/manage.php:652
msgid "%d %s deleted"
msgstr "%d %s が削除されました"

#: controllers/admin/history.php:139
msgid "history"
msgid_plural "histories"
msgstr[0] "履歴"

#: controllers/admin/import.php:75
msgid "WP All Import lost track of where you are.<br/><br/>Maybe you cleared your cookies or maybe it is just a temporary issue on your or your web host's end.<br/>If you can't do an import without seeing this error, change your session settings on the All Import -> Settings page."
msgstr "WP All Importはあなたの現在の場所を失ってしまいました。<br/> <br/>あなたのクッキーをクリアしたのかもしれないし、あなたのウェブホストの終わりに一時的な問題かもしれません。 このエラーが表示されずにインポートするには、[すべてのインポート - >設定]ページでセッション設定を変更します。"

#: controllers/admin/import.php:92 controllers/admin/import.php:684
msgid "There are no elements to import based on your XPath.<br/><br/>If you are in Step 2, you probably specified filtering options that don’t match any elements present in your file.<br/>If you are seeing this error elsewhere, it means that while the XPath expression for your initial import matched some elements in your file previously, there are now zero elements in the file that match this expression.<br/>You can edit the XPath for your import by going to the Manage Imports -> Import Settings page."
msgstr "あなたのXPathに基づいてインポートする要素はありません。<br/>ステップ2の場合は、ファイルに存在する要素と一致しないフィルタリングオプションが指定されている可能性があります。 このエラーを他の場所で見ると、最初のインポートのXPath式がファイル内のいくつかの要素と一致していましたが、この式に一致する要素がファイル内にゼロになりました。 [インポートの管理] - > [設定のインポート]ページに移動します。"

#: controllers/admin/import.php:151
msgid "The import associated with this export has been deleted."
msgstr "このエクスポートに関連したインポートは削除されました。"

#: controllers/admin/import.php:151
msgid "Please re-run your export by clicking Run Export on the All Export -> Manage Exports page. Then try your import again."
msgstr "[すべてのエクスポート] - > [エクスポートの管理]ページで[エクスポートの実行]をクリックして、エクスポートを再実行してください。 次に、インポートを再試行してください。"

#: controllers/admin/import.php:156
msgid "This import has been deleted."
msgstr "このインポートは削除されました。"

#: controllers/admin/import.php:177
msgid "Required PHP components are missing.<br/><br/>WP All Import requires DOMDocument, XMLReader, and XMLWriter PHP modules to be installed.<br/>These are standard features of PHP, and are necessary for WP All Import to read the files you are trying to import.<br/>Please contact your web hosting provider and ask them to install and activate the DOMDocument, XMLReader, and XMLWriter PHP modules."
msgstr "必要なPHPコンポーネントが欠落しています。 <br/><br/> WPすべてのインポートのDOMDocument、XMLReaderのを必要とし、するXMLWriter PHPモジュールがインストールされています。 <br/>これらは、PHPの標準機能であり、WPすべてのインポートは、インポートしようとしているファイルを読み取るために必要です。 <br/>あなたのWebホスティングプロバイダに連絡したDOMDocument、XMLReaderを、とするXMLWriter PHPモジュールをインストールし、アクティブにするように依頼してください。"

#: controllers/admin/import.php:252
msgid "Select an item type to import the data"
msgstr "データをインポートするアイテムの種類を選択します"

#: controllers/admin/import.php:256
msgid "Previous import for update must be selected to proceed with a new one"
msgstr "更新の前のインポートは、新しいものを続行するように選択する必要があります"

#: controllers/admin/import.php:260
msgid "Select a taxonomy to import the data"
msgstr "データをインポートする分類を選択します。"

#: controllers/admin/import.php:303
msgid "File is no longer in the correct format"
msgstr "ファイルは正しい形式になっていません"

#: controllers/admin/import.php:306 controllers/admin/import.php:349
msgid "Certain columns are required to be present in your file to enable it to be re-imported with WP All Import. These columns are missing. Re-export your file using WP All Export, and don't delete any of the columns when editing it. Then, re-import will work correctly."
msgstr "特定の列は、WPすべてのインポートで再インポートすることを可能にするために、あなたのファイルに存在することが必要とされています。これらの列は表示されません。 WPすべてのエクスポートを使用してファイルを再エクスポートし、それを編集するときに任意の列を削除しないでください。その後、再インポートが正常に動作します。"

#: controllers/admin/import.php:309
msgid "The import template you are using requires the User Add-On.<br/><a href=\"http://www.wpallimport.com/add-ons/user-import/?utm_source=wordpress.org&utm_medium=wpai-import-template&utm_campaign=free+wp+all+export+plugin\" target=\"_blank\">Purchase the User Add-On</a>"
msgstr "あなたが使用しているインポートテンプレートは、ユーザーのインポートアドオンが必要です。 <br/> <a href=\"http://www.wpallimport.com/add-ons/user-import/?utm_source=wordpress.org&utm_medium=wpai-import-template&utm_campaign=free wp all export plugin\" target=\"_blank\">ユーザーのインポートアドオンを購入</a>"

#: controllers/admin/import.php:384
msgid "Unable to download feed resource."
msgstr "フィードリソースをダウンロードできません。"

#: controllers/admin/import.php:461 controllers/admin/import.php:485
#: controllers/admin/settings.php:705
msgid "Please confirm you are importing a valid feed.<br/> Often, feed providers distribute feeds with invalid data, improperly wrapped HTML, line breaks where they should not be, faulty character encodings, syntax errors in the XML, and other issues.<br/><br/>WP All Import has checks in place to automatically fix some of the most common problems, but we can’t catch every single one.<br/><br/>It is also possible that there is a bug in WP All Import, and the problem is not with the feed.<br/><br/>If you need assistance, please contact support – <a href=\"mailto:<EMAIL>\"><EMAIL></a> – with your XML/CSV file. We will identify the problem and release a bug fix if necessary."
msgstr "多くの場合、フィードプロバイダは、無効なデータ、不適切にラップされたHTML、不適切な行の改行、誤った文字エンコード、XMLの構文エラーなどのフィードを配布します。< br /> <br/> WPすべてのインポートでは、最も一般的な問題のいくつかを自動的に修正するためのチェックが行われていますが、すべての問題を自動的に検出することはできません。 あなたが援助を必要とする場合は、サポートに連絡してください - <a href=\"mailto:<EMAIL>\"> @ wpallimportをサポートしてください。 com </a> - あなたのXML / CSVファイルで。 問題を特定し、必要に応じてバグ修正をリリースします。"

#: controllers/admin/import.php:480
msgid "WP All Import unable to detect file type.<br/><br/>WP All Import not able to determine what type of file you are importing. Make sure your file extension is correct for the file type you are importing.<br/> Please choose the correct file type from the dropdown below, or try adding &type=xml or &type=csv to the end of the URL, for example http://example.com/export-products.php?&type=xml"
msgstr "WP All Importでファイルタイプを検出できません。<br/> <br/> WP All Importは、インポートするファイルのタイプを特定できません。 ファイルの拡張子が、インポートするファイルの種類に合っていることを確認してください。<br/>下のドロップダウンから正しいファイルの種類を選択するか、http://example.com/export-products.php?&type=xml のように &type=xml または &type=csv をURLの末尾に追加してみてください "

#: controllers/admin/import.php:513
msgid "No elements selected"
msgstr "要素が選択されていません"

#: controllers/admin/import.php:518 controllers/admin/import.php:747
msgid "Your XPath is not valid.<br/><br/>Click \"get default XPath\" to get the default XPath."
msgstr "あなたのXPathは無効です。<br/> <br/>デフォルトのXPathを取得するには、「デフォルトのXPathを取得」をクリックします。"

#: controllers/admin/import.php:522 controllers/admin/import.php:753
msgid "XPath must match only elements"
msgstr "XPathは要素のみと一致する必要があります"

#: controllers/admin/import.php:552
msgid "Warning: No matching elements found for XPath expression from the import being updated. It probably means that new XML file has different format. Though you can update XPath, procceed only if you sure about update operation being valid."
msgstr "警告：インポートが更新されてから、XPath式が見つかりません整合素子。これはおそらく、新しいXMLファイルは、異なるフォーマットを有することを意味します。あなたはXPathを更新することができますが、唯一の更新操作が有効であることについてよくわから場合procceed。"

#: controllers/admin/import.php:594 controllers/admin/import.php:740
msgid "Your XPath is empty.<br/><br/>Please enter an XPath expression, or click \"get default XPath\" to get the default XPath."
msgstr "あなたのXPathは空です。 <br/><br/> XPath式を入力するか、デフォルトのXPathを取得するには、「取得デフォルトのXPath」をクリックしてください。"

#: controllers/admin/import.php:749
msgid "No matching variations found for XPath specified"
msgstr "XPathのが見つかりませマッチングバリエーションが指定されていません"

#: controllers/admin/import.php:990 controllers/admin/import.php:1006
#: controllers/admin/import.php:1137
msgid "WP All Import lost track of where you are.<br/><br/>Maybe you cleared your cookies or maybe it is just a temporary issue on your web host's end.<br/>If you can't do an import without seeing this error, change your session settings on the All Import -> Settings page."
msgstr "WP All Importはあなたの現在の場所を失ってしまいました。<br/> <br/>あなたのクッキーをクリアしたのかもしれないし、あなたのウェブホストの終わりに一時的な問題かもしれません。 このエラーが表示されずにインポートするには、[すべてのインポート - >設定]ページでセッション設定を変更します。"

#: controllers/admin/import.php:992
msgid "<strong>Warning</strong>: your title is blank."
msgstr "<strong>警告:</strong> あなたのタイトルは空白です。"

#: controllers/admin/import.php:997
msgid "<strong>Warning</strong>: resulting post title is empty"
msgstr "<strong>警告</strong>: 結果の投稿タイトルは空です"

#: controllers/admin/import.php:1002
msgid "Error parsing title: %s"
msgstr "タイトルの解析中にエラーが発生しました: %s"

#: controllers/admin/import.php:1008
msgid "<strong>Warning</strong>: your content is blank."
msgstr "<strong>警告</strong>: あなたのコンテンツは空白です。"

#: controllers/admin/import.php:1013
msgid "<strong>Warning</strong>: resulting post content is empty"
msgstr "<strong>警告</strong>: 結果の投稿コンテンツは空です"

#: controllers/admin/import.php:1018
msgid "Error parsing content: %s"
msgstr "コンテンツの解析中にエラーが発生しました: %s"

#: controllers/admin/import.php:1157 controllers/admin/import.php:1313
#: controllers/admin/import.php:1446
msgid "Error parsing: %s"
msgstr "エラー解析: %s"

#: controllers/admin/import.php:1269 controllers/admin/import.php:1425
msgid "Error parsing: String could not be parsed as XML"
msgstr "エラー解析：文字列をXMLとして解析できませんでした"

#: controllers/admin/import.php:1310 controllers/admin/import.php:1443
msgid "There is no data to preview"
msgstr "プレビューするデータはありません"

#: controllers/admin/import.php:1506
msgid "You've reached your max_input_vars limit of %d. Please increase this."
msgstr "max_input_varsの上限（%d）に達しました。 これを増やしてください。"

#: controllers/admin/import.php:1563 views/admin/import/template.php:71
msgid "Excerpt"
msgstr "抜粋"

#: controllers/admin/import.php:1567 controllers/admin/import.php:1571
#: models/import/record.php:1340
#: views/admin/import/options/_reimport_options.php:143
#: views/admin/import/options/_reimport_taxonomies_options.php:106
#: views/admin/import/template.php:116
msgid "Images"
msgstr "画像"

#: controllers/admin/import.php:1577
msgid "Images meta "
msgstr "画像メタ"

#: controllers/admin/import.php:1592
msgid "Custom Field Name"
msgstr "カスタムフィールド名"

#: controllers/admin/import.php:1596
msgid "Custom Field Value"
msgstr "カスタムフィールド値"

#: controllers/admin/import.php:1609
msgid "Both name and value must be set for all woocommerce attributes"
msgstr "名前と値の両方がすべてのwoocommerce属性に設定する必要があります"

#: controllers/admin/import.php:1612
msgid "Attribute Field Name"
msgstr "属性フィールド名"

#: controllers/admin/import.php:1615
msgid "Attribute Field Value"
msgstr "属性フィールドの値"

#: controllers/admin/import.php:1626
msgid "Tags"
msgstr "タグ"

#: controllers/admin/import.php:1629 views/admin/history/index.php:33
msgid "Date"
msgstr "日付"

#: controllers/admin/import.php:1631 controllers/admin/import.php:1632
msgid "Start Date"
msgstr "開始日"

#: controllers/admin/import.php:1666
msgid "Template updated"
msgstr "テンプレートアップデート"

#: controllers/admin/import.php:1766
msgid "%s template is invalid: %s"
msgstr "%s のテンプレートは無効: %s"

#: controllers/admin/import.php:1878
msgid "Records to import must be specified or uncheck `Import only specified records` option to process all records"
msgstr "すべてのレコードを処理するには、インポートするレコードを指定するか、「特定のレコードのみをインポートする」オプションをオフにする必要があります"

#: controllers/admin/import.php:1883
msgid "Wrong format of `Import only specified records` value"
msgstr "`指定されたレコードだけをインポートする 'ための値の形式が間違っています"

#: controllers/admin/import.php:1886
msgid "One of the numbers in `Import only specified records` value exceeds record quantity in XML file"
msgstr "`インポートのみ指定レコード`の値の数字の一つは、XMLファイル内のレコード数を超えます"

#: controllers/admin/import.php:1893
msgid "Expression for `Post Unique Key` must be set, use the same expression as specified for post title if you are not sure what to put there"
msgstr "`投稿ユニーク Key`の表示を設定する必要があります。何を置くべきか分からない場合は、ポストタイトルに指定されているのと同じ式を使用しなければなりません"

#: controllers/admin/import.php:1895
msgid "Post Unique Key"
msgstr "投稿ユニーク Key"

#: controllers/admin/import.php:1899
msgid "Custom field name must be specified."
msgstr "カスタムフィールド名を指定する必要があります。"

#: controllers/admin/import.php:1901
msgid "Custom field value must be specified."
msgstr "カスタムフィールド値を指定する必要があります。"

#: controllers/admin/import.php:1905
msgid "Post ID must be specified."
msgstr "投稿 ID を指定する必要があります。"

#: controllers/admin/import.php:1909
msgid "Term name must be specified."
msgstr "用語の名前を指定する必要があります。"

#: controllers/admin/import.php:1912
msgid "Term slug must be specified."
msgstr "用語スラグを指定する必要があります。"

#: controllers/admin/import.php:1991
msgid "WP All Import doesn't support this import type."
msgstr "WP All Import はこのインポートタイプをサポートしていません。"

#: controllers/admin/import.php:2039
msgid "<strong>Warning:</strong> this file does not have the same structure as the last file associated with this import. WP All Import won't be able to import this file with your current settings. Probably you'll need to adjust your XPath in the \"Configure Advanced Settings\" box below, and reconfigure your import by clicking \"Edit\" on the Manage Imports page."
msgstr "<strong>警告：</strong>このファイルは、このインポートに関連付けられた最後のファイルと同じ構造を持っていません。 WPすべてのインポートは、あなたの現在の設定でこのファイルをインポートすることはできません。おそらくあなたは、以下の「設定詳細設定」ボックスにXPathを調整し、管理インポートページの「編集」をクリックして、インポートを再設定する必要があります。"

#: controllers/admin/import.php:2084
msgid "Root element not found for uploaded feed."
msgstr "ルート要素は、アップロードされたフィードが見つかりません。"

#: controllers/admin/import.php:2136
msgid "Import updated"
msgstr "インポートを更新"

#: controllers/admin/import.php:2136
msgid "Import created"
msgstr "インポートを作成"

#: controllers/admin/import.php:2238
msgid "Configuration updated"
msgstr "設定が更新されました"

#: controllers/admin/import.php:2417 controllers/admin/import.php:2737
#: controllers/admin/import.php:2876
#: views/admin/import/options/_reimport_taxonomies_options.php:7
#: views/admin/import/options/_reimport_taxonomies_template.php:6
#: views/admin/import/template/_custom_fields_template.php:6
#: views/admin/import/template/_term_meta_template.php:6
#: views/admin/import/template/_term_other_template.php:6
#: views/admin/manage/delete.php:45 views/admin/manage/index.php:284
msgid "Taxonomy Terms"
msgstr "分類規則"

#: controllers/admin/import.php:2443 controllers/admin/import.php:2745
#: controllers/admin/import.php:2883 models/import/record.php:650
msgid "%d %s created %d updated %d deleted %d skipped"
msgstr "%d 件の %s が作成されました。 %d 個は更新され %d 個は削除され %d 個はスキップされました"

#: controllers/admin/import.php:2890
msgid "Canceled"
msgstr "キャンセル済み"

#: controllers/admin/import.php:2890
msgid "Complete"
msgstr "完了"

#: controllers/admin/license.php:43
msgid "Licenses saved"
msgstr "ライセンス保存"

#: controllers/admin/manage.php:257
msgid "The other two files in this zip are the export file containing all of your data and the import template for WP All Import. \n"
"\n"
"To import this data, create a new import with WP All Import and upload this zip file."
msgstr "この zip ファイルに他の 2 つのファイルは、WP をすべてインポートのすべてのあなたのデータとテンプレートのインポートを含むエクスポート ファイルです。\n"
"\n"
"このデータをインポートするには、WP をすべてインポートと新しいインポートを作成し、この zip ファイルをアップロードします。"

#: controllers/admin/manage.php:312 views/admin/manage/index.php:272
msgid "Import canceled"
msgstr "インポートがキャンセル"

#: controllers/admin/manage.php:377
msgid "This import appears to be using FTP. Unfortunately WP All Import no longer supports the FTP protocol. Please contact <a href=\"mailto:<EMAIL>\"><EMAIL></a> if you have any questions."
msgstr "このインポートはFTPを使用しているようです。残念なことにWP All ImportはFTPプロトコルをサポートしなくなりました。ご不明な点がございましたら、<a href=\"mailto:<EMAIL>\"> <EMAIL> </a>までご連絡ください。"

#: controllers/admin/manage.php:477
msgid "No matching elements found for Root element and XPath expression specified"
msgstr "ルート要素とXPath式が一致する要素が見つかりません"

#: controllers/admin/manage.php:573
msgid "File does not exists."
msgstr "ファイルが存在しません。"

#: controllers/admin/manage.php:652 views/admin/manage/bulk.php:10
msgid "import"
msgid_plural "imports"
msgstr[0] "インポート"

#: controllers/admin/settings.php:60
msgid "History File Count must be a non-negative integer"
msgstr "履歴ファイル数は、負でない整数でなければなりません"

#: controllers/admin/settings.php:63
msgid "History Age must be a non-negative integer"
msgstr "履歴経年は非負整数でなければなりません"

#: controllers/admin/settings.php:83
msgid "Settings saved"
msgstr "設定保存"

#: controllers/admin/settings.php:114
msgid "Unknown File extension. Only txt files are permitted"
msgstr "不明なファイルの拡張子。唯一のTXTファイルが許可されています"

#: controllers/admin/settings.php:127
msgid "%d template imported"
msgid_plural "%d templates imported"
msgstr[0] "%d テンプレートのインポート"

#: controllers/admin/settings.php:129
msgid "Wrong imported data format"
msgstr "間違ってインポートされたデータ形式"

#: controllers/admin/settings.php:131
msgid "File is empty or doesn't exests"
msgstr "ファイルが空または存在しません"

#: controllers/admin/settings.php:134
msgid "Undefined entry!"
msgstr "未定義のエントリ！"

#: controllers/admin/settings.php:136
msgid "Please select file."
msgstr "ファイルを選択してください。"

#: controllers/admin/settings.php:142
msgid "Templates must be selected"
msgstr "テンプレートを選択する必要があります"

#: controllers/admin/settings.php:151
msgid "%d template deleted"
msgid_plural "%d templates deleted"
msgstr[0] "%d テンプレートは削除されました"

#: controllers/admin/settings.php:279
msgid "Files not found"
msgstr "ファイルが見つかりません"

#: controllers/admin/settings.php:287
msgid "Clean Up has been successfully completed."
msgstr "クリーンアップが正常に完了しました。"

#: controllers/admin/settings.php:445
msgid "Uploads folder is not writable."
msgstr "アップロードフォルダは書き込み可能ではありません。"

#: controllers/admin/settings.php:502
msgid "Failed to open temp directory."
msgstr "一時ディレクトリを開くことができませんでした。"

#: controllers/admin/settings.php:527 controllers/admin/settings.php:552
msgid "Failed to open input stream."
msgstr "入力ストリームを開くことができませんでした。"

#: controllers/admin/settings.php:534 controllers/admin/settings.php:559
msgid "Failed to open output stream."
msgstr "出力ストリームを開くことができませんでした。"

#: controllers/admin/settings.php:538
msgid "Failed to move uploaded file."
msgstr "アップロードされたファイルの移動に失敗しました。"

#: controllers/admin/settings.php:713
#: views/admin/import/options/_import_file.php:51
msgid "This %s file has errors and is not valid."
msgstr "ファイル %s にエラーと有効ではありません。"

#: filters/pmxi_custom_types.php:8
msgid "WooCommerce Products"
msgstr "WooCommerce 商品リスト"

#: filters/pmxi_custom_types.php:9
msgid "WooCommerce Orders"
msgstr "WooCommerceの注文"

#: filters/pmxi_custom_types.php:10
msgid "WooCommerce Coupons"
msgstr "WooCommerce クーポン"

#: helpers/import_custom_meta_box.php:25
msgid "Custom fields can be used to add extra metadata to a post that you can <a href=\"http://codex.wordpress.org/Using_Custom_Fields\" target=\"_blank\">use in your theme</a>."
msgstr "カスタムフィールドを使用して、<a href=\"http://codex.wordpress.org/Using_Custom_Fields\" target=\"_blank\">あなたのテーマで使用できる</a>投稿に特別なメタデータを追加することができます。"

#: helpers/reverse_taxonomies_html.php:18
#: views/admin/import/template/_taxonomies_template.php:41
#: views/admin/import/template/_taxonomies_template.php:63
#: views/admin/import/template/_taxonomies_template.php:97
#: views/admin/import/template/_taxonomies_template.php:107
#: views/admin/import/template/_taxonomies_template.php:116
#: views/admin/import/template/_taxonomies_template.php:164
#: views/admin/import/template/_taxonomies_template.php:182
#: views/admin/import/template/_taxonomies_template.php:189
#: views/admin/import/template/_taxonomies_template.php:201
msgid "Assign post to the taxonomy."
msgstr "投稿をタクソノミーに割り当てます。"

#: helpers/wp_all_import_addon_notifications.php:108
msgid "Make imports easier with the <strong>Advanced Custom Fields Add-On</strong> for WP All Import: <a href=\"%s\" target=\"_blank\">Read More</a>"
msgstr "WP All Importの<strong>高度なカスタムフィールドアドオン</ strong>でインポートを簡単にする：<a href=\"%s\" target=\"_blank\">続きを読む</a>"

#: helpers/wp_all_import_addon_notifications.php:124
msgid "WP All Export"
msgstr "WP すべてエクスポート"

#: helpers/wp_all_import_addon_notifications.php:125
msgid "Export anything in WordPress to CSV, XML, or Excel."
msgstr "ワードプレスで何かを CSV、XML、または Excel にエクスポートします。"

#: helpers/wp_all_import_addon_notifications.php:128
#: views/admin/import/confirm.php:53 views/admin/import/index.php:356
#: views/admin/import/index.php:373 views/admin/import/options.php:70
#: views/admin/import/options/_import_file.php:40
#: views/admin/import/options/_import_file.php:57
#: views/admin/import/process.php:86 views/admin/import/process.php:120
msgid "Read More"
msgstr "詳細表示"

#: helpers/wp_all_import_addon_notifications.php:141
msgid "Make imports easier with the <strong>free %s Add-On</strong> for WP All Import: <a href=\"%s\" target=\"_blank\">Get Add-On</a>"
msgstr "WP all Importの<strong>無料%sアドオン</ strong>を使用してインポートを簡単にする：<a href=\"%s\" target=\"_blank\">アドオンを取得する</a>"

#: helpers/wp_all_import_is_json.php:13
msgid "Maximum stack depth exceeded"
msgstr "最大スタック深度を超えました"

#: helpers/wp_all_import_is_json.php:16
msgid "Underflow or the modes mismatch"
msgstr "アンダーフローまたはモードの不一致"

#: helpers/wp_all_import_is_json.php:19
msgid "Unexpected control character found"
msgstr "予期しない制御文字が見つかりました"

#: helpers/wp_all_import_is_json.php:22
msgid "Syntax error, malformed JSON"
msgstr "構文エラー、不正な形式のJSON"

#: helpers/wp_all_import_is_json.php:25
msgid "Malformed UTF-8 characters, possibly incorrectly encoded"
msgstr "誤ってエンコードされた可能性のある不正なUTF-8文字列"

#: helpers/wp_all_import_is_json.php:28
msgid "Unknown json error"
msgstr "不明なJSONエラー"

#: helpers/wp_all_import_template_notifications.php:14
msgid "The import template you are using requires the %s. If you continue without it your data may import incorrectly.<br/><br/><a href=\"%s\" target=\"_blank\">"
msgstr "使用しているテンプレートのインポートには、%s が必要です。それせずに続行した場合、データが正しくインポート可能性があります。<br><br><a href=\"%s\" target=\"_blank\"></a>"

#: helpers/wp_all_import_template_notifications.php:23
msgid "The import template you are using requires the User Add-On. If you continue without it your data may import incorrectly.<br/><br/><a href=\"http://www.wpallimport.com/add-ons/user-import/?utm_source=wordpress.org&utm_medium=wpai-import-template&utm_campaign=free+wp+all+export+plugin\" target=\"_blank\">Purchase the User Add-On</a>."
msgstr "使用しているインポートテンプレートには、ユーザーインポートアドオンが必要です。あなたがそれなしで続けると、あなたのデータは間違ってインポートされる可能性があります。<br/> <br/><a href=\"http://www.wpallimport.com/add-ons/user-import/?utm_source=wordpress.org&utm_medium=wpai-import-template&utm_campaign=free+wp+all+export+plugin\" target=\"_blank\">ユーザーインポートアドオンを購入してください</a>。"

#: helpers/wp_all_import_template_notifications.php:27
msgid "The import template you are using requires the WooCommerce Import Add-On. If you continue without it your data may import incorrectly.<br/><br/><a href=\"http://www.wpallimport.com/woocommerce-product-import/\" target=\"_blank\">Purchase the WooCommerce Import Add-On</a>."
msgstr "使用しているインポートテンプレートには、WooCommerceインポートアドオンが必要です。それがなくてもデータが間違ってインポートされる可能性があります。<br/> <br/> <a href=\"http://www.wpallimport.com/woocommerce-product-import/\" target=\"_blank\"> WooCommerceインポートアドオンを購入する</a>をします。"

#: helpers/wp_all_import_template_notifications.php:32
msgid "The import template you are using requires the Realia Add-On. If you continue without it your data may import incorrectly.<br/><br/><a href=\"https://wordpress.org/plugins/realia-xml-csv-property-listings-import/\" target=\"_blank\">Download the Realia Add-On</a>."
msgstr "あなたが使用しているインポートテンプレートは、実物教材アドオンが必要です。あなたはそれなしで続行した場合、あなたのデータが間違ってインポートすることがあります。 <br/><br/> <a href=\"https://wordpress.org/plugins/realia-xml-csv-property-listings-import/\" target=\"_blank\">実物教材は、アドオンのダウンロード</a> 。"

#: helpers/wp_all_import_template_notifications.php:39
msgid "The import template you are using requires the WP Residence Add-On. If you continue without it your data may import incorrectly.<br/><br/><a href=\"https://wordpress.org/plugins/wp-residence-add-on-for-wp-all-import/\" target=\"_blank\">Download the WP Residence Add-On</a>."
msgstr "あなたが使用しているインポートテンプレートは、WPレジデンスアドオンが必要です。あなたはそれなしで続行した場合、あなたのデータが間違ってインポートすることがあります。 <br/><br/> <a href=\"https://wordpress.org/plugins/wp-residence-add-on-for-wp-all-import/\" target=\"_blank\">ダウンロードWPレジデンスアドオン</a> 。"

#: helpers/wp_all_import_template_notifications.php:46
msgid "The import template you are using requires the RealHomes Add-On. If you continue without it your data may import incorrectly.<br/><br/><a href=\"https://wordpress.org/plugins/realhomes-xml-csv-property-listings-import/\" target=\"_blank\">Download the RealHomes Add-On</a>."
msgstr "あなたが使用しているインポートテンプレートはRealHomesアドオンが必要です。あなたはそれなしで続行した場合、あなたのデータが間違ってインポートすることがあります。 <br/><br/> <a href=\"https://wordpress.org/plugins/realhomes-xml-csv-property-listings-import/\" target=\"_blank\">RealHomesは、アドオンのダウンロード</a> 。"

#: helpers/wp_all_import_template_notifications.php:52
msgid "The import template you are using requires the Jobify Add-On. If you continue without it your data may import incorrectly.<br/><br/><a href=\"https://wordpress.org/plugins/jobify-xml-csv-listings-import/\" target=\"_blank\">Download the Jobify Add-On</a>."
msgstr "あなたが使用しているインポートテンプレートはJobifyアドオンが必要です。あなたはそれなしで続行した場合、あなたのデータが間違ってインポートすることがあります。 <br/><br/> <a href=\"https://wordpress.org/plugins/jobify-xml-csv-listings-import/\" target=\"_blank\">Jobifyは、アドオンのダウンロード</a> 。"

#: helpers/wp_all_import_template_notifications.php:58
msgid "The import template you are using requires the Listify Add-On. If you continue without it your data may import incorrectly.<br/><br/><a href=\"https://wordpress.org/plugins/listify-xml-csv-listings-import/\" target=\"_blank\">Download the Listify Add-On</a>."
msgstr "あなたが使用しているインポートテンプレートはListifyアドオンが必要です。あなたはそれなしで続行した場合、あなたのデータが間違ってインポートすることがあります。 <br/><br/> <a href=\"https://wordpress.org/plugins/listify-xml-csv-listings-import/\" target=\"_blank\">Listifyは、アドオンのダウンロード</a> 。"

#: helpers/wp_all_import_template_notifications.php:64
msgid "The import template you are using requires the Reales WP Add-On. If you continue without it your data may import incorrectly.<br/><br/><a href=\"https://wordpress.org/plugins/reales-wp-xml-csv-property-listings-import/\" target=\"_blank\">Download the Reales WP Add-On</a>."
msgstr "あなたが使用しているインポートテンプレートはレアレスWPアドオンが必要です。あなたはそれなしで続行した場合、あなたのデータが間違ってインポートすることがあります。 <br/><br/> <a href=\"https://wordpress.org/plugins/reales-wp-xml-csv-property-listings-import/\" target=\"_blank\">ダウンロードレアレスWPアドオン</a> 。"

#: helpers/wp_all_import_template_notifications.php:74
msgid "The import template you are using requires the WP Job Manager Add-On. If you continue without it your data may import incorrectly.<br/><br/><a href=\"https://wordpress.org/plugins/wp-job-manager-xml-csv-listings-import/\" target=\"_blank\">Download the WP Job Manager Add-On</a>."
msgstr "あなたが使用しているインポートテンプレートは、WPジョブマネージャアドオンが必要です。あなたはそれなしで続行した場合、あなたのデータが間違ってインポートすることがあります。 <br/><br/> <a href=\"https://wordpress.org/plugins/wp-job-manager-xml-csv-listings-import/\" target=\"_blank\">WPジョブマネージャは、アドオンのダウンロード</a> 。"

#: helpers/wp_all_import_template_notifications.php:80
msgid "The import template you are using requires the Yoast SEO Add-On. If you continue without it your data may import incorrectly.<br/><br/><a href=\"https://wordpress.org/plugins/yoast-seo-settings-xml-csv-import/\" target=\"_blank\">Download the Yoast SEO Add-On</a>."
msgstr "あなたが使用しているインポートテンプレートはYoast SEOアドオンが必要です。あなたはそれなしで続行した場合、あなたのデータが間違ってインポートすることがあります。 <br/><br/> <a href=\"https://wordpress.org/plugins/yoast-seo-settings-xml-csv-import/\" target=\"_blank\">Yoast SEOアドオンのダウンロード</a> 。"

#: helpers/wp_all_import_template_notifications.php:86
msgid "The import template you are using requires the Listable Add-On. If you continue without it your data may import incorrectly.<br/><br/><a href=\"https://wordpress.org/plugins/import-xml-csv-listings-to-listable-theme/\" target=\"_blank\">Download the Listable Add-On</a>."
msgstr "あなたが使用しているインポートテンプレートは、リスト可能アドオンが必要です。あなたはそれなしで続行した場合、あなたのデータが間違ってインポートすることがあります。 <br/><br/> <a href=\"https://wordpress.org/plugins/import-xml-csv-listings-to-listable-theme/\" target=\"_blank\">リスト可能なアドオンのダウンロード</a> 。"

#: helpers/wp_all_import_template_notifications.php:91
msgid "The import template you are using requires an Add-On for WP All Import. If you continue without using this Add-On your data may import incorrectly."
msgstr "あなたが使用しているインポートテンプレートは、アドオンWPすべてのインポートのためにする必要があります。あなたはこのアドオンあなたのデータ使用せずに続行した場合、誤ってインポートすることができます。"

#: helpers/wp_all_import_template_notifications.php:103
msgid "<strong>Warning:</strong>"
msgstr "<strong>警告：</strong>"

#: models/import/record.php:44
msgid "WP All Import can't read your file.<br/><br/>Probably, you are trying to import an invalid XML feed. Try opening the XML feed in a web browser (Google Chrome is recommended for opening XML files) to see if there is an error message.<br/>Alternatively, run the feed through a validator: http://validator.w3.org/<br/>99% of the time, the reason for this error is because your XML feed isn't valid.<br/>If you are 100% sure you are importing a valid XML feed, please contact WP All Import support."
msgstr "WP All Import can't read your file.<br/><br/>Probably, you are trying to import an invalid XML feed. Try opening the XML feed in a web browser (Google Chrome is recommended for opening XML files) to see if there is an error message.<br/>Alternatively, run the feed through a validator: http://validator.w3.org/<br/>99% of the time, the reason for this error is because your XML feed isn't valid.<br/>If you are 100% sure you are importing a valid XML feed, please contact WP All Import support."

#: models/import/record.php:56
msgid "Invalid XML"
msgstr "無効なXML"

#: models/import/record.php:59
msgid "Line"
msgstr "線"

#: models/import/record.php:60
msgid "Column"
msgstr "カラム"

#: models/import/record.php:61
msgid "Code"
msgstr "コード"

#: models/import/record.php:72
msgid "Required PHP components are missing."
msgstr "必要なPHPコンポーネントが欠落しています。"

#: models/import/record.php:73
msgid "WP All Import requires the SimpleXML PHP module to be installed. This is a standard feature of PHP, and is necessary for WP All Import to read the files you are trying to import.<br/>Please contact your web hosting provider and ask them to install and activate the SimpleXML PHP module."
msgstr "WP All ImportがインストールされているのSimpleXML PHPモジュールが必要です。これは、PHPの標準機能であり、WPすべてのインポートは、インポートしようとしているファイルを読み取るために必要です。 <br/>あなたのWebホスティングプロバイダに連絡し、SimpleXMLをPHPモジュールをインストールしてアクティブにするように依頼してください。"

#: models/import/record.php:117
msgid "This import appears to be using FTP. Unfortunately WP All Import no longer supports the FTP protocol. Please contact <a href=\"mailto:<EMAIL>\">%s</a> if you have any questions."
msgstr "このインポートはFTPを使用しているようです。残念なことにWP All ImportはFTPプロトコルをサポートしなくなりました。ご不明な点がございましたら、<a href=\"mailto:<EMAIL>\">%s </a>にお問い合わせください。"

#: models/import/record.php:267
msgid "#%s No matching elements found for Root element and XPath expression specified"
msgstr "#%s はルート要素とXPath式が一致する要素が見つかりません"

#: models/import/record.php:533
msgid "Deleted missing records %s for import #%s"
msgstr "インポート #%s の %s を記録削除された行方不明"

#: models/import/record.php:596
msgid "Updating stock status for missing records %s for import #%s"
msgstr "インポート #%s の %s を記録が見つからないため在庫状況の更新"

#: models/import/record.php:624
msgid "import finished & cron un-triggered<br>%s %s created %s updated %s deleted %s skipped"
msgstr "インポートが完了し、cronがトリガされました<br>%s の %s を作成した %s を更新 %s を削除 %s をスキップ"

#: models/import/record.php:657
msgid "Records Processed %s. Records imported %s of %s."
msgstr "処理済みのレコード %s は %s のインポートされた %s を記録。"

#: models/import/record.php:676
msgid "#%s source file not found"
msgstr "#%s のソースファイルが見つかりません"

#: models/import/record.php:729
msgid "Composing titles..."
msgstr "タイトルを構成します..."

#: models/import/record.php:739
msgid "Composing parent terms..."
msgstr "親の条件を作成する."

#: models/import/record.php:748
msgid "Composing terms slug..."
msgstr "用語スラグを構成する."

#: models/import/record.php:758
msgid "Composing excerpts..."
msgstr "抜粋の作成..."

#: models/import/record.php:769
msgid "Composing statuses..."
msgstr "ステータスを構成します..."

#: models/import/record.php:780
msgid "Composing comment statuses..."
msgstr "コメントのステータスを構成します..."

#: models/import/record.php:791
msgid "Composing ping statuses..."
msgstr "pingのステータスを構成します..."

#: models/import/record.php:802
msgid "Composing post formats..."
msgstr "投稿フォーマットを作る..."

#: models/import/record.php:814
msgid "Composing duplicate indicators..."
msgstr "重複するインジケーターを構成するには."

#: models/import/record.php:827
msgid "Composing page templates..."
msgstr "ページテンプレートの作成..."

#: models/import/record.php:838
msgid "Composing post types..."
msgstr "ポストタイプを構成します..."

#: models/import/record.php:852
msgid "Composing page parent..."
msgstr "ページの親を構成します..."

#: models/import/record.php:912
msgid "Composing authors..."
msgstr "著者を構成します..."

#: models/import/record.php:953
msgid "Composing slugs..."
msgstr "スラッグを構成します..."

#: models/import/record.php:962
msgid "Composing menu order..."
msgstr "メニューの順序を構成します..."

#: models/import/record.php:971
msgid "Composing contents..."
msgstr "コンテンツを構成します..."

#: models/import/record.php:984
msgid "Composing dates..."
msgstr "日付の作成..."

#: models/import/record.php:992 models/import/record.php:1005
#: models/import/record.php:1011
msgid "<b>WARNING</b>: unrecognized date format `%s`, assigning current date"
msgstr "<b>警告</b> ：認識できない日付フォーマット`%s`、現在の日付を割り当てます"

#: models/import/record.php:1027
msgid "Composing terms for `%s` taxonomy..."
msgstr "`%s`分類のための条項を構成します..."

#: models/import/record.php:1239
msgid "Composing custom parameters..."
msgstr "カスタムパラメータの作成..."

#: models/import/record.php:1346 models/import/record.php:1464
msgid "<b>WARNING</b>"
msgstr "<b>警告</b>"

#: models/import/record.php:1347
msgid "<b>WARNING</b>: No featured images will be created. Uploads folder is not found."
msgstr "<b>警告</b>：注目画像は作成されません。 アップロードフォルダが見つかりません。"

#: models/import/record.php:1358
msgid "Composing URLs for "
msgstr "URLを構成します。"

#: models/import/record.php:1389 models/import/record.php:1401
#: models/import/record.php:1413 models/import/record.php:1425
#: models/import/record.php:1437 models/import/record.php:1448
msgid "Composing "
msgstr "構成"

#: models/import/record.php:1465
msgid "<b>WARNING</b>: No attachments will be created"
msgstr "<b>警告</b> ：添付ファイルは作成されません"

#: models/import/record.php:1468
msgid "Composing URLs for attachments files..."
msgstr "添付ファイルのURLを構成します..."

#: models/import/record.php:1497
msgid "Composing unique keys..."
msgstr "一意キーを構成します..."

#: models/import/record.php:1505
msgid "Processing posts..."
msgstr "処理の記事..."

#: models/import/record.php:1511
msgid "Data parsing via add-ons..."
msgstr "アドオンを経由しての解析データ..."

#: models/import/record.php:1554
msgid "Calculate specified records to import..."
msgstr "インポートする指定されたレコードを計算し..."

#: models/import/record.php:1581
msgid "---"
msgstr "---"

#: models/import/record.php:1582
msgid "Record #%s"
msgstr "レコード #%s"

#: models/import/record.php:1589 models/import/record.php:1779
msgid "<b>SKIPPED</b>: by specified records option"
msgstr "<b>スキップ</b>：指定されたレコードオプション"

#: models/import/record.php:1598
msgid "<b>ACTION</b>: pmxi_before_post_import ..."
msgstr "<b>アクション</b>: pmxi_before_post_import ..."

#: models/import/record.php:1606
msgid "<b>WARNING</b>: title is empty."
msgstr "<b>警告</b> ：タイトルは空です。"

#: models/import/record.php:1627
msgid "Combine all data for user %s..."
msgstr "ユーザー%sのすべてのデータを結合する..."

#: models/import/record.php:1647
msgid "Combine all data for term %s..."
msgstr "ユーザー%sのすべてのデータを結合する..."

#: models/import/record.php:1665
msgid "Combine all data for post `%s`..."
msgstr "投稿 `%s`のすべてのデータを結合します..."

#: models/import/record.php:1694
msgid "Find corresponding article among previously imported for post `%s`..."
msgstr "以前にインポートされた記事のうち、投稿 `%s`に対応する記事を検索します..."

#: models/import/record.php:1702
msgid "Duplicate post was found for post %s with unique key `%s`..."
msgstr "重複投稿は、投稿%sの一意のキー `%s`持つために発見されました..."

#: models/import/record.php:1716
msgid "Duplicate post wasn't found with unique key `%s`..."
msgstr "重複した後は、一意のキー、 `%s`で見つかりませんでした..."

#: models/import/record.php:1730
msgid "Find corresponding article among database for post `%s`..."
msgstr "ポスト `%s`のためのデータベースの間で対応する記事を検索..."

#: models/import/record.php:1743
msgid "Duplicate post was found for post `%s`..."
msgstr "重複投稿は、投稿 '%s' で見つかりませんでした."

#: models/import/record.php:1757
msgid "Duplicate post wasn't found for post `%s`..."
msgstr "重複投稿は、ポスト '%s' に見つかりませんでした."

#: models/import/record.php:1800
msgid "<b>SKIPPED</b>: By filter wp_all_import_is_post_to_update `%s`"
msgstr "<b>スキップ：</b>フィルタwp_all_import_is_post_to_updateでは`%s`"

#: models/import/record.php:1817
msgid "<b>SKIPPED</b>: Previously imported record found for `%s`"
msgstr "<b>スキップ</b>： `%s`見つかり以前にインポートされたレコードを"

#: models/import/record.php:1846
msgid "Preserve description of already existing taxonomy term for `%s`"
msgstr "既存の説明を保持する '%s' の分類用語"

#: models/import/record.php:1850
msgid "Preserve name of already existing taxonomy term for `%s`"
msgstr "既存の名前を維持 '%s' の分類用語"

#: models/import/record.php:1854
msgid "Preserve slug of already existing taxonomy term for `%s`"
msgstr "既存のスラグを保持 '%s' の分類用語"

#: models/import/record.php:1862
msgid "Preserve parent of already existing taxonomy term for `%s`"
msgstr "既存の親を保持 '%s' の分類用語"

#: models/import/record.php:1868
msgid "Preserve taxonomies of already existing article for `%s`"
msgstr "`%s`のための既存の記事の分類法を維持"

#: models/import/record.php:1873
msgid "<b>WARNING</b>: Unable to get current taxonomies for article #%d, updating with those read from XML file"
msgstr "<b>警告</b> ：XMLファイルから読み込まれたもので、更新、記事の＃%dの現在の分類法を取得できません"

#: models/import/record.php:1890
msgid "Preserve date of already existing article for `%s`"
msgstr "`%s`のために既存の記事の日付を保持"

#: models/import/record.php:1894
msgid "Preserve status of already existing article for `%s`"
msgstr "`%s`のために既存の記事の状態を保持"

#: models/import/record.php:1898
msgid "Preserve content of already existing article for `%s`"
msgstr "`%s`のための既存の記事の内容を保存します"

#: models/import/record.php:1902
msgid "Preserve title of already existing article for `%s`"
msgstr "`%s`のために既存の記事のタイトルを保持"

#: models/import/record.php:1906
msgid "Preserve slug of already existing article for `%s`"
msgstr "`%s`のために既存の物品のスラグを保持"

#: models/import/record.php:1926
msgid "Preserve excerpt of already existing article for `%s`"
msgstr "`%s`のために既存の記事の抜粋を保ちます"

#: models/import/record.php:1930
msgid "Preserve menu order of already existing article for `%s`"
msgstr "`%s`のために既存の記事のメニューの順序を保持"

#: models/import/record.php:1934
msgid "Preserve post parent of already existing article for `%s`"
msgstr "`%s`のために既存の記事のポストの親を保持"

#: models/import/record.php:1938
msgid "Preserve post type of already existing article for `%s`"
msgstr "既存のポストの種類を保持 '%s' の記事"

#: models/import/record.php:1942
msgid "Preserve comment status of already existing article for `%s`"
msgstr "既存の注釈のステータスを保持する '%s' の記事"

#: models/import/record.php:1946
msgid "Preserve post author of already existing article for `%s`"
msgstr "`%s`のために既存の記事の投稿者を保持"

#: models/import/record.php:1962 models/import/record.php:1981
msgid "Deleting images for `%s`"
msgstr "`%s`のための削除画像"

#: models/import/record.php:1976
msgid "Deleting attachments for `%s`"
msgstr "`%s`のための添付ファイルを削除"

#: models/import/record.php:2004
msgid "Applying filter `pmxi_article_data` for `%s`"
msgstr "`%s`のフィルタ` pmxi_article_data`を適用します"

#: models/import/record.php:2012
msgid "<b>SKIPPED</b>: by do not create new posts option."
msgstr "<b>スキップ</b>：新しい記事オプションを作成しないことで。"

#: models/import/record.php:2084
msgid "<b>WARNING</b>: Unable to create cloaked link for %s"
msgstr "<b>警告</b> ：%sのクロークリンクを作成することができません。"

#: models/import/record.php:2111
msgid "<b>SKIPPED</b>: By filter wp_all_import_is_post_to_create `%s`"
msgstr "<b>スキップ</b>：フィルタwp_all_import_is_post_to_updateでは`%s`"

#: models/import/record.php:2122
msgid "<b>ERROR</b> Sorry, that email address `%s` is already used!"
msgstr "<strong>エラー</strong>:申し訳ありませんがメールアドレス `%s` はすでに使用されています！"

#: models/import/record.php:2132 models/import/record.php:2162
msgid "<b>CREATING</b> `%s` `%s`"
msgstr "<b>作成</b> `%s` `%s`"

#: models/import/record.php:2135 models/import/record.php:2165
msgid "<b>UPDATING</b> `%s` `%s`"
msgstr "<b>更新</b> `%s` `%s`"

#: models/import/record.php:2148 models/import/record.php:2173
#: models/import/record.php:2178 models/import/record.php:3369
msgid "<b>ERROR</b>"
msgstr "<b>エラー</b>"

#: models/import/record.php:2201
msgid "Associate post `%s` with current import ..."
msgstr "現在のインポートに関連付けポスト `%s` ..."

#: models/import/record.php:2207
msgid "Associate post `%s` with post format %s ..."
msgstr "投稿 `%s`を投稿形式 %s に関連付ける"

#: models/import/record.php:2219
msgid "<b>CUSTOM FIELDS:</b>"
msgstr "<b>CAMPOS PERSONALIZADOS:</b>"

#: models/import/record.php:2267
msgid "- Custom field %s has been deleted for `%s` attempted to `update all custom fields` setting ..."
msgstr "- カスタムフィールド`%s`で%sがすべてのカスタムフィールドの設定'を更新しようとしましたが削除されました..."

#: models/import/record.php:2285
msgid "- Custom field %s has been deleted for `%s` attempted to `update only these custom fields: %s, leave rest alone` setting ..."
msgstr " - カスタムフィールド%sが  `%s`のために削除されました：`これらのカスタムフィールドのみを更新しようとしました：%sの設定残しておくだけです。..."

#: models/import/record.php:2304
msgid "- Custom field %s has been deleted for `%s` attempted to `leave these fields alone: %s, update all other Custom Fields` setting ..."
msgstr "- `%s`のカスタムフィールド%sが削除されました。`これらのフィールドだけを残しました：%s は他のすべてのカスタムフィールドの設定を更新しました。"

#: models/import/record.php:2374
msgid "- Custom field `%s` has been skipped attempted to record matching options ..."
msgstr "- カスタムフィールド `%s`は、一致するオプションを記録しようとスキップされています..."

#: models/import/record.php:2382
msgid "- <b>ACTION</b>: pmxi_custom_field"
msgstr "<b>アクション</b>: pmxi_custom_field"

#: models/import/record.php:2421
msgid "- Custom field `%s` has been updated with value `%s` for post `%s` ..."
msgstr "- カスタムフィールド `%s`は値`ポスト `%s`の%s`で更新されています..."

#: models/import/record.php:2422
msgid "- <b>ACTION</b>: pmxi_update_post_meta"
msgstr "- <b>アクション</b>: pmxi_update_post_meta"

#: models/import/record.php:2462
msgid "<b>IMAGES:</b>"
msgstr "<b>画像:</b>"

#: models/import/record.php:2466
msgid "<b>ERROR</b>: Target directory %s is not writable"
msgstr "<b>エラー</b>: ターゲットディレクトリ %s は書き込み可能ではありません"

#: models/import/record.php:2495
msgid "- Keep existing and add newest images ..."
msgstr "- 既存のままにして最新の画像を追加..."

#: models/import/record.php:2581
msgid "- Importing image `%s` for `%s` ..."
msgstr "- `%s`の画像 '%s`の読み込み中..."

#: models/import/record.php:2610 models/import/record.php:2688
msgid "- <b>WARNING</b>: Image %s not found in media gallery."
msgstr "-<b>警告</b>: イメージ %s メディア ギャラリー内に見つかりません。"

#: models/import/record.php:2614 models/import/record.php:2692
msgid "- Using existing image `%s` for post `%s` ..."
msgstr "-ポスト '%s' を '%s' の既存のイメージを使用して."

#: models/import/record.php:2625
msgid "- found base64_encoded image"
msgstr "-  Base64エンコードされた画像を発見"

#: models/import/record.php:2899
msgid "- <b>ACTION</b>: "
msgstr "-<b>アクション</b>。"

#: models/import/record.php:2928
msgid "- Attachment has been successfully updated for image `%s`"
msgstr "-添付ファイル イメージ '%s' を正常に更新しました"

#: models/import/record.php:2946
msgid "- Post `%s` saved as Draft, because no images are downloaded successfully"
msgstr "- 正常にダウンロードされたイメージがないため、 `%s`を下書きとして保存しました。"

#: models/import/record.php:2955
msgid "Post `%s` saved as Draft, because no images are downloaded successfully"
msgstr "画像が正常にダウンロードされなかったため、投稿 `%s`を下書きとして保存しました。"

#: models/import/record.php:2994
msgid "Images import skipped for post `%s` according to 'pmxi_is_images_to_update' filter..."
msgstr "'pmxi_is_images_to_update'フィルタに基づいて画像のインポートが `%s`の投稿でスキップされました..."

#: models/import/record.php:3006
msgid "<b>ATTACHMENTS:</b>"
msgstr "<b>添付ファイル：</b>"

#: models/import/record.php:3009
msgid "- <b>ERROR</b>: Target directory %s is not writable"
msgstr "- <b>エラー</b>: ターゲットディレクトリ%sは書き込み可能ではありません"

#: models/import/record.php:3018
msgid "- Importing attachments for `%s` ..."
msgstr "- `%s`の添付ファイルのインポート中..."

#: models/import/record.php:3043
msgid "- Using existing file `%s` for post `%s` ..."
msgstr "投稿 '%s' の既存のファイル '%s' を使用して."

#: models/import/record.php:3052
msgid "- Filename for attachment was generated as %s"
msgstr "- 添付ファイルのファイル名が%sとして生成されました"

#: models/import/record.php:3059
msgid "- <b>WARNING</b>: Attachment file %s cannot be saved locally as %s"
msgstr "- <b>警告</ b>：添付ファイル%sをローカルで%sとして保存することはできません"

#: models/import/record.php:3060
msgid "- <b>WP Error</b>: %s"
msgstr "- <b>WP エラー</b>: %s"

#: models/import/record.php:3074
msgid "- File %s has been successfully downloaded"
msgstr "- ファイル%sが正常にダウンロードされました"

#: models/import/record.php:3090
msgid "- Attachment has been successfully created for post `%s`"
msgstr "- 投稿 '%s`の添付ファイルが正常に作成されました"

#: models/import/record.php:3091 models/import/record.php:3116
msgid "- <b>ACTION</b>: pmxi_attachment_uploaded"
msgstr "- <b>アクション</b>: pmxi_attachment_uploaded"

#: models/import/record.php:3110
msgid "- Attachment has been successfully updated for file `%s`"
msgstr "-添付ファイル ファイル '%s' を正常に更新しました"

#: models/import/record.php:3114
msgid "- Attachment has been successfully created for file `%s`"
msgstr "- 投稿 '%s`の添付ファイルが正常に作成されました"

#: models/import/record.php:3127
msgid "Attachments import skipped for post `%s` according to 'pmxi_is_attachments_to_update' filter..."
msgstr "'pmxi_is_attachments_to_update'フィルタに従って添付ファイルのインポートが `%s`の投稿にスキップされました..."

#: models/import/record.php:3134
msgid "<b>TAXONOMIES:</b>"
msgstr "<b>タクソノミー：</b>"

#: models/import/record.php:3143
msgid "- Importing taxonomy `%s` ..."
msgstr "- タクソノミー `%s` をインポートしています..."

#: models/import/record.php:3146
msgid "- Auto-nest enabled with separator `%s` ..."
msgstr "- 自動巣は、セパレータ `%s`で有効になって..."

#: models/import/record.php:3152
msgid "- %s %s `%s` has been skipped attempted to `Leave these taxonomies alone, update all others`..."
msgstr "- %sの%sの`%s`は`すべてothers`を更新し、単独でこれらの分類法のままにしようとし、スキップされています..."

#: models/import/record.php:3157
msgid "- %s %s `%s` has been skipped attempted to `Update only these taxonomies, leave the rest alone`..."
msgstr "- %sの%sの `%s`は、`更新のみこれらの分類法に試みスキップされた、alone`残りの部分を残して..."

#: models/import/record.php:3196
msgid "- Creating parent %s %s `%s` ..."
msgstr "- 親%sの%sの `%s`を作成しています..."

#: models/import/record.php:3199
msgid "- Creating child %s %s for %s named `%s` ..."
msgstr "- `%s`という名前の%sの子%sの%sを作成しています..."

#: models/import/record.php:3206
msgid "- <b>WARNING</b>: `%s`"
msgstr "- <b>警告</b>: `%s`"

#: models/import/record.php:3227
msgid "- Attempted to create parent %s %s `%s`, duplicate detected. Importing %s to existing `%s` %s, ID %d, slug `%s` ..."
msgstr "- 親%sの%sの `%s`、検出された複製を作成しようとしました。 %sの `%s`は%sの既存の、ID %d の、スラグ`%s`のインポート..."

#: models/import/record.php:3230
msgid "- Attempted to create child %s %s `%s`, duplicate detected. Importing %s to existing `%s` %s, ID %d, slug `%s` ..."
msgstr "- 子%s、%sの '%s`を作成しようとしましたが重複が検出されました。 %sを既存の `%s`%s、ID %d、スラッグ`%s`にインポートしています..."

#: models/import/record.php:3245
msgid "- %s %s `%s` has been skipped attempted to `Do not update Taxonomies (incl. Categories and Tags)`..."
msgstr "- %sの%sの `%s`は` `タクソノミー（税込カテゴリとタグ）を更新しないと試みスキップされています..."

#: models/import/record.php:3264
msgid "<b>CREATED</b> `%s` `%s` (ID: %s)"
msgstr "<b>作成</b> `%s` `%s` (ID: %s)"

#: models/import/record.php:3266
msgid "<b>UPDATED</b> `%s` `%s` (ID: %s)"
msgstr "<b>更新</b> `%s` `%s` (ID: %s)"

#: models/import/record.php:3309
msgid "<b>ACTION</b>: pmxi_saved_post"
msgstr "<b>アクション</b>: pmxi_saved_post"

#: models/import/record.php:3316
msgid "<span class=\"processing_info\"><span class=\"created_count\">%s</span><span class=\"updated_count\">%s</span><span class=\"percents_count\">%s</span></span>"
msgstr "<span class=\"processing_info\"><span class=\"created_count\">%s</span><span class=\"updated_count\">%s</span><span class=\"percents_count\">%s</span></span>"

#: models/import/record.php:3320
msgid "<b>ACTION</b>: pmxi_after_post_import"
msgstr "<b>アクション</b>: pmxi_after_post_import"

#: models/import/record.php:3349
msgid "Update stock status previously imported posts which are no longer actual..."
msgstr "以前にインポートされた在庫ステータスを更新しました。これはもはや実際のものではありません..."

#: models/import/record.php:3373
msgid "Cleaning temporary data..."
msgstr "一時的なデータのクリーニング..."

#: models/import/record.php:3389
msgid "Deleting source XML file..."
msgstr "ソースXMLファイルを削除しています..."

#: models/import/record.php:3393
msgid "Deleting chunks files..."
msgstr "チャンクファイルを削除しています..."

#: models/import/record.php:3400 models/import/record.php:3409
msgid "<b>WARNING</b>: Unable to remove %s"
msgstr "<b>警告</b> ：%sは削除できません。"

#: models/import/record.php:3421
msgid "Removing previously imported posts which are no longer actual..."
msgstr "以前はインポートされた投稿は削除されました..."

#: models/import/record.php:3443
msgid "<b>ACTION</b>: pmxi_delete_post"
msgstr "<b>アクション</b>: pmxi_delete_post"

#: models/import/record.php:3445
msgid "Deleting posts from database"
msgstr "データベースから投稿を削除する"

#: models/import/record.php:3462
msgid "Instead of deletion user with ID `%s`, set Custom Field `%s` to value `%s`"
msgstr "ID '%s' の削除ユーザーの代わりにカスタム フィールド '%s' を '%s' の値に設定します。"

#: models/import/record.php:3466
msgid "Instead of deletion taxonomy term with ID `%s`, set Custom Field `%s` to value `%s`"
msgstr "ID '%s' の分類用語を削除、代わりにカスタム フィールド '%s' を '%s' の値に設定します。"

#: models/import/record.php:3470
msgid "Instead of deletion post with ID `%s`, set Custom Field `%s` to value `%s`"
msgstr "代わりにID `%s`と削除後の、`%s`を大切にカスタムフィールド `%s`を設定"

#: models/import/record.php:3482
msgid "Instead of deletion, change post with ID `%s` status to Draft"
msgstr "削除する代わりに、ID `%s`ステータスの投稿をDraftに変更してください"

#: models/import/record.php:3566
msgid "%d Posts deleted from database. IDs (%s)"
msgstr "%d 件の記事はデータベースから削除されます。Id (%s)"

#: models/import/record.php:3658
msgid "<b>ERROR</b> Could not insert term relationship into the database"
msgstr "<b>エラー</b> データベースに用語の関係を挿入できませんでした"

#: views/admin/addons/index.php:3
msgid "WP All Import Add-ons"
msgstr "WP All Import アドオン"

#: views/admin/addons/index.php:8
msgid "Premium Add-ons"
msgstr "プレミアムアドオン"

#: views/admin/addons/index.php:20 views/admin/addons/index.php:61
msgid "Installed"
msgstr "インストール済み"

#: views/admin/addons/index.php:22
msgid "Free Version Installed"
msgstr "無料版がインストールされている"

#: views/admin/addons/index.php:29 views/admin/addons/index.php:70
msgid " required"
msgstr "必須"

#: views/admin/addons/index.php:36 views/admin/addons/index.php:77
#: views/admin/addons/index.php:82 views/admin/import/index.php:115
msgid "Download"
msgstr "ダウンロード"

#: views/admin/addons/index.php:41
msgid "Purchase & Install"
msgstr "購入 & インストール"

#: views/admin/addons/index.php:49
msgid "Free Add-ons"
msgstr "無料アドオン"

#: views/admin/addons/index.php:63
msgid "Paid Version Installed"
msgstr "有料版インストール"

#: views/admin/help/index.php:1
msgid "WP All Import Help"
msgstr "WP All Import ヘルプ"

#: views/admin/history/index.php:16 views/admin/history/index.php:18
#: views/admin/history/index.php:20
msgid "%s - Import History"
msgstr "%s - インポート履歴"

#: views/admin/history/index.php:32 views/admin/manage/index.php:27
msgid "ID"
msgstr "ＩＤ"

#: views/admin/history/index.php:34
msgid "Run Time"
msgstr "実使用時間"

#: views/admin/history/index.php:35
msgid "Type"
msgstr "種類"

#: views/admin/history/index.php:36 views/admin/manage/index.php:30
msgid "Summary"
msgstr "要約"

#: views/admin/history/index.php:42
msgid "Scheduling Status"
msgstr "スケジューリングステータス"

#: views/admin/history/index.php:42
msgid "triggered"
msgstr "トリガー"

#: views/admin/history/index.php:42
msgid "and processing"
msgstr "処理中…"

#: views/admin/history/index.php:52 views/admin/history/index.php:226
#: views/admin/manage/index.php:44 views/admin/manage/index.php:359
msgid "Bulk Actions"
msgstr "一括操作"

#: views/admin/history/index.php:53 views/admin/history/index.php:228
#: views/admin/manage/index.php:45 views/admin/manage/index.php:192
#: views/admin/manage/index.php:361
msgid "Delete"
msgstr "削除"

#: views/admin/history/index.php:55 views/admin/history/index.php:234
#: views/admin/import/element.php:97 views/admin/manage/index.php:47
#: views/admin/manage/index.php:367
msgid "Apply"
msgstr "適用"

#: views/admin/history/index.php:61 views/admin/manage/index.php:53
msgid "Displaying %s&#8211;%s of %s"
msgstr "%s&#8211;%s の %sを表示しています"

#: views/admin/history/index.php:108
msgid "No previous history found."
msgstr "以前の履歴が見つかりませんでした。"

#: views/admin/history/index.php:161
msgid "manual run"
msgstr "手動実行"

#: views/admin/history/index.php:164
msgid "continue run"
msgstr "実行を継続"

#: views/admin/history/index.php:189
msgid "Download Log"
msgstr "ダウンロード履歴"

#: views/admin/history/index.php:193
msgid "Log Unavailable"
msgstr "ログを使用できません"

#: views/admin/history/index.php:230 views/admin/manage/index.php:363
msgid "Restore"
msgstr "復元"

#: views/admin/history/index.php:231 views/admin/manage/index.php:364
msgid "Delete Permanently"
msgstr "完全削除"

#: views/admin/history/index.php:238 views/admin/import/confirm.php:359
#: views/admin/import/element.php:224 views/admin/import/index.php:388
#: views/admin/import/options.php:104 views/admin/import/process.php:124
#: views/admin/import/template.php:234 views/admin/manage/index.php:372
#: views/admin/manage/scheduling.php:62 views/admin/settings/index.php:230
msgid "Created by"
msgstr "作成者"

#: views/admin/import/confirm.php:12 views/admin/import/element.php:9
#: views/admin/import/index.php:44 views/admin/import/options.php:19
#: views/admin/import/process.php:9 views/admin/import/template.php:10
msgid "Import XML / CSV"
msgstr "XML/ CSVインポーター"

#: views/admin/import/confirm.php:15 views/admin/import/element.php:12
#: views/admin/import/index.php:47 views/admin/import/options.php:22
#: views/admin/import/process.php:12 views/admin/import/template.php:13
msgid "Support"
msgstr "サポート"

#: views/admin/import/confirm.php:15 views/admin/import/element.php:12
#: views/admin/import/index.php:47 views/admin/import/options.php:22
#: views/admin/import/process.php:12 views/admin/import/template.php:13
msgid "Documentation"
msgstr "ドキュメンテーション"

#: views/admin/import/confirm.php:45 views/admin/import/options.php:62
msgid "This URL no longer returns an import file"
msgstr "この URL は、もはやインポート ファイルを返します"

#: views/admin/import/confirm.php:46 views/admin/import/options.php:63
msgid "You must provide a URL that returns a valid import file."
msgstr "有効なインポート ファイルを返す URL を指定する必要があります。"

#: views/admin/import/confirm.php:48 views/admin/import/index.php:362
#: views/admin/import/options.php:65
#: views/admin/import/options/_import_file.php:46
msgid "There's a problem with your import file"
msgstr "インポート ファイルに問題があります。"

#: views/admin/import/confirm.php:49 views/admin/import/options.php:66
msgid "It has changed and is not compatible with this import template."
msgstr "それが変更され、このテンプレートのインポートと互換性がありません。"

#: views/admin/import/confirm.php:72
msgid "Your file is all set up!"
msgstr "あなたのファイルはすべてセットアップされています！"

#: views/admin/import/confirm.php:74
msgid "This import did not finish successfuly last time it was run."
msgstr "このインポートは、前回の実行時に正常に終了しませんでした。"

#: views/admin/import/confirm.php:78
msgid "Check the settings below, then click the green button to run the import."
msgstr "下の設定を確認し、緑のボタンをクリックしてインポートを実行します。"

#: views/admin/import/confirm.php:80
msgid "You can attempt to continue where it left off."
msgstr "中断したところから続けることができます。"

#: views/admin/import/confirm.php:88 views/admin/import/confirm.php:348
msgid "Confirm & Run Import"
msgstr "インポートの確認 & 実行"

#: views/admin/import/confirm.php:98
msgid "Continue from the last run"
msgstr "最後の実行から続行"

#: views/admin/import/confirm.php:102
msgid "Run from the beginning"
msgstr "最初から実行します"

#: views/admin/import/confirm.php:105 views/admin/import/process.php:99
msgid "Continue Import"
msgstr "インポートを続行"

#: views/admin/import/confirm.php:107
msgid "Run entire import from the beginning"
msgstr "最初から全体のインポートを実行します。"

#: views/admin/import/confirm.php:127
msgid "Import Summary"
msgstr "インポートの概要"

#: views/admin/import/confirm.php:133
msgid "Your max_execution_time is %s seconds"
msgstr "あなたのmax_execution_timeには、%s秒です"

#: views/admin/import/confirm.php:157
msgid "WP All Import will import the file <span style=\"color:#40acad;\">%s</span>, which is <span style=\"color:#000; font-weight:bold;\">%s</span>"
msgstr "WP All Importがファイルをインポートします <span style=\"color:#40acad;\">%s</span>, である <span style=\"color:#000; font-weight:bold;\">%s</span>"

#: views/admin/import/confirm.php:157
msgid "undefined"
msgstr "未定義"

#: views/admin/import/confirm.php:160
msgid "WP All Import will process the records matching the XPath expression: <span style=\"color:#46ba69; font-weight:bold;\">%s</span>"
msgstr "WP All Importは、XPath式に一致するレコードを処理します:<span style=\"color:#46ba69; font-weight:bold;\">%s</span>"

#: views/admin/import/confirm.php:162
msgid "WP All Import will process <span style=\"color:#46ba69; font-weight:bold;\">%s</span> rows in your file"
msgstr "WP All Importは、ファイル内の<span style=\"color:#46ba69; font-weight:bold;\">%s</span>行を処理します"

#: views/admin/import/confirm.php:164
msgid "WP All Import will process all %s <span style=\"color:#46ba69; font-weight:bold;\">&lt;%s&gt;</span> records in your file"
msgstr "WP All Import は、ファイル内のすべての%s <span style=\"color:#46ba69; font-weight:bold;\">&lt;%s&gt;</span>レコードを処理します"

#: views/admin/import/confirm.php:168
msgid "WP All Import will process only specified records: %s"
msgstr "WP All Importは、指定されたレコードを処理します：%s"

#: views/admin/import/confirm.php:175
msgid "Your unique key is <span style=\"color:#000; font-weight:bold;\">%s</span>"
msgstr "あなたのユニークなキーが<span style=\"color:#000; font-weight:bold;\">%s</span>であります"

#: views/admin/import/confirm.php:179
msgid "%ss previously imported by this import (ID: %s) with the same unique key will be updated."
msgstr "以前にこのインポート (ID: %s) によってインポート%ssの同じ一意のキーでは更新されます。"

#: views/admin/import/confirm.php:182
msgid "%ss previously imported by this import (ID: %s) that aren't present for this run of the import will be deleted."
msgstr "以前にこのインポートによってインポート%ss (ID: %s)インポートのこの実行のために存在していない削除されます。"

#: views/admin/import/confirm.php:186
msgid "%ss previously imported by this import (ID: %s) that aren't present for this run of the import will be set to draft."
msgstr "以前にこのインポートによってインポート%ss（ID: %s）インポートのこの実行のために存在していないドラフトに設定されます。"

#: views/admin/import/confirm.php:190
msgid "Records with unique keys that don't match any unique keys from %ss created by previous runs of this import (ID: %s) will be created."
msgstr "このインポートの以前の実行によって作成された%ssから任意の一意のキーと一致しない一意キーを持つレコード（ID: %s）が作成されます。"

#: views/admin/import/confirm.php:204
msgid "WP All Import will merge data into existing %ss, matching the following criteria: %s"
msgstr "WP All Importには、次の条件に一致する、既存 %ss,にデータをマージします：%s"

#: views/admin/import/confirm.php:207
msgid "Existing data will be updated with the data specified in this import."
msgstr "既存のデータは、このインポートで指定されたデータで更新されます。"

#: views/admin/import/confirm.php:210
msgid "Next %s data will be updated, <strong>all other data will be left alone</strong>"
msgstr "次に%sのデータが更新され、 <strong>他のすべてのデータがそのまま残されます</strong>"

#: views/admin/import/confirm.php:214
msgid "status"
msgstr "ステータス"

#: views/admin/import/confirm.php:217
msgid "title"
msgstr "タイトル"

#: views/admin/import/confirm.php:220
msgid "slug"
msgstr "slug"

#: views/admin/import/confirm.php:223
msgid "content"
msgstr "コンテンツ"

#: views/admin/import/confirm.php:226
msgid "excerpt"
msgstr "抜粋"

#: views/admin/import/confirm.php:229
msgid "dates"
msgstr "日付"

#: views/admin/import/confirm.php:232
msgid "menu order"
msgstr "メニュー順"

#: views/admin/import/confirm.php:235
msgid "parent post"
msgstr "親投稿"

#: views/admin/import/confirm.php:238
msgid "post type"
msgstr "編集する投稿タイプを選択"

#: views/admin/import/confirm.php:241
msgid "attachments"
msgstr "添付ファイル"

#: views/admin/import/confirm.php:248
msgid "all advanced custom fields"
msgstr "すべてのアドバンスカスタムフィールド"

#: views/admin/import/confirm.php:251
msgid "only ACF presented in import options"
msgstr "唯一のACFインポートオプションで提供"

#: views/admin/import/confirm.php:254
msgid "only these ACF : %s"
msgstr "これらのACF : %s"

#: views/admin/import/confirm.php:257
msgid "all ACF except these: %s"
msgstr "これらを除くすべてのACF: %s"

#: views/admin/import/confirm.php:267
msgid "old images will be updated with new"
msgstr "古い画像は新しい画像に更新されます"

#: views/admin/import/confirm.php:270
msgid "only new images will be added"
msgstr "新しい画像のみが追加されます"

#: views/admin/import/confirm.php:280
msgid "all custom fields"
msgstr "すべてのカスタムフィールド"

#: views/admin/import/confirm.php:283
msgid "only these custom fields : %s"
msgstr "これらのカスタムフィールドのみ : %s"

#: views/admin/import/confirm.php:286
msgid "all cusom fields except these: %s"
msgstr "これらを除くすべてのカスタムフィールド: %s"

#: views/admin/import/confirm.php:296
msgid "remove existing taxonomies, add new taxonomies"
msgstr "既存のタクソノミーを削除し、新しいタクソノミーを追加する"

#: views/admin/import/confirm.php:299
msgid "only add new"
msgstr "新しく追加する"

#: views/admin/import/confirm.php:302
msgid "update only these taxonomies: %s , leave the rest alone"
msgstr "これらの分類だけを更新する： %s ,残りを一人で残す"

#: views/admin/import/confirm.php:305
msgid "leave these taxonomies: %s alone, update all others"
msgstr "これらの分類法を残す：%sの一人で、他のすべてを更新"

#: views/admin/import/confirm.php:316
msgid "New %ss will be created from records that don't match the above criteria."
msgstr "新しい%ssは上記の基準に一致しないレコードから作成されます。"

#: views/admin/import/confirm.php:322
msgid "High-Speed, Small File Processing enabled. Your import will fail if it takes longer than your server's max_execution_time."
msgstr "高速、小型ファイル処理が有効になっています。それはあなたのサーバーのmax_execution_timeはより長い時間がかかる場合は、あなたのインポートは失敗します。"

#: views/admin/import/confirm.php:324
msgid "Piece By Piece Processing enabled. %s records will be processed each iteration. If it takes longer than your server's max_execution_time to process %s records, your import will fail."
msgstr "少しずつでは有効になって処理します。 %sのレコードは、各反復が処理されます。それは%sのレコードを処理するために、サーバーのmax_execution_timeはより長い時間がかかる場合は、インポートは失敗します。"

#: views/admin/import/confirm.php:328
msgid "Your file will be split into %s records chunks before processing."
msgstr "あなたのファイルが処理する前に、%sのレコードのチャンクに分割されます。"

#: views/admin/import/confirm.php:332
msgid "do_action calls will be disabled in wp_insert_post and wp_insert_attachment during the import."
msgstr "do_actionコールは、インポート時にwp_insert_postとwp_insert_attachmentに無効になります。"

#: views/admin/import/confirm.php:351
msgid "or go back to Step 4"
msgstr "またはステップ4に戻ります"

#: views/admin/import/confirm.php:353
msgid "or go back to Manage Imports"
msgstr "インポートの管理に戻る"

#: views/admin/import/element.php:34 views/admin/import/element.php:221
msgid "Continue to Step 3"
msgstr "ステップ3に進みます"

#: views/admin/import/element.php:47
msgid "What element are you looking for?"
msgstr "あなたはどんな要素を探していますか？"

#: views/admin/import/element.php:76
msgid "of <span class=\"wpallimport-elements-count-info\">%s</span>"
msgstr "の <span class=\"wpallimport-elements-count-info\">%s</span>"

#: views/admin/import/element.php:94
msgid "Set delimiter for CSV fields:"
msgstr "CSVフィールドの区切り文字を設定："

#: views/admin/import/element.php:115
msgid "Each <span>&lt;<span class=\"root_element\">%s</span>&gt;</span> element will be imported into a <span>New %s</span>"
msgstr "各 <span>&lt;<span class=\"root_element\">%s</span>&gt;</span> 要素は<span>新しい%s </span>要素にインポートされます。"

#: views/admin/import/element.php:119
msgid "Data in <span>&lt;<span class=\"root_element\">%s</span>&gt;</span> elements will be imported to <span>%s</span>"
msgstr "<span>&lt;<span class=\"root_element\">%s</span>&gt;</span>のデータ、要素は<span>%s</span>にインポートされます"

#: views/admin/import/element.php:124
msgid "This doesn't look right, try manually selecting a different root element on the left."
msgstr "これは正しく表示されません。左側の別のルート要素を手動で選択してみてください。"

#: views/admin/import/element.php:136
msgid "Add Filtering Options"
msgstr "フィルタリングオプションを追加する"

#: views/admin/import/element.php:143 views/admin/import/element.php:195
msgid "Element"
msgstr "要素"

#: views/admin/import/element.php:144 views/admin/import/element.php:196
msgid "Rule"
msgstr "ルール"

#: views/admin/import/element.php:145 views/admin/import/element.php:197
#: views/admin/import/options/_reimport_options.php:42
#: views/admin/import/options/_reimport_taxonomies_options.php:46
#: views/admin/import/options/_reimport_taxonomies_template.php:125
#: views/admin/import/options/_reimport_template.php:107
#: views/admin/import/template/_custom_fields_template.php:52
#: views/admin/import/template/_custom_fields_template.php:86
#: views/admin/import/template/_custom_fields_template.php:295
#: views/admin/import/template/_custom_fields_template.php:429
#: views/admin/import/template/_term_meta_template.php:52
#: views/admin/import/template/_term_meta_template.php:86
#: views/admin/import/template/_term_meta_template.php:295
#: views/admin/import/template/_term_meta_template.php:429
msgid "Value"
msgstr "値"

#: views/admin/import/element.php:151
msgid "Select Element"
msgstr "要素を選択"

#: views/admin/import/element.php:157
msgid "Select Rule"
msgstr "ルールを選択"

#: views/admin/import/element.php:158
msgid "equals"
msgstr "等しい"

#: views/admin/import/element.php:159
msgid "not equals"
msgstr "等しくありません"

#: views/admin/import/element.php:160
msgid "greater than"
msgstr "大なり"

#: views/admin/import/element.php:161
msgid "equals or greater than"
msgstr "等しいかより大きい"

#: views/admin/import/element.php:162
msgid "less than"
msgstr "最小化"

#: views/admin/import/element.php:163
msgid "equals or less than"
msgstr "等しいまたは未満"

#: views/admin/import/element.php:164
msgid "contains"
msgstr "含む"

#: views/admin/import/element.php:165
msgid "not contains"
msgstr "含まれていません"

#: views/admin/import/element.php:166
msgid "is empty"
msgstr "空である"

#: views/admin/import/element.php:167
msgid "is not empty"
msgstr "空ではない"

#: views/admin/import/element.php:174
msgid "Add Rule"
msgstr "ルールを追加"

#: views/admin/import/element.php:183
#: views/admin/import/options/_settings_template.php:174
msgid "XPath"
msgstr "XPath"

#: views/admin/import/element.php:198
msgid "Condition"
msgstr "状態"

#: views/admin/import/element.php:203
msgid "No filtering options. Add filtering options to only import records matching some specified criteria."
msgstr "いいえフィルタリングオプションはありません。いくつかの指定された条件に一致するのみインポートレコードをフィルタリングオプションを追加します。"

#: views/admin/import/element.php:208
msgid "Apply Filters To XPath"
msgstr "XPathのにフィルタを適用"

#: views/admin/import/element.php:217
msgid "Back to Step 1"
msgstr "ステップ1に戻る"

#: views/admin/import/evaluate.php:3
msgid "<span class=\"matches_count\">%s</span> <strong>%s</strong> will be imported"
msgstr "<span class=\"matches_count\">%s</span> <strong>%s</strong> インポートされます"

#: views/admin/import/evaluate.php:3
msgid "row"
msgid_plural "rows"
msgstr[0] "行"

#: views/admin/import/evaluate.php:5
msgid "<span class=\"matches_count\">%s</span> <strong>%s</strong> %s will be imported"
msgstr "<span class=\"matches_count\">%s</span>の<strong>%s</strong> %sがインポートされます"

#: views/admin/import/evaluate.php:7
msgid "Click an element to select it, or scroll down to add filtering options."
msgstr "それを選択するか、フィルタリングオプションを追加するには、スクロールダウンする要素をクリックします。"

#: views/admin/import/evaluate.php:9
#: views/admin/import/evaluate_variations.php:5
msgid "<strong>Note</strong>: Highlighting is turned off since can be very slow on large sets of elements."
msgstr "<strong>注</strong> ：要素の大規模なセットに非常に遅くなることがありますので、強調表示がオフになっています。"

#: views/admin/import/evaluate_variations.php:3
msgid "Current selection matches <span class=\"matches_count\">%s</span> %s."
msgstr "現在の選択は<span class=\"matches_count\">%s</span>の%s.の一致します。"

#: views/admin/import/evaluate_variations.php:14
msgid "#<strong>%s</strong> out of <strong>%s</strong>"
msgstr "#<strong>%s</strong> 中の <strong>%s</strong>"

#: views/admin/import/index.php:69
msgid "First, specify how you want to import your data"
msgstr "まず、あなたがあなたのデータをインポートする方法を指定します"

#: views/admin/import/index.php:71
msgid "First, specify previously exported file"
msgstr "まず、以前にエクスポートしたファイルを指定します"

#: views/admin/import/index.php:72
msgid "The data in this file can be modified, but the structure of the file (column/element names) should not change."
msgstr "このファイル内のデータを変更することができますが、ファイル（列/要素名）の構造は変更しないでください。"

#: views/admin/import/index.php:76
#: views/admin/import/options/_import_file.php:74
msgid "Upload a file"
msgstr "ファイルをアップロード"

#: views/admin/import/index.php:80
#: views/admin/import/options/_import_file.php:78
msgid "Download from URL"
msgstr "URLからダウンロード"

#: views/admin/import/index.php:84
#: views/admin/import/options/_import_file.php:82
msgid "Use existing file"
msgstr "既存のファイルを使用します"

#: views/admin/import/index.php:94
#: views/admin/import/options/_import_file.php:92
msgid "Click here to select file from your computer..."
msgstr "お使いのコンピュータからファイルを選択するには、ここをクリックしてください..."

#: views/admin/import/index.php:97
msgid "Upload Complete"
msgstr "アップロード完了"

#: views/admin/import/index.php:119
msgid "<strong>Hint:</strong> After you create this import, you can schedule it to run automatically, on a pre-defined schedule, with cron jobs. If anything in your file has changed, WP All Import can update your site with the changed data automatically."
msgstr "<strong>ヒント：</strong>このインポートを作成した後、あなたはcronジョブで、事前に定義されたスケジュールに基づいて、自動的に実行するようにスケジュールすることができます。あなたのファイルで何かが変更されている場合は、WPのすべてのインポートが自動的に変更されたデータを使用してサイトを更新することができます。"

#: views/admin/import/index.php:159
#: views/admin/import/options/_import_file.php:144
msgid "Select a previously uploaded file"
msgstr "以前にアップロードしたファイルを選択します"

#: views/admin/import/index.php:168
#: views/admin/import/options/_import_file.php:156
msgid "Upload files to <strong>%s</strong> and they will appear in this list"
msgstr "Cargar archivos a <strong>%s</strong> y ellos aparecerán en esta lista"

#: views/admin/import/index.php:183
msgid "Import data from this file into..."
msgstr "このファイルからデータをインポート..."

#: views/admin/import/index.php:187
msgid "New Items"
msgstr "新しいアイテム"

#: views/admin/import/index.php:191
msgid "Existing Items"
msgstr "既存のアイテム"

#: views/admin/import/index.php:214
msgid "Taxonomies"
msgstr "分類"

#: views/admin/import/index.php:218
#: views/admin/import/options/_settings_template.php:81
msgid "Users"
msgstr "ユーザー"

#: views/admin/import/index.php:244
msgid "Create new"
msgstr "新規作成"

#: views/admin/import/index.php:245
msgid "Import to existing"
msgstr "既存のインポート"

#: views/admin/import/index.php:248
msgid "for each record in my data file."
msgstr "のデータファイルの各レコードとして作成。"

#: views/admin/import/index.php:249
msgid "and update some or all of their data."
msgstr "そのデータの一部または全部を更新します。"

#: views/admin/import/index.php:298
msgid "Select taxonomy to import into..."
msgstr "インポートする分類を選択してください."

#: views/admin/import/index.php:300
#: views/admin/import/options/_settings_template.php:56
msgid "Select Taxonomy"
msgstr "分類選択"

#: views/admin/import/index.php:309
#: views/admin/import/options/_settings_template.php:151
msgid "The User Add-On is Required to Import Users"
msgstr "ユーザー インポートのアドオンはユーザーのインポートに必要です"

#: views/admin/import/index.php:310
#: views/admin/import/options/_settings_template.php:152
msgid "Purchase the User Add-On"
msgstr "ユーザーインポートアドオンの購入"

#: views/admin/import/index.php:315
#: views/admin/import/options/_settings_template.php:157
msgid "The WooCommerce Add-On is Required to Import Products"
msgstr "WooCommerce アドオンはインポートに必要です"

#: views/admin/import/index.php:316 views/admin/import/index.php:326
#: views/admin/import/index.php:334
#: views/admin/import/options/_settings_template.php:158
#: views/admin/import/options/_settings_template.php:164
#: views/admin/import/options/_settings_template.php:168
msgid "Purchase the WooCommerce Add-On Pro"
msgstr "WooCommerceアドオンプロを購入する"

#: views/admin/import/index.php:322
msgid "The Pro version of the WooCommerce Add-On is required to Import Orders, but you have the free version installed"
msgstr "WooCommerce アドオンの Pro バージョンがインポートの順序、する必要が無料版をインストールします。"

#: views/admin/import/index.php:324
#: views/admin/import/options/_settings_template.php:163
msgid "The WooCommerce Add-On Pro is Required to Import Orders"
msgstr "WooCommerce アドオン Pro は、インポートの順序に必要です"

#: views/admin/import/index.php:330
msgid "The Pro version of the WooCommerce Add-On is required to Import Coupons, but you have the free version installed"
msgstr "WooCommerce アドオンの Pro バージョンがインポート クーポンに必要が無料版をインストールします。"

#: views/admin/import/index.php:332
#: views/admin/import/options/_settings_template.php:167
msgid "The WooCommerce Add-On Pro is Required to Import Coupons"
msgstr "WooCommerce アドオン Pro はインポート クーポンに必要です"

#: views/admin/import/index.php:340
msgid "In Step 4, you will map the records in your file to the existing items on your site and specify which data points will be updated and which will be left alone."
msgstr "ステップ4では、あなたのサイト上の既存のアイテムにファイル内のレコードをマップし、データ点が更新され、放置されるかを指定します。"

#: views/admin/import/index.php:341
msgid "The Existing Items option is commonly used to update existing products with new stock quantities while leaving all their other data alone, update properties on your site with new pricing, etc."
msgstr "一人ですべての他のデータを残したまま、既存のアイテムのオプションは、一般的になど、新価格であなたのサイトのプロパティを更新し、新たな在庫数量を持つ既存の製品を更新するために使用されます"

#: views/admin/import/index.php:352
#: views/admin/import/options/_import_file.php:36
msgid "File upload rejected by server"
msgstr "ファイルのアップロード サーバーによって拒否されました"

#: views/admin/import/index.php:353
#: views/admin/import/options/_import_file.php:37
msgid "Contact your host and have them check your server's error log."
msgstr "あなたのホストに連絡し、サーバーのエラー ログを確認してもらいます。"

#: views/admin/import/index.php:367
msgid "Please verify that the file you using is a valid %s file."
msgstr "ファイルを使用してあなたが確認してください有効な %s ファイルです。"

#: views/admin/import/index.php:382
msgid "Skip to Step 4"
msgstr "ステップ4をスキップ"

#: views/admin/import/index.php:383
msgid "Continue to Step 2"
msgstr "ステップ2に進みます"

#: views/admin/import/options.php:119
#: views/admin/import/options/_reimport_taxonomies_template.php:22
#: views/admin/import/options/_reimport_taxonomies_template.php:89
#: views/admin/import/options/_reimport_template.php:14
#: views/admin/import/options/_reimport_template.php:81
msgid "Record Matching"
msgstr "レコードマッチング"

#: views/admin/import/options.php:122
msgid "Record Matching is how WP All Import matches records in your file with posts that already exist WordPress."
msgstr "レコードマッチングはWPすべてのインポートがすでにWordPressの存在ポストにあなたのファイル内のレコードを照合する方法です。"

#: views/admin/import/options.php:126
msgid "Record Matching is most commonly used to tell WP All Import how to match up records in your file with posts WP All Import has already created on your site, so that if your file is updated with new data, WP All Import can update your posts accordingly."
msgstr "レコードマッチングは、最も一般的にあなたのファイルが新しいデータで更新される場合、WPのすべてのインポートがそれに応じてあなたの記事を更新できるように、WPのすべてのインポートがすでにあなたのサイト上で作成された記事で、ファイル内のレコードを一致させるためにどのようにWPすべてのインポートを指示するために使用され。"

#: views/admin/import/options.php:131
msgid "AUTOMATIC RECORD MATCHING"
msgstr "レコードの自動マッチング"

#: views/admin/import/options.php:134
msgid "Automatic Record Matching allows WP All Import to update records that were imported or updated during the last run of this same import."
msgstr "自動レコードマッチングでは、WP All Importを使用して、この同じインポートの前回の実行時にインポートまたは更新されたレコードを更新できます。"

#: views/admin/import/options.php:138
msgid "Your unique key must be UNIQUE for each record in your feed. Make sure you get it right - you can't change it later. You'll have to re-create your import."
msgstr "あなたのユニークなキーは、フィード内の各レコードの一意である必要があります。あなたは右のそれを得ることを確認してください - あなたは後でそれを変更することはできません。あなたのインポートを再作成する必要があります。"

#: views/admin/import/options.php:143
msgid "MANUAL RECORD MATCHING"
msgstr "手動レコードマッチング"

#: views/admin/import/options.php:146
msgid "Manual record matching allows WP All Import to update any records, even records that were not imported with WP All Import, or are part of a different import."
msgstr "マニュアルレコードマッチングは、WPすべてのインポートがすべてのレコードを更新することを可能にするWPすべてのインポートでインポートされなかったとしても、レコード、または異なる輸入の一部です。"

#: views/admin/import/options/_buttons_template.php:2
msgid "To run the import, click Run Import on the Manage Imports page."
msgstr "インポートを実行するには、輸入を管理]ページの[実行インポート]をクリックします。"

#: views/admin/import/options/_buttons_template.php:11
msgid "Back to Step 3"
msgstr "ステップ3に戻る"

#: views/admin/import/options/_buttons_template.php:15
msgid "Save Only"
msgstr "保存のみ"

#: views/admin/import/options/_buttons_template.php:18
msgid "Continue"
msgstr "続行"

#: views/admin/import/options/_buttons_template.php:21
#: views/admin/import/template.php:227
msgid "Back to Manage Imports"
msgstr "インポートの管理に戻る"

#: views/admin/import/options/_buttons_template.php:22
msgid "Save Import Configuration"
msgstr "インポート設定を保存"

#: views/admin/import/options/_import_file.php:62
msgid "Import File"
msgstr "ファイルのインポート"

#: views/admin/import/options/_import_file.php:71
msgid "Specify the location of the file to use for future runs of this import."
msgstr "このインポートの将来の実行に使用するファイルの場所を指定します。"

#: views/admin/import/options/_import_file.php:94
msgid "<span>Upload Complete</span> - "
msgstr "<span>アップロードが完了</span>-"

#: views/admin/import/options/_import_file.php:106
msgid "Upload"
msgstr "アップロード"

#: views/admin/import/options/_reimport_options.php:2
#: views/admin/import/options/_reimport_taxonomies_options.php:11
msgid "When WP All Import finds new or changed data..."
msgstr "WP All Importは、新規または変更されたデータを見つけます..."

#: views/admin/import/options/_reimport_options.php:4
#: views/admin/import/options/_reimport_taxonomies_options.php:13
msgid "If this import is run again and WP All Import finds new or changed data..."
msgstr "このインポートを再度実行するかどうか、新しい検索 WP をすべてインポートまたは変更されたデータ."

#: views/admin/import/options/_reimport_options.php:9
msgid "Create new posts from records newly present in your file"
msgstr "あなたのファイルに新しく存在レコードから、新しい投稿を作成します。"

#: views/admin/import/options/_reimport_options.php:11
msgid "New posts will only be created when ID column is present and value in ID column is unique."
msgstr "ID列が存在し、ID列の値が一意である場合に、新しいポストにのみ作成されます。"

#: views/admin/import/options/_reimport_options.php:18
msgid "Delete posts that are no longer present in your file"
msgstr "ファイルに存在しなくなった投稿を削除する"

#: views/admin/import/options/_reimport_options.php:20
#: views/admin/import/options/_reimport_taxonomies_options.php:29
msgid "Records removed from the import file can only be deleted when importing into New Items. This feature cannot be enabled when importing into Existing Items."
msgstr "インポート ファイルから削除されたレコードは、新しいアイテムにインポートする場合にのみ削除できます。既存のアイテムにインポートする場合、この機能を有効にできません。"

#: views/admin/import/options/_reimport_options.php:27
msgid "Do not remove attachments"
msgstr "添付ファイルを削除しない"

#: views/admin/import/options/_reimport_options.php:32
#: views/admin/import/options/_reimport_taxonomies_options.php:36
msgid "Do not remove images"
msgstr "画像を削除しない"

#: views/admin/import/options/_reimport_options.php:37
msgid "Instead of deletion, set Custom Field"
msgstr "削除の代わりにカスタムフィールドを設定する"

#: views/admin/import/options/_reimport_options.php:40
#: views/admin/import/options/_reimport_taxonomies_options.php:44
#: views/admin/import/options/_reimport_taxonomies_options.php:73
#: views/admin/import/options/_reimport_taxonomies_template.php:106
#: views/admin/import/options/_reimport_taxonomies_template.php:123
#: views/admin/import/options/_reimport_template.php:105
#: views/admin/import/template/_custom_fields_template.php:51
#: views/admin/import/template/_term_meta_template.php:51
msgid "Name"
msgstr "名前"

#: views/admin/import/options/_reimport_options.php:50
msgid "Instead of deletion, change post status to Draft"
msgstr "削除の代わりに、ステータスを下書きに変更します"

#: views/admin/import/options/_reimport_options.php:57
msgid "Update existing posts with changed data in your file"
msgstr "既存の投稿をファイル内の変更されたデータで更新する"

#: views/admin/import/options/_reimport_options.php:59
#: views/admin/import/options/_reimport_taxonomies_options.php:58
msgid "These options will only be used if you run this import again later. All data is imported the first time you run an import."
msgstr "後で再びこのインポートを実行する場合、これらのオプションは使用されます。すべてのデータは、インポートを最初に実行したときにインポートされます。"

#: views/admin/import/options/_reimport_options.php:63
#: views/admin/import/options/_reimport_taxonomies_options.php:62
msgid "Update all data"
msgstr "すべてのデータ更新"

#: views/admin/import/options/_reimport_options.php:66
#: views/admin/import/options/_reimport_taxonomies_options.php:65
msgid "Choose which data to update"
msgstr "更新するデータを選択します"

#: views/admin/import/options/_reimport_options.php:69
#: views/admin/import/options/_reimport_taxonomies_options.php:68
msgid "Unselect All"
msgstr "すべての選択を解除"

#: views/admin/import/options/_reimport_options.php:69
#: views/admin/import/options/_reimport_taxonomies_options.php:68
msgid "Select All"
msgstr "全選択"

#: views/admin/import/options/_reimport_options.php:74
msgid "Post status"
msgstr "投稿ステータス"

#: views/admin/import/options/_reimport_options.php:75
msgid "Hint: uncheck this box to keep trashed posts in the trash."
msgstr "ヒント: ゴミ箱にゴミ箱の注文を残すには、このボックスをオフします。"

#: views/admin/import/options/_reimport_options.php:80
#: views/admin/import/options/_reimport_template.php:98
msgid "Title"
msgstr "タイトル"

#: views/admin/import/options/_reimport_options.php:85
msgid "Author"
msgstr "販売者"

#: views/admin/import/options/_reimport_options.php:90
#: views/admin/import/options/_reimport_taxonomies_options.php:78
#: views/admin/import/options/_reimport_taxonomies_template.php:113
#: views/admin/import/template/_other_template.php:290
msgid "Slug"
msgstr "Slug"

#: views/admin/import/options/_reimport_options.php:95
#: views/admin/import/options/_reimport_template.php:100
msgid "Content"
msgstr "コンテンツ"

#: views/admin/import/options/_reimport_options.php:100
msgid "Excerpt/Short Description"
msgstr "抜粋/簡単な説明"

#: views/admin/import/options/_reimport_options.php:105
msgid "Dates"
msgstr "日付"

#: views/admin/import/options/_reimport_options.php:110
msgid "Menu order"
msgstr "表示順番"

#: views/admin/import/options/_reimport_options.php:115
msgid "Parent post"
msgstr "親投稿"

#: views/admin/import/options/_reimport_options.php:120
msgid "Post type"
msgstr "編集する投稿タイプを選択"

#: views/admin/import/options/_reimport_options.php:125
msgid "Comment status"
msgstr "コメント ステータス"

#: views/admin/import/options/_reimport_options.php:130
msgid "Attachments"
msgstr "添付"

#: views/admin/import/options/_reimport_options.php:144
#: views/admin/import/options/_reimport_taxonomies_options.php:107
msgid "This will keep the featured image if it exists, so you could modify the post image manually, and then do a reimport, and it would not overwrite the manually modified post image."
msgstr "手動でポスト画像を変更し、再インポートを行うと、それは手動で変更後のイメージを上書きしませんでしたので、これは、それが存在する場合、機能のイメージを維持します。"

#: views/admin/import/options/_reimport_options.php:148
#: views/admin/import/options/_reimport_taxonomies_options.php:111
msgid "Update all images"
msgstr "すべての画像を更新"

#: views/admin/import/options/_reimport_options.php:154
msgid "Don't touch existing images, append new images"
msgstr "新しい画像を追加し、既存の画像をそのままにする。"

#: views/admin/import/options/_reimport_options.php:163
#: views/admin/import/template/_custom_fields_template.php:17
msgid "Custom Fields"
msgstr "カスタム フィールド"

#: views/admin/import/options/_reimport_options.php:164
msgid "If Keep Custom Fields box is checked, it will keep all Custom Fields, and add any new Custom Fields specified in Custom Fields section, as long as they do not overwrite existing fields. If 'Only keep this Custom Fields' is specified, it will only keep the specified fields."
msgstr "キープカスタムフィールドのチェックボックスをオンにすると、それはすべてのカスタムフィールドを維持し、限り、彼らは既存のフィールドを上書きしないように、カスタムフィールド]セクションで指定された新しいカスタムフィールドを追加します。 'のみ続けるこのカスタムフィールド」が指定されている場合、それだけで指定したフィールドを保持します。"

#: views/admin/import/options/_reimport_options.php:168
msgid "Update all Custom Fields"
msgstr "カスタムフィールドを更新"

#: views/admin/import/options/_reimport_options.php:172
msgid "Update only these Custom Fields, leave the rest alone"
msgstr "これらのカスタムフィールドのみを更新し、残りはそのままにします"

#: views/admin/import/options/_reimport_options.php:180
msgid "Leave these fields alone, update all other Custom Fields"
msgstr "他のすべてのカスタムフィールドを更新し、単独でこれらのフィールドを残します"

#: views/admin/import/options/_reimport_options.php:192
msgid "Taxonomies (incl. Categories and Tags)"
msgstr "タクソノミー(incl. カテゴリとタグ）"

#: views/admin/import/options/_reimport_options.php:206
msgid "Leave these taxonomies alone, update all others"
msgstr "これらのタクソノミーだけを残し、他のすべてを更新する"

#: views/admin/import/options/_reimport_options.php:214
msgid "Update only these taxonomies, leave the rest alone"
msgstr "これらのタクソノミーのみを更新し、残りは単独で残す"

#: views/admin/import/options/_reimport_options.php:222
msgid "Remove existing taxonomies, add new taxonomies"
msgstr "既存のタクソノミーを削除し、新しいタクソノミーを追加する"

#: views/admin/import/options/_reimport_options.php:226
msgid "Only add new"
msgstr "新規追加のみ"

#: views/admin/import/options/_reimport_taxonomies_options.php:18
msgid "Create new %s from records newly present in your file"
msgstr "あなたのファイルに新しく存在するレコードから新しい%sを作成する"

#: views/admin/import/options/_reimport_taxonomies_options.php:20
msgid "New %s will only be created when ID column is present and value in ID column is unique."
msgstr "新しい %s は、ID 列があり、ID 列の値が一意場合にのみ作成されます。"

#: views/admin/import/options/_reimport_taxonomies_options.php:27
msgid "Delete %s that are no longer present in your file"
msgstr "ファイルに存在しない%sを削除する"

#: views/admin/import/options/_reimport_taxonomies_options.php:41
msgid "Instead of deletion, set Term Meta"
msgstr "削除ではなく用語 Meta を設定します。"

#: views/admin/import/options/_reimport_taxonomies_options.php:56
msgid "Update existing %s with changed data in your file"
msgstr "既存の%sをファイル内の変更されたデータで更新する"

#: views/admin/import/options/_reimport_taxonomies_options.php:83
msgid "Description"
msgstr "詳細"

#: views/admin/import/options/_reimport_taxonomies_options.php:93
#: views/admin/import/template/_term_other_template.php:23
msgid "Parent term"
msgstr "親ターム"

#: views/admin/import/options/_reimport_taxonomies_options.php:119
#: views/admin/import/template/_term_meta_template.php:17
msgid "Term Meta"
msgstr "タームメタ"

#: views/admin/import/options/_reimport_taxonomies_options.php:120
msgid "If Keep Term Meta box is checked, it will keep all Term Meta, and add any new Term Meta specified in Term Meta section, as long as they do not overwrite existing fields. If 'Only keep this Term Meta' is specified, it will only keep the specified fields."
msgstr "タームメタを保持するチェックボックスがオンの場合、既存のフィールドを上書きしないかぎり、すべてのタームメタを保持し、タームメタセクションで指定された新しいTerm Metaを追加します。 「この期間をメタのみ保持する」が指定されている場合、指定されたフィールドのみが保持されます。"

#: views/admin/import/options/_reimport_taxonomies_options.php:124
msgid "Update all Term Meta"
msgstr "すべてのタームメタ を更新します。"

#: views/admin/import/options/_reimport_taxonomies_options.php:128
msgid "Update only these Term Meta, leave the rest alone"
msgstr "これらのタクソノミーのみを更新し、残りは単独で残す"

#: views/admin/import/options/_reimport_taxonomies_options.php:136
msgid "Leave these fields alone, update all other Term Meta"
msgstr "単独でこれらのフィールドを残して、他のすべての用語のメタを更新"

#: views/admin/import/options/_reimport_taxonomies_template.php:30
#: views/admin/import/options/_reimport_template.php:22
msgid "Choose how exported data will be re-imported."
msgstr "エクスポートされたデータをどのように再インポートするかを選択します。"

#: views/admin/import/options/_reimport_taxonomies_template.php:36
#: views/admin/import/options/_reimport_template.php:28
msgid "WP All Import will create new %s for each unique record in your file."
msgstr "WP All Importは、新しい%sのあなたのファイル内の各一意のレコードのために作成されます。"

#: views/admin/import/options/_reimport_taxonomies_template.php:49
#: views/admin/import/options/_reimport_template.php:41
msgid "WP All Import will associate records in your file with %s it has already created from previous runs of this import based on the Unique Identifier."
msgstr "WP All Importは、%sのそれは既に一意識別子に基づいて、このインポートの以前の実行から作成していると、ファイル内のレコードを関連付けます。"

#: views/admin/import/options/_reimport_taxonomies_template.php:52
#: views/admin/import/options/_reimport_template.php:44
msgid "Unique Identifier"
msgstr "一意の識別子"

#: views/admin/import/options/_reimport_taxonomies_template.php:58
#: views/admin/import/options/_reimport_taxonomies_template.php:69
#: views/admin/import/options/_reimport_template.php:50
#: views/admin/import/options/_reimport_template.php:61
msgid "Auto-detect"
msgstr "自動検知"

#: views/admin/import/options/_reimport_taxonomies_template.php:61
#: views/admin/import/options/_reimport_template.php:53
#: views/admin/manage/index.php:327
msgid "Edit"
msgstr "編集 "

#: views/admin/import/options/_reimport_taxonomies_template.php:62
#: views/admin/import/options/_reimport_template.php:54
msgid "Warning: Are you sure you want to edit the Unique Identifier?"
msgstr "警告：あなたは一意の識別子を編集してもよろしいですか？"

#: views/admin/import/options/_reimport_taxonomies_template.php:63
#: views/admin/import/options/_reimport_template.php:55
msgid "It is recommended you delete all %s associated with this import before editing the unique identifier."
msgstr "あなたが一意の識別子を編集する前に、このインポートに関連付けられているすべての%sを削除することをお勧めします。"

#: views/admin/import/options/_reimport_taxonomies_template.php:64
#: views/admin/import/options/_reimport_template.php:56
msgid "Editing the unique identifier will dissociate all existing %s linked to this import. Future runs of the import will result in duplicates, as WP All Import will no longer be able to update these %s."
msgstr "一意の識別子を編集すると、このインポートにリンクされているすべての既存の%sが分離されます。 WP All Importはこれらの%sを更新することができなくなるので、インポートの将来の実行は重複を招きます。"

#: views/admin/import/options/_reimport_taxonomies_template.php:65
#: views/admin/import/options/_reimport_template.php:57
msgid "You really should just re-create your import, and pick the right unique identifier to start with."
msgstr "あなたは本当に自分のインポートを再作成し、で開始する権利一意の識別子を選択する必要があります。"

#: views/admin/import/options/_reimport_taxonomies_template.php:75
#: views/admin/import/options/_reimport_template.php:67
msgid "Drag an element, or combo of elements, to the box above. The Unique Identifier should be unique for each record in your file, and should stay the same even if your file is updated. Things like product IDs, titles, and SKUs are good Unique Identifiers because they probably won't change. Don't use a description or price, since that might be changed."
msgstr "上のボックスに、要素、または要素のコンボをドラッグします。一意識別子は、ファイル内のレコードごとに一意である必要があり、あなたのファイルが更新されていても同じままである必要があります。彼らはおそらく変更されませんので、製品ID、タイトル、およびSKUのようなものが良いユニークな識別子です。それは変更される場合がありますので、説明や価格を使用しないでください。"

#: views/admin/import/options/_reimport_taxonomies_template.php:76
#: views/admin/import/options/_reimport_template.php:68
msgid "If you run this import again with an updated file, the Unique Identifier allows WP All Import to correctly link the records in your updated file with the %s it will create right now. If multiple records in this file have the same Unique Identifier, only the first will be created. The others will be detected as duplicates."
msgstr "あなたが更新されたファイルで再びこのインポートを実行する場合は、一意の識別子がWPすべてのインポートが正しく%sのあなたの更新されたファイル内のレコードをリンクすることができ、それは今作成されます。このファイルに複数のレコードが同じ一意の識別子を使用している場合は、最初に作成されます。他の人が重複として検出されます。"

#: views/admin/import/options/_reimport_taxonomies_template.php:91
#: views/admin/import/options/_reimport_template.php:83
msgid "WP All Import will merge data into existing %s."
msgstr "WP All Import は、既存の %sにデータをマージします。"

#: views/admin/import/options/_reimport_taxonomies_template.php:101
msgid "Records in your file will be matched with %s on your site based on..."
msgstr "あなたのファイルのレコードは、あなたのサイトの%ssと一致します..."

#: views/admin/import/options/_reimport_taxonomies_template.php:120
#: views/admin/import/options/_reimport_template.php:103
msgid "Custom field"
msgstr "カスタムフィールド"

#: views/admin/import/options/_reimport_taxonomies_template.php:130
msgid "Term ID"
msgstr "ターム ID"

#: views/admin/import/options/_reimport_template.php:93
msgid "Records in your file will be matched with %ss on your site based on..."
msgstr "あなたのファイルのレコードは、あなたのサイトの%ssと一致します..."

#: views/admin/import/options/_reimport_template.php:113
msgid "Post ID"
msgstr "投稿ID"

#: views/admin/import/options/_settings_template.php:4
msgid "Configure Advanced Settings"
msgstr "詳細設定を構成します"

#: views/admin/import/options/_settings_template.php:11
msgid "Import Speed Optimization"
msgstr "インポート速度の最適化"

#: views/admin/import/options/_settings_template.php:15
msgid "High Speed Small File Processing"
msgstr "高速小ファイル処理"

#: views/admin/import/options/_settings_template.php:15
msgid "If the import takes longer than your server's timeout settings (max_execution_time, mod_fcgid read data timeout, etc.) it will fail."
msgstr "インポートがサーバーのタイムアウト設定（max_execution_time、mod_fcgid読み取りデータタイムアウトなど）よりも時間がかかる場合は、失敗します。"

#: views/admin/import/options/_settings_template.php:19
msgid "Iterative, Piece-by-Piece Processing"
msgstr "反復的なピース・バイ・ピース処理"

#: views/admin/import/options/_settings_template.php:23
msgid "In each iteration, process"
msgstr "各反復において、プロセス"

#: views/admin/import/options/_settings_template.php:23
#: views/admin/import/process.php:31
msgid "records"
msgstr "レコード"

#: views/admin/import/options/_settings_template.php:24
msgid "WP All Import must be able to process this many records in less than your server's timeout settings. If your import fails before completion, to troubleshoot you should lower this number. If you are importing images, especially high resolution images, high numbers here are probably a bad idea, since downloading the images can take lots of time - for example, 20 posts with 5 images each = 100 images. At 500Kb per image that's 50Mb that needs to be downloaded. Can your server download that before timing out? If not, the import will fail."
msgstr "WP All Importは、サーバーのタイムアウト設定よりも少なくても、この多くのレコードを処理できる必要があります。 完了前にインポートに失敗した場合は、トラブルシューティングのためにこの数を減らす必要があります。 イメージ、特に高解像度のイメージをインポートする場合は、イメージのダウンロードに時間がかかりすぎる可能性が高いため、ここでの数字は多分悪い考えです。 画像あたり500Kbでダウンロードする必要がある50MBです。 タイムアウトする前にサーバからダウンロードできますか？ そうでない場合、インポートは失敗します。"

#: views/admin/import/options/_settings_template.php:29
msgid "Split file up into <strong>"
msgstr "ファイルを分割する<strong>"

#: views/admin/import/options/_settings_template.php:30
msgid "This option will decrease the amount of slowdown experienced at the end of large imports. The slowdown is partially caused by the need for WP All Import to read deeper and deeper into the file on each successive iteration. Splitting the file into pieces means that, for example, instead of having to read 19000 records into a 20000 record file when importing the last 1000 records, WP All Import will just split it into 20 chunks, and then read the last chunk from the beginning."
msgstr "減速の量を減少させます。このオプションは、大規模な輸入の最後に経験しました。景気減速は、部分的に連続する各繰り返しでファイルに深く深く読むためにWPすべてのインポートの必要性によって引き起こされます。粉々にファイルを分割すると、たとえば、代わりに最後の1000個のレコードをインポートするときに20000レコードファイルに19000レコードを読み取るするので、WPすべてのインポートがちょうど20チャンクに分割し、その後、最初から最後のチャンクを読んで、ということを意味します。"

#: views/admin/import/options/_settings_template.php:37
msgid "Increase speed by disabling do_action calls in wp_insert_post during import."
msgstr "インポート中にwp_insert_postでdo_action呼び出しを無効にすることで高速化を図ります。"

#: views/admin/import/options/_settings_template.php:38
msgid "This option is for advanced users with knowledge of WordPress development. Your theme or plugins may require these calls when posts are created. Next action will be disabled: 'transition_post_status', 'save_post', 'pre_post_update', 'add_attachment', 'edit_attachment', 'edit_post', 'post_updated', 'wp_insert_post'. Verify your created posts work properly if you check this box."
msgstr "このオプションは、WordPressの開発の知識を持つ上級者向けの機能です。ポストが作成されたときにあなたのテーマやプラグインは、これらの呼び出しが必要な場合があります。次のアクションは無効になります： 'transition_post_status'、 'save_post'、 'pre_post_update'、 'ADD_ATTACHMENT'、 'edit_attachment'、 'edit_post'、 'post_updated'、 'wp_insert_post」。このチェックボックスをオンにした場合、あなたの作成したポストが正しく動作を確認してください。"

#: views/admin/import/options/_settings_template.php:43
msgid "Taxonomy Type"
msgstr "分類体系"

#: views/admin/import/options/_settings_template.php:44
msgid "Editing this will change the taxonomy type of the taxonomies processed by this import. Re-run the import for the changes to take effect."
msgstr "これを編集は、このインポートによって処理された分類の分類型を変更します。変更を反映のためインポートを再実行します。"

#: views/admin/import/options/_settings_template.php:46
msgid "Post Type"
msgstr "投稿タイプ"

#: views/admin/import/options/_settings_template.php:47
msgid "Editing this will change the post type of the posts processed by this import. Re-run the import for the changes to take effect."
msgstr "これを編集すると、このインポートによって処理されたポストのポストタイプを変更します。変更を有効にするには、インポートを再実行します。"

#: views/admin/import/options/_settings_template.php:175
msgid "Editing this can break your entire import. You will have to re-create it from scratch."
msgstr "これを編集すると、あなたの全体のインポートを破ることができます。あなたは最初からそれを再作成する必要があります。"

#: views/admin/import/options/_settings_template.php:180
msgid "Downloads"
msgstr "ダウンロード"

#: views/admin/import/options/_settings_template.php:183
msgid "Import Template"
msgstr "テンプレートをインポート"

#: views/admin/import/options/_settings_template.php:184
msgid "Import Bundle"
msgstr "インポートのバンドル"

#: views/admin/import/options/_settings_template.php:187
msgid "Other"
msgstr "その他"

#: views/admin/import/options/_settings_template.php:191
msgid "Import only specified records"
msgstr "指定されたレコードのみをインポートする"

#: views/admin/import/options/_settings_template.php:191
msgid "Enter records or record ranges separated by commas, e.g. <b>1,5,7-10</b> would import the first, the fifth, and the seventh to tenth."
msgstr "カンマで区切られたレコードまたはレコードの範囲を入力し、例えば<b>1,5,7-10</b> 、第1〜第5、及び第十に七インポートされます。"

#: views/admin/import/options/_settings_template.php:202
msgid "Delete source XML file after importing"
msgstr "インポート後にソースXMLファイルを削除します。"

#: views/admin/import/options/_settings_template.php:202
msgid "This setting takes effect only when script has access rights to perform the action, e.g. file is not deleted when pulled via HTTP or delete permission is not granted to the user that script is executed under."
msgstr "この設定は、スクリプトのアクションを実行するためのアクセス権を持っているHTTP経由で引っ張られたり、権限を削除すると、例えばファイルが削除されていませんが、スクリプトの下で実行されているユーザーに付与されていない場合にのみ有効になります。"

#: views/admin/import/options/_settings_template.php:209
msgid "Auto-Cloak Links"
msgstr "自動クロック リンク"

#: views/admin/import/options/_settings_template.php:209
msgid "Automatically process all links present in body of created post or page with <b>%s</b> plugin"
msgstr "自動的に<b>%s</b>プラグインで作成したポストやページの体内に存在するすべてのリンクを処理"

#: views/admin/import/options/_settings_template.php:217
#: views/admin/import/options/_settings_template.php:220
msgid "Use StreamReader instead of XMLReader to parse import file"
msgstr "インポート ファイルの解析に XMLReader の代わりに StreamReader を使用します。"

#: views/admin/import/options/_settings_template.php:217
msgid "WP All Import is being forced to use Stream Reader for all imports. Go to WP All Import ▸ Settings to modify this setting."
msgstr "WP すべてインポートは強制的にすべてのインポートのストリーム リーダーを使用します。WP すべてインポート ▸ 設定この設定を変更するに移動します。"

#: views/admin/import/options/_settings_template.php:220
#: views/admin/settings/index.php:188
msgid "XMLReader is much faster, but has a bug that sometimes prevents certain records from being imported with import files that contain special cases."
msgstr "XMLReaderははるかに高速ですが、特殊なケースを含むインポートファイルで特定のレコードがインポートされないことがあるバグがあります。"

#: views/admin/import/options/_settings_template.php:225
msgid "Friendly Name"
msgstr "フレンドリ名"

#: views/admin/import/preview.php:6 views/admin/import/preview_images.php:6
#: views/admin/import/preview_prices.php:6
#: views/admin/import/preview_taxonomies.php:6 views/admin/import/tag.php:8
msgid "<strong><input type=\"text\" value=\"%s\" name=\"tagno\" class=\"tagno\"/></strong><span class=\"out_of\"> of <strong class=\"pmxi_count\">%s</strong></span>"
msgstr "<strong><input type=\"text\" value=\"%s\" name=\"tagno\" class=\"tagno\"/></strong><span class=\"out_of\"> の <strong class=\"pmxi_count\">%s</strong></span>"

#: views/admin/import/preview_images.php:17
msgid "Test Images"
msgstr "テスト画像"

#: views/admin/import/preview_images.php:24
msgid "Click to test that your images are able to be accessed by WP All Import."
msgstr "あなたのイメージが WP すべてインポートにアクセスすることをテストするをクリックします。"

#: views/admin/import/preview_images.php:26
msgid "Run Test"
msgstr "テスト実行"

#: views/admin/import/preview_images.php:60
msgid "Retrieving images..."
msgstr "画像の取得中..."

#: views/admin/import/preview_images.php:64
msgid "WP All Import will import images from the following file paths:"
msgstr "WP All Importには、次のファイルパスから画像をインポートします："

#: views/admin/import/preview_images.php:65
msgid "Please ensure the images exists at these file paths"
msgstr "これらのファイルパスにイメージが存在することを確認してください"

#: views/admin/import/preview_images.php:73
#: views/admin/import/preview_images.php:115
#: views/admin/import/preview_images.php:154
msgid "Here are the above URLs, in &lt;img&gt; tags. "
msgstr "ここでの&lt;img&gt; タグ内の上記のURLは、あります。"

#: views/admin/import/preview_images.php:92
msgid "Searching images..."
msgstr "画像を検索しています."

#: views/admin/import/preview_images.php:96
msgid "WP All Import will import images from the media library"
msgstr "WP をすべてインポート メディア ライブラリからイメージをインポートします。"

#: views/admin/import/preview_images.php:97
msgid "Please ensure the images exists at media library"
msgstr "画像はメディア ライブラリに存在するか確認してください。"

#: views/admin/import/preview_images.php:141
msgid "Download in progress..."
msgstr "プログレスバーの表示"

#: views/admin/import/preview_images.php:145
msgid "WP All Import will attempt to import images from the following URLs:"
msgstr "WP All Import には、次のURLからイメージをインポートしようとします："

#: views/admin/import/preview_images.php:146
msgid "Please check the URLs to ensure they point to valid images"
msgstr "URLを確認して、有効な画像を指していることを確認してください"

#: views/admin/import/preview_images.php:169
msgid "Images not found for current record."
msgstr "現在のレコードで画像が見つかりません。"

#: views/admin/import/preview_prices.php:16
msgid "Preview Prices"
msgstr "プレビュー価格"

#: views/admin/import/preview_prices.php:18
msgid "Regular Price"
msgstr "通常価格"

#: views/admin/import/preview_prices.php:19
msgid "Sale Price"
msgstr "セール価格"

#: views/admin/import/preview_taxonomies.php:16
msgid "Test Taxonomies Hierarchy"
msgstr "タクソノミーの階層テスト"

#: views/admin/import/process.php:21
msgid "Import <span id=\"status\">in Progress</span>"
msgstr "<span id=\"status\">進行中</span>インポート"

#: views/admin/import/process.php:22
msgid "Importing may take some time. Please do not close your browser or refresh the page until the process is complete."
msgstr "インポートには多少時間がかかる場合があります。ブラウザを閉じるか、プロセスが完了するまで、ページを更新しないでください。"

#: views/admin/import/process.php:29
msgid "Time Elapsed"
msgstr "経過時間"

#: views/admin/import/process.php:31
msgid "Created"
msgstr "作成済み"

#: views/admin/import/process.php:31
msgid "Updated"
msgstr "更新済み"

#: views/admin/import/process.php:31
msgid "of"
msgstr "の"

#: views/admin/import/process.php:48
msgid "Import Complete!"
msgstr "インポート完了！"

#: views/admin/import/process.php:50
msgid "Duplicate records detected during import"
msgstr "重複レコードのインポート中に検出"

#: views/admin/import/process.php:50
msgid "The unique identifier is how WP All Import tells two items in your import file apart. If it is the same for two items, then the first item will be overwritten when the second is imported."
msgstr "一意の識別子は、離れてあなたのインポート ファイルの 2 つの項目が指示 WP をすべてインポートされます。2 つの項目と同じ場合、最初の項目は 2 番目がインポートされると上書きされます。"

#: views/admin/import/process.php:52
msgid "The file you are importing has %s records, but WP All Import only created <span class=\"inserted_count\"></span> %s. It detected the other records in your file as duplicates. This could be because they actually are duplicates or it could be because your Unique Identifier is not unique for each record.<br><br>If your import file has no duplicates and you want to import all %s records, you should delete everything that was just imported and then edit your Unique Identifier so it's unique for each item."
msgstr "インポートするファイルには%s個のレコードがありますが、WP All Importは<span class = \"inserted_count\"> </ span>%sのみを作成しました。 ファイル内の他のレコードが重複として検出されました。 これは、実際に重複しているか、一意の識別子がレコードごとに一意でないために発生する可能性があります。インポートファイルに重複がなく、すべてのレコード%sをインポートする場合は、一意のIDを編集して各アイテムに固有のものにします"

#: views/admin/import/process.php:54 views/admin/import/process.php:61
msgid "Delete & Edit"
msgstr "削除 & 編集"

#: views/admin/import/process.php:57
msgid "<span id=\"skipped_count\">%s</span> orders were skipped during this import"
msgstr "<span id=\"skipped_count\">%s</span>注文がこのインポート中にスキップされました"

#: views/admin/import/process.php:59
msgid "WP All Import is unable import an order when it cannot match the products or customer specified. <a href=\"%s\" style=\"margin: 0;\">See the import log</a> for a list of which orders were skipped and why."
msgstr "WP をすべてインポート製品または顧客の指定された一致できません輸入注文であります。<a href=\"%s\" style=\"margin: 0;\">インポートのログを参照してください</a>の注文がスキップされたリストを、なぜ。"

#: views/admin/import/process.php:63
msgid "WP All Import successfully imported your file <span>%s</span> into your WordPress installation!"
msgstr "WP All Importは、あなたのワードプレスのインストールにファイル<span>%s</span>を正常にインポートしました!"

#: views/admin/import/process.php:65 views/admin/import/process.php:67
msgid "There were <span class=\"wpallimport-errors-count\">%s</span> errors and <span class=\"wpallimport-warnings-count\">%s</span> warnings in this import. You can see these in the import log."
msgstr "エラー <span class=\"wpallimport-errors-count\">%s</span>と<span class=\"wpallimport-warnings-count\">%s</span>警告このインポートであった。インポート ログでこれらを見ることができます。"

#: views/admin/import/process.php:70
msgid "View Logs"
msgstr "ログの表示"

#: views/admin/import/process.php:78
msgid "Hide this notice."
msgstr "このお知らせを隠す"

#: views/admin/import/process.php:82
msgid "Want to speed up your import?"
msgstr "あなたのインポートを高速化したいですか。"

#: views/admin/import/process.php:83
msgid "Check out our guide on increasing import speed."
msgstr "インポート速度の増加に私たちのガイドをチェックしてください。"

#: views/admin/import/process.php:87
msgid "opens in new tab"
msgstr "新しいタブで開きます"

#: views/admin/import/process.php:95 views/admin/import/process.php:116
msgid "Your server terminated the import process"
msgstr "サーバーでは、インポート プロセスを終了しました。"

#: views/admin/import/process.php:96
msgid "<a href='%s' target='_blank'>Read more</a> about how to prevent this from happening again."
msgstr "再び起きてからこれを防止する方法について<a href='%s' target='_blank'>続きを読む</a>。"

#: views/admin/import/process.php:100
msgid "with <span id='wpallimport-new-records-per-iteration'>%s</span> records per iteration"
msgstr "イテレーションごとに<span id='wpallimport-new-records-per-iteration'>%s</span>のレコードを"

#: views/admin/import/process.php:106
msgid "Log"
msgstr "ログ"

#: views/admin/import/process.php:117
msgid "Ask your host to check your server's error log. They will be able to determine why your server is terminating the import process."
msgstr "あなたのホスト サーバーのエラー ログを確認してくださいにお問い合わせください。あなたのサーバーはインポート プロセスを終了する理由を判断することができるようになります。"

#: views/admin/import/tag.php:5
msgid "Elements"
msgstr "要素"

#: views/admin/import/tag.php:27
msgid "History file not found. Probably you are using wrong encoding."
msgstr "履歴ファイルが見つかりません。おそらく間違ったエンコーディングを使用しています。"

#: views/admin/import/template.php:42
msgid "Name & Description"
msgstr "名前と説明"

#: views/admin/import/template.php:44
msgid "Title & Content"
msgstr "タイトル & 内容"

#: views/admin/import/template.php:52
msgid "Drag & drop any element on the right to set the title."
msgstr "ドラッグ & タイトルを設定する右上の任意の要素を削除します。"

#: views/admin/import/template.php:71
msgid "WooCommerce Short Description"
msgstr "WooCommerceの簡単な説明"

#: views/admin/import/template.php:75
#: views/admin/import/template/_taxonomies_template.php:138
msgid "Preview"
msgstr "プリビュー"

#: views/admin/import/template.php:81
msgid "Advanced Options"
msgstr "高度な設定"

#: views/admin/import/template.php:88
msgid "Keep line breaks from file"
msgstr "ファイルから改行を維持します。"

#: views/admin/import/template.php:93
msgid "Decode HTML entities with <b>html_entity_decode</b>"
msgstr "<b>html_entity_decode</b>を使用してHTMLエンティティをデコードする"

#: views/admin/import/template.php:161 views/admin/settings/index.php:212
msgid "Function Editor"
msgstr "関数エディター"

#: views/admin/import/template.php:171 views/admin/settings/index.php:220
msgid "Save Functions"
msgstr "関数を保存"

#: views/admin/import/template.php:172 views/admin/settings/index.php:221
msgid "Add functions here for use during your import. You can access this file at %s"
msgstr "あなたのインポート時に使用するためにここに機能を追加します。あなたは%sでこのファイルにアクセスすることができます"

#: views/admin/import/template.php:197
msgid "Save settings as a template"
msgstr "設定をテンプレートとして保存します。"

#: views/admin/import/template.php:200
msgid "Template name..."
msgstr "テンプレート名..."

#: views/admin/import/template.php:205
msgid "Load Template..."
msgstr "テンプレートを読み込みます..."

#: views/admin/import/template.php:225
msgid "Back to Step 2"
msgstr "ステップ2へ戻る"

#: views/admin/import/template.php:229
msgid "Continue to Step 4"
msgstr "ステップ4に進みます"

#: views/admin/import/template/_custom_fields_template.php:27
msgid "Your website is using Custom Fields to store data for %s."
msgstr "あなたのウェブサイトは、%s のデータを格納するカスタム フィールドを使用しています。"

#: views/admin/import/template/_custom_fields_template.php:28
#: views/admin/import/template/_term_meta_template.php:28
msgid "See Detected Fields"
msgstr "検出されたフィールドを参照"

#: views/admin/import/template/_custom_fields_template.php:30
msgid "No Custom Fields are present in your database for %s."
msgstr "%s のデータベースにカスタムフィールドはありません。"

#: views/admin/import/template/_custom_fields_template.php:31
#: views/admin/import/template/_term_meta_template.php:31
msgid "Manually create a %s, and fill out each field you want to import data to. WP All Import will then display these fields as available for import below."
msgstr "手動で %s を作成し、データのインポートしたい各フィールドに記入します。WP をすべてインポートしても、これらのフィールドは以下のインポートに利用できるよう表示します。"

#: views/admin/import/template/_custom_fields_template.php:33
#: views/admin/import/template/_custom_fields_template.php:41
#: views/admin/import/template/_term_meta_template.php:33
#: views/admin/import/template/_term_meta_template.php:41
msgid "Hide Notice"
msgstr "このお知らせを隠す"

#: views/admin/import/template/_custom_fields_template.php:38
#: views/admin/import/template/_term_meta_template.php:38
msgid "Clear All Fields"
msgstr "すべてのフィールドをクリア"

#: views/admin/import/template/_custom_fields_template.php:40
#: views/admin/import/template/_term_meta_template.php:40
msgid "If not all fields were detected, manually create a %s, and fill out each field you want to import data to. Then create a new import, and WP All Import will display these fields as available for import below."
msgstr "すべてのフィールドが検出されていない場合は、手動で%sを作成し、データをインポートする各フィールドに入力します。 次に、新しいインポートを作成し、WP All Importはこれらのフィールドを下にインポートできるように表示します。"

#: views/admin/import/template/_custom_fields_template.php:67
#: views/admin/import/template/_custom_fields_template.php:276
#: views/admin/import/template/_custom_fields_template.php:410
#: views/admin/import/template/_term_meta_template.php:67
#: views/admin/import/template/_term_meta_template.php:276
#: views/admin/import/template/_term_meta_template.php:410
msgid "Click to specify"
msgstr "クリックすると指定"

#: views/admin/import/template/_custom_fields_template.php:72
#: views/admin/import/template/_custom_fields_template.php:281
#: views/admin/import/template/_custom_fields_template.php:415
#: views/admin/import/template/_term_meta_template.php:72
#: views/admin/import/template/_term_meta_template.php:281
#: views/admin/import/template/_term_meta_template.php:415
msgid "Serialized"
msgstr "シリアル化された"

#: views/admin/import/template/_custom_fields_template.php:85
#: views/admin/import/template/_custom_fields_template.php:294
#: views/admin/import/template/_custom_fields_template.php:428
#: views/admin/import/template/_term_meta_template.php:85
#: views/admin/import/template/_term_meta_template.php:294
#: views/admin/import/template/_term_meta_template.php:428
msgid "Key"
msgstr "キー"

#: views/admin/import/template/_custom_fields_template.php:156
#: views/admin/import/template/_custom_fields_template.php:536
msgid "Add Custom Field"
msgstr "カスタムフィールド追加"

#: views/admin/import/template/_custom_fields_template.php:162
#: views/admin/import/template/_custom_fields_template.php:330
#: views/admin/import/template/_custom_fields_template.php:464
#: views/admin/import/template/_term_meta_template.php:162
#: views/admin/import/template/_term_meta_template.php:330
#: views/admin/import/template/_term_meta_template.php:464
msgid "Auto-Detect"
msgstr "自動検知"

#: views/admin/import/template/_custom_fields_template.php:167
#: views/admin/import/template/_custom_fields_template.php:335
#: views/admin/import/template/_custom_fields_template.php:469
#: views/admin/import/template/_term_meta_template.php:167
#: views/admin/import/template/_term_meta_template.php:335
#: views/admin/import/template/_term_meta_template.php:469
#: views/admin/license/index.php:40
msgid "Save"
msgstr "保存"

#: views/admin/import/template/_featured_template.php:8
msgid "Show hints"
msgstr "ヒントの表示"

#: views/admin/import/template/_featured_template.php:15
msgid "Download images hosted elsewhere"
msgstr "他の場所でホストされている画像をダウンロードします。"

#: views/admin/import/template/_featured_template.php:19
msgid "Enter image URL one per line, or separate them with a "
msgstr "画像URLを1行に1つずつ入力するか、または"

#: views/admin/import/template/_featured_template.php:25
msgid "Use images currently in Media Library"
msgstr "画像は現在のメディアライブラリで使用してください。"

#: views/admin/import/template/_featured_template.php:29
#: views/admin/import/template/_featured_template.php:39
msgid "Enter image filenames one per line, or separate them with a "
msgstr "画像ファイル名を1行に1つずつ入力するか、または"

#: views/admin/import/template/_featured_template.php:36
msgid "Use images currently uploaded in %s"
msgstr "現在 %s にアップロードされたイメージを使用します。"

#: views/admin/import/template/_featured_template.php:44
msgid "Image Options"
msgstr "画像オプション"

#: views/admin/import/template/_featured_template.php:49
msgid "Search through the Media Library for existing images before importing new images"
msgstr "新しい画像をインポートする前に、既存の画像をメディアライブラリから検索する"

#: views/admin/import/template/_featured_template.php:50
msgid "If an image with the same file name is found in the Media Library then that image will be attached to this record instead of importing a new image. Disable this setting if your import has different images with the same file name."
msgstr "同じファイル名の画像はメディア ライブラリで見つかった、新しいイメージをインポートする代わりにこのレコードにそのイメージがアタッチされます。インポートは同じファイル名を持つ画像を持っている場合は、この設定を無効にします。"

#: views/admin/import/template/_featured_template.php:55
msgid "Keep images currently in Media Library"
msgstr "画像は現在のメディアライブラリで使用してください。"

#: views/admin/import/template/_featured_template.php:56
msgid "If disabled, images attached to imported posts will be deleted and then all images will be imported."
msgstr "無効にすると、インポートされた投稿に添付した画像が削除され、すべての画像を取り込みます。"

#: views/admin/import/template/_featured_template.php:62
msgid "Preview & Test"
msgstr "プレビュー＆テスト"

#: views/admin/import/template/_featured_template.php:67
msgid "Set the first image to the Featured Image (_thumbnail_id)"
msgstr "最初の画像をおすすめ画像に設定する (_thumbnail_id)"

#: views/admin/import/template/_featured_template.php:72
msgid "If no images are downloaded successfully, create entry as Draft."
msgstr "画像を正常にダウンロードしてない場合は、下書きとしてエントリを作成します。"

#: views/admin/import/template/_featured_template.php:83
msgid "SEO & Advanced Options"
msgstr "SEO & 高度なオプション"

#: views/admin/import/template/_featured_template.php:91
msgid "Meta Data"
msgstr "メタデータ"

#: views/admin/import/template/_featured_template.php:95
msgid "Set Title(s)"
msgstr "タイトル設定"

#: views/admin/import/template/_featured_template.php:97
#: views/admin/import/template/_featured_template.php:108
#: views/admin/import/template/_featured_template.php:119
msgid "Enter one per line, or separate them with a "
msgstr "画像URLを1行に1つずつ入力するか、または"

#: views/admin/import/template/_featured_template.php:99
msgid "The first title will be linked to the first image, the second title will be linked to the second image, ..."
msgstr "最初のタイトルは、最初の画像にリンクされます、2 番目のタイトルは、2 番目の画像にリンクされます."

#: views/admin/import/template/_featured_template.php:106
msgid "Set Caption(s)"
msgstr "設定キャプション（S）"

#: views/admin/import/template/_featured_template.php:110
msgid "The first caption will be linked to the first image, the second caption will be linked to the second image, ..."
msgstr "最初のキャプションは、最初の画像にリンクされます、2 番目のキャプションは、2 番目の画像にリンクされます."

#: views/admin/import/template/_featured_template.php:117
msgid "Set Alt Text(s)"
msgstr "代替テキスト設定"

#: views/admin/import/template/_featured_template.php:121
msgid "The first alt text will be linked to the first image, the second alt text will be linked to the second image, ..."
msgstr "最初の alt テキストは、最初の画像にリンクされます、2 番目の alt テキストは、2 番目の画像にリンクされます."

#: views/admin/import/template/_featured_template.php:128
msgid "Set Description(s)"
msgstr "詳細を設定"

#: views/admin/import/template/_featured_template.php:132
msgid "Separate them with a"
msgstr "それらを"

#: views/admin/import/template/_featured_template.php:137
msgid "Enter them one per line"
msgstr "1 行につき 1 つ入力します。"

#: views/admin/import/template/_featured_template.php:139
msgid "The first description will be linked to the first image, the second description will be linked to the second image, ..."
msgstr "最初の説明は、最初の画像にリンクされます、2 番目の説明は、2 番目の画像にリンクされます."

#: views/admin/import/template/_featured_template.php:143
#: views/admin/settings/index.php:79
msgid "Files"
msgstr "ファイル"

#: views/admin/import/template/_featured_template.php:145
msgid "These options only available if Download images hosted elsewhere is selected above."
msgstr "ダウンロード画像は、他の場所でホストされている場合のみ利用できるこれらのオプションは上記選択されます。"

#: views/admin/import/template/_featured_template.php:149
msgid "Change image file names to"
msgstr "画像ファイル名を次のように変更する"

#: views/admin/import/template/_featured_template.php:152
msgid "Multiple image will have numbers appended, i.e. image-name-1.jpg, image-name-2.jpg "
msgstr "複数の画像に数字が追加されます。例. image-name-1.jpg, image-name-2.jpg "

#: views/admin/import/template/_featured_template.php:158
msgid "Change image file extensions"
msgstr "画像ファイルの拡張子を変更"

#: views/admin/import/template/_featured_template.php:176
msgid "WP All Import will automatically ignore elements with blank image URLs/filenames."
msgstr "WP All Import は、空のイメージURL /ファイル名を持つ要素を自動的に無視します。"

#: views/admin/import/template/_featured_template.php:177
msgid "WP All Import must download the images to your server. You can't have images in a Gallery that are referenced by external URL. That's just how WordPress works."
msgstr "WP All Import はあなたのサーバーに画像をダウンロードする必要があります。あなたは、外部のURLで参照されているギャラリーにイメージを持つことはできません。それはWordpressの動作方法です。"

#: views/admin/import/template/_featured_template.php:178
msgid "Importing a variable number of images can be done using a <a href=\"%s\" target=\"_blank\">FOREACH LOOP</a>"
msgstr "可変数の画像をインポートするには、<a href=\"%s\" target=\"_blank\"> FOREACHループ</a>を使用します"

#: views/admin/import/template/_featured_template.php:179
msgid "For more information check out our <a href=\"%s\" target=\"_blank\">comprehensive documentation</a>"
msgstr "詳細については、<a href=\"%s\" target=\"_blank\">包括的なドキュメント</a>をご覧ください"

#: views/admin/import/template/_nested_template.php:35
msgid "Nested XML/CSV files"
msgstr "ネストされたXML / CSVファイル"

#: views/admin/import/template/_nested_template.php:48
msgid "remove"
msgstr "削除"

#: views/admin/import/template/_nested_template.php:69
msgid "Specify the URL of the nested file to use."
msgstr "使用するネストされたファイルのURLを指定します。"

#: views/admin/import/template/_nested_template.php:73
msgid "Add"
msgstr "追加"

#: views/admin/import/template/_other_template.php:16
#: views/admin/import/template/_term_other_template.php:13
msgid "Other %s Options"
msgstr "その他 %s のオプション"

#: views/admin/import/template/_other_template.php:28
msgid "Post Status"
msgstr "投稿ステータス"

#: views/admin/import/template/_other_template.php:31
msgid "Published"
msgstr "公開済み"

#: views/admin/import/template/_other_template.php:35
msgid "Draft"
msgstr "下書き"

#: views/admin/import/template/_other_template.php:43
msgid "The value of presented XPath should be one of the following: ('publish', 'draft', 'trash')."
msgstr "提示された XPath の値は、次のいずれかをする必要があります: ('公開'、'下書き'、'ゴミ箱')。"

#: views/admin/import/template/_other_template.php:52
msgid "Post Dates"
msgstr "投稿日付"

#: views/admin/import/template/_other_template.php:52
msgid "Use any format supported by the PHP <b>strtotime</b> function. That means pretty much any human-readable date will work."
msgstr "PHP<b>strtotime</ b>の関数でサポートされている任意のフォーマットを使用してください。それはほとんどすべての人間が読める日が動作することを意味します。"

#: views/admin/import/template/_other_template.php:56
msgid "As specified"
msgstr "指定通り"

#: views/admin/import/template/_other_template.php:65
msgid "Random dates"
msgstr "ランダム日付"

#: views/admin/import/template/_other_template.php:65
msgid "Posts will be randomly assigned dates in this range. WordPress ensures posts with dates in the future will not appear until their date has been reached."
msgstr "投稿はこの範囲内でランダムに割り当てられます。 WordPressは、将来の日付の投稿が日付に達するまで表示されないようにします。"

#: views/admin/import/template/_other_template.php:69
#: views/admin/manage/delete.php:56
msgid "and"
msgstr "と"

#: views/admin/import/template/_other_template.php:77
msgid "Comments"
msgstr "コメント"

#: views/admin/import/template/_other_template.php:80
#: views/admin/import/template/_other_template.php:103
msgid "Open"
msgstr "開く"

#: views/admin/import/template/_other_template.php:84
#: views/admin/import/template/_other_template.php:107
msgid "Closed"
msgstr "閉じる"

#: views/admin/import/template/_other_template.php:92
#: views/admin/import/template/_other_template.php:115
msgid "The value of presented XPath should be one of the following: ('open', 'closed')."
msgstr "表示されるXPathの値は、次のいずれかである必要があります: ('open', 'closed')."

#: views/admin/import/template/_other_template.php:100
msgid "Trackbacks and Pingbacks"
msgstr "トラックバックとピンバック"

#: views/admin/import/template/_other_template.php:123
msgid "Post Slug"
msgstr "投稿スラッグ"

#: views/admin/import/template/_other_template.php:131
msgid "Post Author"
msgstr "投稿者"

#: views/admin/import/template/_other_template.php:133
msgid "Assign the post to an existing user account by specifying the user ID, username, or e-mail address."
msgstr "ユーザーID、ユーザー名、またはメールアドレスを指定して既存のユーザーアカウントに投稿を割り当てます。"

#: views/admin/import/template/_other_template.php:139
msgid "Download & Import Attachments"
msgstr "ダウンロード＆インポートファイル"

#: views/admin/import/template/_other_template.php:140
#: views/admin/import/template/_taxonomies_template.php:65
#: views/admin/import/template/_taxonomies_template.php:122
#: views/admin/import/template/_taxonomies_template.php:134
#: views/admin/import/template/_taxonomies_template.php:212
msgid "Separated by"
msgstr "区切る"

#: views/admin/import/template/_other_template.php:148
msgid "Search for existing attachments to prevent duplicates in media library"
msgstr "メディア ライブラリ内の重複を防ぐために既存の添付ファイルの検索"

#: views/admin/import/template/_other_template.php:155
msgid "Post Format"
msgstr "投稿フォーマット"

#: views/admin/import/template/_other_template.php:161
msgid "Standard"
msgstr "標準"

#: views/admin/import/template/_other_template.php:193
msgid "Page Template"
msgstr "ページ テンプレート"

#: views/admin/import/template/_other_template.php:196
msgid "Select a template"
msgstr "テンプレートを選択"

#: views/admin/import/template/_other_template.php:200
msgid "Default"
msgstr "デフォルト"

#: views/admin/import/template/_other_template.php:222
msgid "Page Parent"
msgstr "親ページ"

#: views/admin/import/template/_other_template.php:222
msgid "Enter the ID, title, or slug of the desired page parent. If adding the child and parent pages in the same import, set 'Records per Iteration' to 1, run the import twice, or run separate imports for child and parent pages."
msgstr "目的のページの親のスラッグを入力します。 同じインポートで子ページと親ページを追加する場合は、「1レコード/ 1反復」を1に設定し、インポートを2回実行するか、子ページと親ページのインポートを別々に実行します。"

#: views/admin/import/template/_other_template.php:226
msgid "Select page parent"
msgstr "親ページを選択します"

#: views/admin/import/template/_other_template.php:229
msgid "(no parent)"
msgstr "﻿(親なし)"

#: views/admin/import/template/_other_template.php:248
msgid "Post Parent"
msgstr "親投稿"

#: views/admin/import/template/_other_template.php:248
msgid "Enter the ID, title, or slug of the desired post parent. If adding the child and parent posts in the same import, set 'Records per Iteration' to 1, run the import twice, or run separate imports for child and parent posts."
msgstr "目的のページの親のスラッグを入力します。 同じインポートで子ページと親ページを追加する場合は、「1レコード/ 1反復」を1に設定し、インポートを2回実行するか、子ページと親ページのインポートを別々に実行します。"

#: views/admin/import/template/_other_template.php:252
msgid "Set post parent"
msgstr "親投稿の設定"

#: views/admin/import/template/_other_template.php:276
msgid "Menu Order"
msgstr "メニュー順"

#: views/admin/import/template/_other_template.php:285
msgid "Dynamic Post Type"
msgstr "ダイナミック投稿タイプ"

#: views/admin/import/template/_other_template.php:295
msgid "If records in this import have different post types specify the slug of the desired post type here.\n"
""
msgstr "このインポートのレコードは、異なるポストタイプを持っている場合は、ここで希望のポストタイプのスラッグを指定します。\n"
""

#: views/admin/import/template/_taxonomies_template.php:13
msgid "Taxonomies, Categories, Tags"
msgstr "タクソノミー、カテゴリ、タグ"

#: views/admin/import/template/_taxonomies_template.php:17
msgid "Show Hints"
msgstr "ヒントの表示"

#: views/admin/import/template/_taxonomies_template.php:38
msgid "Each %s has just one %s"
msgstr "それぞれの%sには1つの%sしかありません"

#: views/admin/import/template/_taxonomies_template.php:46
#: views/admin/import/template/_taxonomies_template.php:70
msgid "Try to match terms to existing child %s"
msgstr "既存の子 %s に条件を合わせてみてください。"

#: views/admin/import/template/_taxonomies_template.php:51
#: views/admin/import/template/_taxonomies_template.php:75
msgid "Only assign %s to the imported %s, not the entire hierarchy"
msgstr "階層全体ではなく、インポートされた%sにのみ%sを割り当てます"

#: views/admin/import/template/_taxonomies_template.php:52
#: views/admin/import/template/_taxonomies_template.php:76
msgid "By default all categories above the matched category will also be assigned to the post. If enabled, only the imported category will be assigned to the post."
msgstr "既定では、投稿に一致するカテゴリの上のすべてのカテゴリが割り当てされます。有効な場合は、投稿にインポートされたカテゴリのみが割り当てられます。"

#: views/admin/import/template/_taxonomies_template.php:60
msgid "Each %s has multiple %s"
msgstr "各 %s は複数の %s"

#: views/admin/import/template/_taxonomies_template.php:85
msgid "%ss have hierarchical (parent/child) %s (i.e. Sports > Golf > Clubs > Putters)"
msgstr "%ss がある階層 (親/子) %s (例 スポーツ > ゴルフ > クラブ > パター)"

#: views/admin/import/template/_taxonomies_template.php:90
msgid "An element in my file contains the entire hierarchy (i.e. you have an element with a value = Sports > Golf > Clubs > Putters)"
msgstr "私のファイルの要素は、階層全体が含まれています（例 あなたが値を持つ要素を持っている=スポーツ>ゴルフ>クラブ>パター）"

#: views/admin/import/template/_taxonomies_template.php:127
msgid "Only assign %s to the bottom level term in the hierarchy"
msgstr "階層の下のレベル用語に %s のみを割り当てる"

#: views/admin/import/template/_taxonomies_template.php:132
msgid "Separate hierarchy groups via symbol"
msgstr "シンボルを介して別の階層グループ"

#: views/admin/import/template/_taxonomies_template.php:140
msgid "Add Another Hierarchy Group"
msgstr "別の階層グループを追加"

#: views/admin/import/template/_taxonomies_template.php:147
msgid "Manually design the hierarchy with drag & drop"
msgstr "手動でドラッグ & ドロップと階層を設計"

#: views/admin/import/template/_taxonomies_template.php:149
msgid "Drag the <img src=\"%s\" class=\"wpallimport-drag-icon\"/> to the right to create a child, drag up and down to re-order."
msgstr "<img src=\"%s\" class=\"wpallimport-drag-icon\"/>を右にドラッグして子を作成し、上下にドラッグして並べ替えます。"

#: views/admin/import/template/_taxonomies_template.php:215
msgid "Add Another Row"
msgstr "情報を追加"

#: views/admin/import/template/_taxonomies_template.php:227
msgid "Enable Mapping for %s"
msgstr "%sのマッピングを有効にします"

#: views/admin/import/template/_taxonomies_template.php:296
msgid "Add Another Rule"
msgstr "他のルールを追加"

#: views/admin/import/template/_taxonomies_template.php:306
msgid "Apply mapping rules before splitting via separator symbol"
msgstr "区切り記号で分割する前に、マッピング ルールを適用します。"

#: views/admin/import/template/_taxonomies_template.php:321
msgid "Show \"private\" taxonomies"
msgstr "「プライベート」タクソノミーを表示"

#: views/admin/import/template/_taxonomies_template.php:332
msgid "Taxonomies that don't already exist on your site will be created."
msgstr "あなたのサイトに存在していないタクソノミーが作成されます。"

#: views/admin/import/template/_taxonomies_template.php:333
msgid "To import to existing parent taxonomies, use the existing taxonomy name or slug."
msgstr "既存の親分類にインポートするには、またはスラグの既存のタクソノミーの名前を使用します。"

#: views/admin/import/template/_taxonomies_template.php:334
msgid "To import to existing hierarchical taxonomies, create the entire hierarchy using the taxonomy names or slugs."
msgstr "既存の階層的な分類にインポートするには、タクソノミー名やスラッグを使用して階層全体を作成します。"

#: views/admin/import/template/_term_meta_template.php:27
msgid "Your website is using Term Meta to store data for %s."
msgstr "あなたのウェブサイトは、%s のデータを格納するメタ用語を使用しています。"

#: views/admin/import/template/_term_meta_template.php:30
msgid "No Term Meta are present in your database for %s."
msgstr "用語メタが %s のデータベースに存在しません。"

#: views/admin/import/template/_term_meta_template.php:156
#: views/admin/import/template/_term_meta_template.php:536
msgid "Add Term Meta"
msgstr "タームメタ追加"

#: views/admin/import/template/_term_other_template.php:21
msgid "Parent Term"
msgstr "親ターム"

#: views/admin/import/template/_term_other_template.php:33
msgid "%s Slug"
msgstr "%s のスラッグ"

#: views/admin/import/template/_term_other_template.php:36
msgid "Set slug automatically"
msgstr "スラッグを自動的に設定します。"

#: views/admin/import/template/_term_other_template.php:40
msgid "Set slug manually"
msgstr "スラグを手動で設定します。"

#: views/admin/import/template/_term_other_template.php:43
msgid "Term Slug"
msgstr "タームスラッグ"

#: views/admin/import/template/_term_other_template.php:44
msgid "The term slug must be unique. If the slug is already in use by another term, WP All Import will add a number to the end of the slug."
msgstr "タームスラグは一意である必要があります。スラッグが既に別の用語が使用されている場合、 WP All Importはスラッグの末尾に番号を追加します。"

#: views/admin/license/index.php:3
msgid "WP All Import Licenses"
msgstr "WP All Import ライセンス"

#: views/admin/license/index.php:23 views/admin/settings/index.php:163
msgid "Active"
msgstr "有効"

#: views/admin/license/index.php:24
msgid "Deactivate License"
msgstr "ライセンスを無効化"

#: views/admin/license/index.php:26 views/admin/settings/index.php:165
msgid "Activate License"
msgstr "ライセンスをアクティブ化"

#: views/admin/manage/bulk.php:1
msgid "Bulk Delete Imports"
msgstr "一括削除インポート"

#: views/admin/manage/bulk.php:10
msgid "Are you sure you want to delete <strong>%s</strong> selected %s?"
msgstr "あなたは<strong>%s</strong>選択の%sを削除してもよろしいですか？"

#: views/admin/manage/bulk.php:12
msgid "Delete associated posts as well"
msgstr "同様に、関連する記事を削除"

#: views/admin/manage/bulk.php:17 views/admin/manage/delete.php:19
msgid "Delete associated images from media gallery"
msgstr "メディア ギャラリーから関連付けられている画像を削除します。"

#: views/admin/manage/bulk.php:22 views/admin/manage/delete.php:24
msgid "Delete associated files from media gallery"
msgstr "メディア ギャラリーから関連するファイルを削除します。"

#: views/admin/manage/bulk.php:31 views/admin/manage/delete.php:32
msgid "<p class=\"wpallimport-delete-posts-warning\"><strong>Important</strong>: this import was created automatically by WP All Export. All posts exported by the \"%s\" export job have been automatically associated with this import.</p>"
msgstr "<p class=\"wpallimport-delete-posts-warning\"><strong>重要</strong>: このインポートは WP をすべてエクスポートによって自動的に作成されました。\"%s\"のエクスポート ジョブによってエクスポートされるすべての記事は、自動的にこのインポートに関連付けられています。</p>"

#: views/admin/manage/delete.php:1
msgid "Delete Import"
msgstr "インポートの削除"

#: views/admin/manage/delete.php:8
msgid "Delete import"
msgstr "インポートを削除"

#: views/admin/manage/delete.php:13
msgid "Delete %s created by %s"
msgstr "%s によって作成された %s を削除します。"

#: views/admin/manage/delete.php:56
msgid "Are you sure you want to delete "
msgstr "あなたはこのスキャンを削除してもよろしいですか？"

#: views/admin/manage/delete.php:56
msgid "the <strong>%s</strong> import"
msgstr "<strong>%s</strong>インポート"

#: views/admin/manage/index.php:18 views/admin/manage/index.php:20
msgid "Search Imports"
msgstr "インポートの検索"

#: views/admin/manage/index.php:28
msgid "File"
msgstr "ファイル"

#: views/admin/manage/index.php:31
msgid "Info & Options"
msgstr "情報＆オプション"

#: views/admin/manage/index.php:100
msgid "No previous imports found. <a href=\"%s\">Start a new import...</a>"
msgstr "前回のインポートは見つかりませんでした。<a href=\"%s\">新しいインポートを開始していません...</a>"

#: views/admin/manage/index.php:182
msgid "Edit Import"
msgstr "インポート編集"

#: views/admin/manage/index.php:187
msgid "Import Settings"
msgstr "インポート設定"

#: views/admin/manage/index.php:228
msgid "triggered with cron"
msgstr "cronでトリガ"

#: views/admin/manage/index.php:235 views/admin/manage/index.php:250
#: views/admin/manage/index.php:264
msgid "last activity %s ago"
msgstr "%s以前の最後のアクティビティ"

#: views/admin/manage/index.php:242
msgid "currently processing with cron"
msgstr "現在のcronで処理します"

#: views/admin/manage/index.php:257
msgid "Import currently in progress"
msgstr "現在進行中のインポート"

#: views/admin/manage/index.php:271 views/admin/manage/index.php:275
msgid "Import Attempt at %s"
msgstr "%sのインポートテスト"

#: views/admin/manage/index.php:276
msgid "Import failed, please check logs"
msgstr "インポートに失敗しました、ログを確認してください。"

#: views/admin/manage/index.php:295
msgid "Last run: %s"
msgstr "前回の実行：%s"

#: views/admin/manage/index.php:295
msgid "never"
msgstr "永遠"

#: views/admin/manage/index.php:296
msgid "%d %s created"
msgstr "%d 件の %s を作成しました"

#: views/admin/manage/index.php:297
msgid "%d updated, %d skipped, %d deleted"
msgstr "%d 件は更新され、%d 件はスキップされ、%d 件は削除されました"

#: views/admin/manage/index.php:304
msgid "settings edited since last run"
msgstr "前回の実行以降編集の設定"

#: views/admin/manage/index.php:316 views/admin/manage/scheduling.php:2
msgid "Cron Scheduling"
msgstr "Cron のスケジューリング"

#: views/admin/manage/index.php:318
msgid "History Logs"
msgstr "履歴ログ"

#: views/admin/manage/index.php:328
msgid "Run Import"
msgstr "インポート実行"

#: views/admin/manage/index.php:330
msgid "Cancel Cron"
msgstr "複製をキャンセル"

#: views/admin/manage/index.php:332
msgid "Cancel"
msgstr "キャンセル"

#: views/admin/manage/scheduling.php:8
msgid "To schedule an import, you must create two cron jobs in your web hosting control panel. One cron job will be used to run the Trigger script, the other to run the Execution script."
msgstr "インポートをスケジュールするには、あなたのウェブホスティングコントロールパネル内の2つのcronジョブを作成する必要があります。一つのcronジョブが実行スクリプトを実行するために、他の、トリガースクリプトを実行するために使用されます。"

#: views/admin/manage/scheduling.php:19
msgid "Trigger Script"
msgstr "トリガー・スクリプト"

#: views/admin/manage/scheduling.php:21
msgid "Every time you want to schedule the import, run the trigger script."
msgstr "インポートをスケジュールするたびにトリガー スクリプトを実行します。"

#: views/admin/manage/scheduling.php:23
msgid "To schedule the import to run once every 24 hours, run the trigger script every 24 hours. Most hosts require you to use “wget” to access a URL. Ask your host for details."
msgstr "24時間ごとに実行するためのインポートをスケジュールするには、24時間ごとにトリガー・スクリプトを実行します。ほとんどのホストは、URLにアクセスするには、「wgetの \"を使用する必要があります。詳細については、お使いのホストを確認して下さい。"

#: views/admin/manage/scheduling.php:25 views/admin/manage/scheduling.php:37
msgid "Example:"
msgstr "例:"

#: views/admin/manage/scheduling.php:29
msgid "Execution Script"
msgstr "実行スクリプト"

#: views/admin/manage/scheduling.php:31
msgid "The Execution script actually executes the import, once it has been triggered with the Trigger script."
msgstr "それがトリガー スクリプトを起動実行スクリプトは実際にインポートを実行します。"

#: views/admin/manage/scheduling.php:33
msgid "It processes in iteration (only importing a few records each time it runs) to optimize server load. It is recommended you run the execution script every 2 minutes."
msgstr "それプロセスの反復 (だけでいくつかのレコードをインポートする実行時間) サーバーの負荷を最適化するために。2 分ごとの実行スクリプトを実行することをお勧めします。"

#: views/admin/manage/scheduling.php:35
msgid "It also operates this way in case of unexpected crashes by your web host. If it crashes before the import is finished, the next run of the cron job two minutes later will continue it where it left off, ensuring reliability."
msgstr "また、あなたのWebホストによって予期せぬクラッシュの場合にはこのように動作します。インポートが完了する前に、それがクラッシュした場合、cronジョブの次の実行は、2分後には信頼性を確保し、オフに左にそれを継続します。"

#: views/admin/manage/scheduling.php:41
msgid "Notes"
msgstr "注記"

#: views/admin/manage/scheduling.php:44
msgid "Your web host may require you to use a command other than wget, although wget is most common. In this case, you must asking your web hosting provider for help."
msgstr "あなたの web ホストは、wget は最も一般的な wget コマンド以外のコマンドを使用する必要があります。このケースでは、助けをあなたの web ホスティングプロバイダーを求めてする必要があります。"

#: views/admin/manage/scheduling.php:54
msgid "To schedule this import with a cron job, you must use the \"Download from URL\" option on the Import Settings screen of WP All Import."
msgstr "この cron ジョブのインポートをスケジュールするには、WP をすべてインポートのインポート設定画面で「URL からダウンロード」オプションを使用する必要が。"

#: views/admin/manage/scheduling.php:57
msgid "Go to Import Settings now..."
msgstr "今すぐインポート設定に移動..."

#: views/admin/manage/update.php:1
msgid "Update Import"
msgstr "アップデートのインポート"

#: views/admin/manage/update.php:9
msgid "Are you sure you want to update <strong>%s</strong> import?"
msgstr "<strong>%s</strong>のインポートを更新してもよろしいですか？"

#: views/admin/manage/update.php:10
msgid "Source path is <strong>%s</strong>"
msgstr "ソースパスは<strong>%s</strong>です"

#: views/admin/manage/update.php:21
msgid "Update feature is not available for this import since it has no external path linked."
msgstr "リンクされている外部のパスがあるないので、更新機能はこのインポートに使用できません。"

#: views/admin/settings/index.php:18
msgid "Import/Export Templates"
msgstr "インポート/エクスポートのテンプレート"

#: views/admin/settings/index.php:32
msgid "Delete Selected"
msgstr "選択したものを削除"

#: views/admin/settings/index.php:33
msgid "Export Selected"
msgstr "エクスポート選択"

#: views/admin/settings/index.php:36
msgid "There are no templates saved"
msgstr "テンプレートは保存されてません"

#: views/admin/settings/index.php:41
msgid "Import Templates"
msgstr "テンプレートのインポート"

#: views/admin/settings/index.php:49
msgid "Cron Imports"
msgstr "Cron インポート"

#: views/admin/settings/index.php:54
msgid "Secret Key"
msgstr "シークレットキー"

#: views/admin/settings/index.php:57
msgid "Changing this will require you to re-create your existing cron jobs."
msgstr "この変更は既存の cron ジョブを再作成する上で必要になります。"

#: views/admin/settings/index.php:61
msgid "Cron Processing Time Limit"
msgstr "cronの処理時間の制限"

#: views/admin/settings/index.php:64
msgid "Leave blank to use your server's limit on script run times."
msgstr "スクリプトの実行時間にサーバーの制限を使用する場合は、空白のままにします。"

#: views/admin/settings/index.php:68
msgid "Cron Sleep"
msgstr "cronのスリープ"

#: views/admin/settings/index.php:71
msgid "Sleep the specified number of seconds between each post created, updated, or deleted with cron. Leave blank to not sleep. Only necessary on servers  that are slowed down by the cron job because they have very minimal processing power and resources."
msgstr "作成、更新、または cron を削除各ポスト間の秒指定された数をスリープ状態します。眠ることを空白のまま。彼らは非常に最小限の処理能力とリソースを持っているので、cron ジョブによって減速されますサーバーでのみ必要です。"

#: views/admin/settings/index.php:84 views/admin/settings/index.php:87
msgid "Secure Mode"
msgstr "セキュアモード"

#: views/admin/settings/index.php:89
msgid "Randomize folder names"
msgstr "フォルダ名をランダム"

#: views/admin/settings/index.php:95
msgid "Imported files, chunks, logs and temporary files will be placed in a folder with a randomized name inside of %s."
msgstr "インポートされたファイル、チャンク、ログ、および一時ファイルは、%sの中にランダムに名前が付けられたフォルダに置かれます。"

#: views/admin/settings/index.php:100
msgid "Log Storage"
msgstr "ログストレージ"

#: views/admin/settings/index.php:103
msgid "Number of logs to store for each import. Enter 0 to never store logs."
msgstr "各インポートに保存するログの数。決してログを保存する場合は 0 を入力します。"

#: views/admin/settings/index.php:107
msgid "Clean Up Temp Files"
msgstr "一時ファイルのクリーンアップ"

#: views/admin/settings/index.php:109
msgid "Clean Up"
msgstr "クリーンアップ"

#: views/admin/settings/index.php:110
msgid "Attempt to remove temp files left over by imports that were improperly terminated."
msgstr "適切にターミネートされた輸入によって一時ファイルを削除しようとします。"

#: views/admin/settings/index.php:118
msgid "Advanced Settings"
msgstr "高度な設定"

#: views/admin/settings/index.php:123
msgid "Chunk Size"
msgstr "チャンクサイズ"

#: views/admin/settings/index.php:126
msgid "Split file into chunks containing the specified number of records."
msgstr "指定された数のレコードを含むチャンクにファイルを分割します。"

#: views/admin/settings/index.php:130
msgid "WP_IMPORTING"
msgstr "WP_IMPORTING"

#: views/admin/settings/index.php:134
msgid "Enable WP_IMPORTING"
msgstr "WP_IMPORTINGを有効化"

#: views/admin/settings/index.php:136
msgid "Setting this constant avoids triggering pingback."
msgstr "ピングバックのトリガーを回避この定数を設定します。"

#: views/admin/settings/index.php:140
msgid "Add Port To URL"
msgstr "URLにポートを追加"

#: views/admin/settings/index.php:143
msgid "Specify the port number to add if you're having problems continuing to Step 2 and are running things on a custom port. Default is blank."
msgstr "カスタムポートでの運用で、ステップ2へ進む際に問題が発生する場合、追加するポート番号を指定してください。デフォルトは空白です。"

#: views/admin/settings/index.php:150
msgid "Licenses"
msgstr "ライセンス"

#: views/admin/settings/index.php:157
msgid "License Key"
msgstr "ライセンスキー"

#: views/admin/settings/index.php:170
msgid "A license key is required to access plugin updates. You can use your license key on an unlimited number of websites. Do not distribute your license key to 3rd parties. You can get your license key in the <a target=\"_blank\" href=\"http://www.wpallimport.com/portal\">customer portal</a>."
msgstr "プラグインのアップデートにアクセスするには、ライセンスキーが必要です。無制限にWebサイトでライセンスキーを使用することができます。ライセンスキーを第三者に配布しないでください。ライセンスキーは<a target=\"_blank\" href=\"http://www.wpallimport.com/portal\">カスタマーポータル</a>で入手できます。"

#: views/admin/settings/index.php:177
msgid "Force Stream Reader"
msgstr "力ストリーム リーダー"

#: views/admin/settings/index.php:182
msgid "Force WP All Import to use StreamReader instead of XMLReader to parse all import files"
msgstr "すべての解析に XMLReader の代わりに StreamReader を使用する WP 力すべてインポート ファイルをインポートします。"

#: views/admin/settings/index.php:186
msgid "Enable Stream Reader"
msgstr "有効にするストリーム リーダー"

#: wp-all-import-pro.php:20
msgid "Please de-activate and remove the free version of WP All Import before activating the paid version."
msgstr "有料版を有効にする前に、WP All Importの無料版を無効にして削除してください。"

#: wp-all-import-pro.php:332
msgid "To enable updates, please enter your license key on the <a href=\"%s\">Licenses</a> page. If you don't have a licence key, please see <a href=\"%s\">details & pricing</a>"
msgstr "更新を有効にするには、ページで<a href=\"%s\">ライセンス</a>上のライセンスキーを入力してください。ライセンスキーをお持ちでない場合は、<a href=\"%s\">詳細と価格</a>をご覧ください。"

#: wp-all-import-pro.php:819 wp-all-import-pro.php:823
msgid "Uploads folder %s must be writable"
msgstr "アップロードフォルダ %s は書き込み可能でなければなりません"

#: wp-all-import-pro.php:960
msgid "<b>%s Plugin</b>: Current sql user %s doesn't have ALTER privileges"
msgstr "<b>%sのプラグイン</b> ：現在のSQLユーザー%s はALTER権限を持っていません。"

#. Plugin Name of the plugin/theme
#: 
msgid "WP All Import Pro"
msgstr "WP All Import Pro"

#. Plugin URI of the plugin/theme
#: 
msgid "http://www.wpallimport.com/"
msgstr "http://www.wpallimport.com/"

#. Description of the plugin/theme
#: 
msgid "The most powerful solution for importing XML and CSV files to WordPress. Import to Posts, Pages, and Custom Post Types. Support for imports that run on a schedule, ability to update existing imports, and much more."
msgstr "ワードプレスに XML および CSV ファイルをインポートするための最も強力なソリューションです。インポートの投稿、ページ、およびカスタム ポスト タイプ。スケジュールで実行する既存の更新機能をインポートすると、インポートおよび大いに多くをサポートします。"

#. Author of the plugin/theme
#: 
msgid "Soflyy"
msgstr "Soflyy"

