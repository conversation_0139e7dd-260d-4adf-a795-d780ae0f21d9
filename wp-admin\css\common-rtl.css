/*! This file is auto-generated */
/* 2 column liquid layout */
#wpwrap {
	height: auto;
	min-height: 100%;
	width: 100%;
	position: relative;
	-webkit-font-smoothing: subpixel-antialiased;
}

#wpcontent {
	height: 100%;
	padding-right: 20px;
}

#wpcontent,
#wpfooter {
	margin-right: 160px;
}

.folded #wpcontent,
.folded #wpfooter {
	margin-right: 36px;
}

#wpbody-content {
	padding-bottom: 65px;
	float: right;
	width: 100%;
	overflow: visible;
}

/* inner 2 column liquid layout */

.inner-sidebar {
	float: left;
	clear: left;
	display: none;
	width: 281px;
	position: relative;
}

.columns-2 .inner-sidebar {
	margin-left: auto;
	width: 286px;
	display: block;
}

.inner-sidebar #side-sortables,
.columns-2 .inner-sidebar #side-sortables {
	min-height: 300px;
	width: 280px;
	padding: 0;
}

.has-right-sidebar .inner-sidebar {
	display: block;
}

.has-right-sidebar #post-body {
	float: right;
	clear: right;
	width: 100%;
	margin-left: -2000px;
}

.has-right-sidebar #post-body-content {
	margin-left: 300px;
	float: none;
	width: auto;
}

/* 2 columns main area */

#col-left {
	float: right;
	width: 35%;
}

#col-right {
	float: left;
	width: 65%;
}

#col-left .col-wrap {
	padding: 0 0 0 6px;
}

#col-right .col-wrap {
	padding: 0 6px 0 0;
}

/* utility classes */
.alignleft {
	float: right;
}

.alignright {
	float: left;
}

.textleft {
	text-align: right;
}

.textright {
	text-align: left;
}

.clear {
	clear: both;
}

/* modern clearfix */
.wp-clearfix:after {
	content: "";
	display: table;
	clear: both;
}

/* Hide visually but not from screen readers */
.screen-reader-text,
.screen-reader-text span,
.ui-helper-hidden-accessible {
	border: 0;
	clip-path: inset(50%);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute;
	width: 1px;
	word-wrap: normal !important; /* many screen reader and browser combinations announce broken words as they would appear visually */
}

.button .screen-reader-text {
	height: auto; /* Fixes a Safari+VoiceOver bug, see ticket #42006 */
}

.screen-reader-text + .dashicons-external {
	margin-top: -1px;
	margin-right: 2px;
}

.screen-reader-shortcut {
	position: absolute;
	top: -1000em;
	right: 6px;
	height: auto;
	width: auto;
	display: block;
	font-size: 14px;
	font-weight: 600;
	padding: 15px 23px 14px;
	/* Background and color set to prevent false positives in automated accessibility tests. */
	background: #f0f0f1;
	color: #2271b1;
	z-index: 100000;
	line-height: normal;
}

.screen-reader-shortcut:focus {
	top: -25px;
	/* Overrides a:focus in the admin. See ticket #56789. */
	color: #2271b1;
	box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
	text-decoration: none;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
	outline-offset: -2px;
}

.hidden,
.js .closed .inside,
.js .hide-if-js,
.no-js .hide-if-no-js,
.js.wp-core-ui .hide-if-js,
.js .wp-core-ui .hide-if-js,
.no-js.wp-core-ui .hide-if-no-js,
.no-js .wp-core-ui .hide-if-no-js {
	display: none;
}

/* @todo: Take a second look. Large chunks of shared color, from the colors.css merge */
.widget-top,
.menu-item-handle,
.widget-inside,
#menu-settings-column .accordion-container,
#menu-management .menu-edit,
.manage-menus,
table.widefat,
.stuffbox,
p.popular-tags,
.widgets-holder-wrap,
.wp-editor-container,
.popular-tags,
.feature-filter,
.comment-ays {
	border: 1px solid #c3c4c7;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

table.widefat,
.wp-editor-container,
.stuffbox,
p.popular-tags,
.widgets-holder-wrap,
.popular-tags,
.feature-filter,
.comment-ays {
	background: #fff;
}

/* general */
html,
body {
	height: 100%;
	margin: 0;
	padding: 0;
}

body {
	background: #f0f0f1;
	color: #3c434a;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 13px;
	line-height: 1.4em;
	min-width: 600px;
}

body.iframe {
	min-width: 0;
	padding-top: 1px;
}

body.modal-open {
	overflow: hidden;
}

body.mobile.modal-open #wpwrap {
	overflow: hidden;
	position: fixed;
	height: 100%;
}

iframe,
img {
	border: 0;
}

td {
	font-family: inherit;
	font-size: inherit;
	font-weight: inherit;
	line-height: inherit;
}

/* Any change to the default link style must be applied to button-link too. */
a {
	color: #2271b1;
	transition-property: border, background, color;
	transition-duration: .05s;
	transition-timing-function: ease-in-out;
}

a,
div {
	outline: 0;
}

a:hover,
a:active {
	color: #135e96;
}

a:focus,
a:focus .media-icon img,
a:focus .plugin-icon,
.wp-person a:focus .gravatar {
	color: #043959;
	box-shadow: 0 0 0 2px #2271b1;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

#adminmenu a:focus {
	box-shadow: none;
	/* Only visible in Windows High Contrast mode */
	outline: 1px solid transparent;
	outline-offset: -1px;
}

.screen-reader-text:focus {
	box-shadow: none;
	outline: none;
}

blockquote,
q {
	quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
	content: "";
	content: none;
}

p,
.wp-die-message {
	font-size: 13px;
	line-height: 1.5;
	margin: 1em 0;
}

blockquote {
	margin: 1em;
}

li,
dd {
	margin-bottom: 6px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	display: block;
	font-weight: 600;
}

h1 {
	color: #1d2327;
	font-size: 2em;
	margin: .67em 0;
}

h2,
h3 {
	color: #1d2327;
	font-size: 1.3em;
	margin: 1em 0;
}

.update-core-php h2 {
	margin-top: 4em;
}

.update-php h2,
.update-messages h2,
h4 {
	font-size: 1em;
	margin: 1.33em 0;
}

h5 {
	font-size: 0.83em;
	margin: 1.67em 0;
}

h6 {
	font-size: 0.67em;
	margin: 2.33em 0;
}

ul,
ol {
	padding: 0;
}

ul {
	list-style: none;
}

ol {
	list-style-type: decimal;
	margin-right: 2em;
}

ul.ul-disc {
	list-style: disc outside;
}

ul.ul-square {
	list-style: square outside;
}

ol.ol-decimal {
	list-style: decimal outside;
}

ul.ul-disc,
ul.ul-square,
ol.ol-decimal {
	margin-right: 1.8em;
}

ul.ul-disc > li,
ul.ul-square > li,
ol.ol-decimal > li {
	margin: 0 0 0.5em;
}

/* rtl:ignore */
.ltr {
	direction: ltr;
}

/* rtl:ignore */
.code,
code {
	font-family: Consolas, Monaco, monospace;
	direction: ltr;
	unicode-bidi: embed;
}

kbd,
code {
	padding: 3px 5px 2px;
	margin: 0 1px;
	background: #f0f0f1;
	background: rgba(0, 0, 0, 0.07);
	font-size: 13px;
}

.subsubsub {
	list-style: none;
	margin: 8px 0 0;
	padding: 0;
	font-size: 13px;
	float: right;
	color: #646970;
}

.subsubsub a {
	line-height: 2;
	padding: .2em;
	text-decoration: none;
}

.subsubsub a .count,
.subsubsub a.current .count {
	color: #50575e; /* #f1f1f1 background */
	font-weight: 400;
}

.subsubsub a.current {
	font-weight: 600;
	border: none;
}

.subsubsub li {
	display: inline-block;
	margin: 0;
	padding: 0;
	white-space: nowrap;
}

/* .widefat - main style for tables */
.widefat {
	border-spacing: 0;
	width: 100%;
	clear: both;
	margin: 0;
}

.widefat * {
	word-wrap: break-word;
}

.widefat a,
.widefat button.button-link {
	text-decoration: none;
}

.widefat td,
.widefat th {
	padding: 8px 10px;
}

.widefat thead th,
.widefat thead td {
	border-bottom: 1px solid #c3c4c7;
}

.widefat tfoot th,
.widefat tfoot td {
	border-top: 1px solid #c3c4c7;
	border-bottom: none;
}

.widefat .no-items td {
	border-bottom-width: 0;
}

.widefat td {
	vertical-align: top;
}

.widefat td,
.widefat td p,
.widefat td ol,
.widefat td ul {
	font-size: 13px;
	line-height: 1.5em;
}

.widefat th,
.widefat thead td,
.widefat tfoot td {
	text-align: right;
	line-height: 1.3em;
	font-size: 14px;
}

.widefat th input,
.updates-table td input,
.widefat thead td input,
.widefat tfoot td input {
	margin: 0 8px 0 0;
	padding: 0;
	vertical-align: text-top;
}

.widefat .check-column {
	width: 2.2em;
	padding: 6px 0 25px;
	vertical-align: top;
}

.widefat tbody th.check-column {
	padding: 9px 0 22px;
}

.widefat thead td.check-column,
.widefat tbody th.check-column,
.updates-table tbody td.check-column,
.widefat tfoot td.check-column {
	padding: 11px 3px 0 0;
}

.widefat thead td.check-column,
.widefat tfoot td.check-column {
	padding-top: 4px;
	vertical-align: middle;
}

.update-php div.updated,
.update-php div.error {
	margin-right: 0;
}

.js-update-details-toggle .dashicons {
	text-decoration: none;
}

.js-update-details-toggle[aria-expanded="true"] .dashicons::before {
	content: "\f142";
}

.no-js .widefat thead .check-column input,
.no-js .widefat tfoot .check-column input {
	display: none;
}

.widefat .num,
.column-comments,
.column-links,
.column-posts {
	text-align: center;
}

.widefat th#comments {
	vertical-align: middle;
}

.wrap {
	margin: 10px 2px 0 20px;
}

.wrap > h2:first-child, /* Back-compat for pre-4.4 */
.wrap [class$="icon32"] + h2, /* Back-compat for pre-4.4 */
.postbox .inside h2, /* Back-compat for pre-4.4 */
.wrap h1 {
	font-size: 23px;
	font-weight: 400;
	margin: 0;
	padding: 9px 0 4px;
	line-height: 1.3;
}

.wrap h1.wp-heading-inline {
	display: inline-block;
	margin-left: 5px;
}

.wp-header-end {
	visibility: hidden;
	margin: -2px 0 0;
}

.subtitle {
	margin: 0;
	padding-right: 25px;
	color: #50575e;
	font-size: 14px;
	font-weight: 400;
	line-height: 1;
}

.subtitle strong {
	word-break: break-all;
}

.wrap .add-new-h2, /* deprecated */
.wrap .add-new-h2:active, /* deprecated */
.wrap .page-title-action,
.wrap .page-title-action:active {
	display: inline-block;
	position: relative;
	box-sizing: border-box;
	cursor: pointer;
	white-space: nowrap;
	text-decoration: none;
	text-shadow: none;
	top: -3px;
	margin-right: 4px;
	border: 1px solid #2271b1;
	border-radius: 3px;
	background: #f6f7f7;
	font-size: 13px;
	font-weight: 400;
	line-height: 2.15384615;
	color: #2271b1; /* use the standard color used for buttons */
	padding: 0 10px;
	min-height: 30px;
	-webkit-appearance: none;

}

.wrap .wp-heading-inline + .page-title-action {
	margin-right: 0;
}

.wrap .add-new-h2:hover, /* deprecated */
.wrap .page-title-action:hover {
	background: #f0f0f1;
	border-color: #0a4b78;
	color: #0a4b78;
}

/* lower specificity: color needs to be overridden by :hover and :active */
.page-title-action:focus {
	color: #0a4b78;
}

/* Dashicon for language options on General Settings and Profile screens */
.form-table th label[for="locale"] .dashicons,
.form-table th label[for="WPLANG"] .dashicons {
	margin-right: 5px;
}

.wrap .page-title-action:focus {
	border-color: #3582c4;
	box-shadow: 0 0 0 1px #3582c4;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

.wrap h1.long-header {
	padding-left: 0;
}

.wp-dialog {
	background-color: #fff;
}

.widgets-chooser ul,
#widgets-left .widget-in-question .widget-top,
#available-widgets .widget-top:hover,
div#widgets-right .widget-top:hover,
#widgets-left .widget-top:hover {
	border-color: #8c8f94;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sorthelper {
	background-color: #c5d9ed;
}

.ac_match,
.subsubsub a.current {
	color: #000;
}

.striped > tbody > :nth-child(odd),
ul.striped > :nth-child(odd),
.alternate {
	background-color: #f6f7f7;
}

.bar {
	background-color: #f0f0f1;
	border-left-color: #4f94d4;
}

/* Helper classes for plugins to leverage the active WordPress color scheme */

.highlight {
	background-color: #f0f6fc;
	color: #3c434a;
}

.wp-ui-primary {
	color: #fff;
	background-color: #2c3338;
}
.wp-ui-text-primary {
	color: #2c3338;
}

.wp-ui-highlight {
	color: #fff;
	background-color: #2271b1;
}
.wp-ui-text-highlight {
	color: #2271b1;
}

.wp-ui-notification {
	color: #fff;
	background-color: #d63638;
}
.wp-ui-text-notification {
	color: #d63638;
}

.wp-ui-text-icon {
	color: #8c8f94; /* same as new icons */
}

/* For emoji replacement images */
img.emoji {
	display: inline !important;
	border: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
	box-shadow: none !important;
}

/*------------------------------------------------------------------------------
  1.0 - Text Styles
------------------------------------------------------------------------------*/

.widget .widget-top,
.postbox .hndle,
.stuffbox .hndle,
.control-section .accordion-section-title,
.sidebar-name,
#nav-menu-header,
#nav-menu-footer,
.menu-item-handle,
.checkbox,
.side-info,
#your-profile #rich_editing,
.widefat thead th,
.widefat thead td,
.widefat tfoot th,
.widefat tfoot td {
	line-height: 1.4em;
}

.widget .widget-top,
.menu-item-handle {
	background: #f6f7f7;
	color: #1d2327;
}

.stuffbox .hndle {
	border-bottom: 1px solid #c3c4c7;
}

.quicktags {
	background-color: #c3c4c7;
	color: #000;
	font-size: 12px;
}

.icon32 {
	display: none;
}

/* @todo can we combine these into a class or use an existing dashicon one? */
.welcome-panel .welcome-panel-close:before,
.tagchecklist .ntdelbutton .remove-tag-icon:before,
#bulk-titles .ntdelbutton:before,
.notice-dismiss:before {
	background: none;
	color: #787c82;
	content: "\f153";
	display: block;
	font: normal 16px/20px dashicons;
	speak: never;
	height: 20px;
	text-align: center;
	width: 20px;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.welcome-panel .welcome-panel-close:before {
	margin: 0;
}

.tagchecklist .ntdelbutton .remove-tag-icon:before {
	margin-right: 2px;
	border-radius: 50%;
	color: #2271b1;
	/* vertically center the icon cross browsers */
	line-height: 1.28;
}

.tagchecklist .ntdelbutton:focus {
	outline: 0;
}

.tagchecklist .ntdelbutton:hover .remove-tag-icon:before,
.tagchecklist .ntdelbutton:focus .remove-tag-icon:before,
#bulk-titles .ntdelbutton:hover:before,
#bulk-titles .ntdelbutton:focus:before {
	color: #d63638;
}

.tagchecklist .ntdelbutton:focus .remove-tag-icon:before {
	box-shadow: 0 0 0 2px #2271b1;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

.key-labels label {
	line-height: 24px;
}

strong, b {
	font-weight: 600;
}

.pre {
	/* https://developer.mozilla.org/en-US/docs/CSS/white-space */
	white-space: pre-wrap; /* css-3 */
	word-wrap: break-word; /* IE 5.5 - 7 */
}

.howto {
	color: #646970;
	display: block;
}

p.install-help {
	margin: 8px 0;
	font-style: italic;
}

.no-break {
	white-space: nowrap;
}

hr {
	border: 0;
	border-top: 1px solid #dcdcde;
	border-bottom: 1px solid #f6f7f7;
}

.row-actions span.delete a,
.row-actions span.trash a,
.row-actions span.spam a,
.plugins a.delete,
#all-plugins-table .plugins a.delete,
#search-plugins-table .plugins a.delete,
.submitbox .submitdelete,
#media-items a.delete,
#media-items a.delete-permanently,
#nav-menu-footer .menu-delete,
#delete-link a.delete,
a#remove-post-thumbnail,
.privacy_requests .remove-personal-data .remove-personal-data-handle {
	color: #b32d2e;
}

abbr.required,
span.required,
.file-error,
.row-actions .delete a:hover,
.row-actions .trash a:hover,
.row-actions .spam a:hover,
.plugins a.delete:hover,
#all-plugins-table .plugins a.delete:hover,
#search-plugins-table .plugins a.delete:hover,
.submitbox .submitdelete:hover,
#media-items a.delete:hover,
#media-items a.delete-permanently:hover,
#nav-menu-footer .menu-delete:hover,
#delete-link a.delete:hover,
a#remove-post-thumbnail:hover,
.privacy_requests .remove-personal-data .remove-personal-data-handle:hover {
	color: #b32d2e;
	border: none;
}

.application-password-display .success {
    color: #007017;
    margin-right: 0.5rem;
}

/*------------------------------------------------------------------------------
  3.0 - Actions
------------------------------------------------------------------------------*/

#major-publishing-actions {
	padding: 10px;
	clear: both;
	border-top: 1px solid #dcdcde;
	background: #f6f7f7;
}

#delete-action {
	float: right;
	line-height: 2.30769231; /* 30px */
}

#delete-link {
	line-height: 2.30769231; /* 30px */
	vertical-align: middle;
	text-align: right;
	margin-right: 8px;
}

#delete-link a {
	text-decoration: none;
}

#publishing-action {
	text-align: left;
	float: left;
	line-height: 1.9;
}

#publishing-action .spinner {
	float: none;
	margin-top: 5px;
}

#misc-publishing-actions {
	padding: 6px 0 0;
}

.misc-pub-section {
	padding: 6px 10px 8px;
}

.word-wrap-break-word,
.misc-pub-filename {
	word-wrap: break-word;
}

#minor-publishing-actions {
	padding: 10px 10px 0;
	text-align: left;
}

#save-post {
	float: right;
}

.preview {
	float: left;
}

#sticky-span {
	margin-right: 18px;
}

.approve,
.unapproved .unapprove {
	display: none;
}

.unapproved .approve,
.spam .approve,
.trash .approve {
	display: inline;
}

td.action-links,
th.action-links {
	text-align: left;
}

#misc-publishing-actions .notice {
	margin-right: 10px;
	margin-left: 10px;
}

/* Filter bar */
.wp-filter {
	display: inline-block;
	position: relative;
	box-sizing: border-box;
	margin: 12px 0 25px;
	padding: 0 10px;
	width: 100%;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	border: 1px solid #c3c4c7;
	background: #fff;
	color: #50575e;
	font-size: 13px;
}

.wp-filter a {
	text-decoration: none;
}

.filter-count {
	display: inline-block;
	vertical-align: middle;
	min-width: 4em;
}

.title-count,
.filter-count .count {
	display: inline-block;
	position: relative;
	top: -1px;
	padding: 4px 10px;
	border-radius: 30px;
	background: #646970;
	color: #fff;
	font-size: 14px;
	font-weight: 600;
}

/* not a part of filter bar, but derived from it, so here for now */
.title-count {
	display: inline;
	top: -3px;
	margin-right: 5px;
	margin-left: 20px;
}

.filter-items {
	float: right;
}

.filter-links {
	display: inline-block;
	margin: 0;
}

.filter-links li {
	display: inline-block;
	margin: 0;
}

.filter-links li > a {
	display: inline-block;
	margin: 0 10px;
	padding: 15px 0;
	border-bottom: 4px solid #fff;
	color: #646970;
	cursor: pointer;
}

.filter-links .current {
	box-shadow: none;
	border-bottom: 4px solid #646970;
	color: #1d2327;
}

.filter-links li > a:hover,
.filter-links li > a:focus,
.show-filters .filter-links a.current:hover,
.show-filters .filter-links a.current:focus {
	color: #135e96;
}

.wp-filter .search-form {
	float: left;
	display: flex;
	align-items: center;
	column-gap: .5rem;
}

.wp-filter .search-form input[type="search"] {
	width: 280px;
	max-width: 100%;
}

.wp-filter .search-form select {
	margin: 0;
}

/* Use flexbox only on the plugins install page. The `filter-links` and search form children will become flex items. */
.plugin-install-php .wp-filter {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	align-items: center;
}

.wp-filter .search-form.search-plugins select,
.wp-filter .search-form.search-plugins .wp-filter-search,
.no-js .wp-filter .search-form.search-plugins .button {
	display: inline-block;
	vertical-align: top;
}

.wp-filter .button.drawer-toggle {
	margin: 10px 9px 0;
	padding: 0 6px 0 10px;
	border-color: transparent;
	background-color: transparent;
	color: #646970;
	vertical-align: baseline;
	box-shadow: none;
}

.wp-filter .drawer-toggle:before {
	content: "\f111";
	margin: 0 0 0 5px;
	color: #646970;
	font: normal 16px/1 dashicons;
	vertical-align: text-bottom;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.wp-filter .button.drawer-toggle:hover,
.wp-filter .drawer-toggle:hover:before,
.wp-filter .button.drawer-toggle:focus,
.wp-filter .drawer-toggle:focus:before {
	background-color: transparent;
	color: #135e96;
}

.wp-filter .button.drawer-toggle:hover,
.wp-filter .button.drawer-toggle:focus:active {
	border-color: transparent;
}

.wp-filter .button.drawer-toggle:focus {
	border-color: #4f94d4;
}

.wp-filter .button.drawer-toggle:active {
	background: transparent;
	box-shadow: none;
	transform: none;
}

.wp-filter .drawer-toggle.current:before {
	color: #fff;
}

.filter-drawer,
.wp-filter .favorites-form {
	display: none;
	margin: 0 -20px 0 -10px;
	padding: 20px;
	border-top: 1px solid #f0f0f1;
	background: #f6f7f7;
	overflow: hidden;
}

.wp-filter .favorites-form .favorites-username {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 0.5rem;
}

.wp-filter .favorites-form .favorites-username input {
	margin: 0;
}

.show-filters .filter-drawer,
.show-favorites-form .favorites-form {
	display: block;
}

.show-filters .filter-links a.current {
	border-bottom: none;
}

.show-filters .wp-filter .button.drawer-toggle {
	border-radius: 2px;
	background: #646970;
	color: #fff;
}

.show-filters .wp-filter .drawer-toggle:hover,
.show-filters .wp-filter .drawer-toggle:focus {
	background: #2271b1;
}

.show-filters .wp-filter .drawer-toggle:before {
	color: #fff;
}

.filter-group {
	box-sizing: border-box;
	position: relative;
	float: right;
	margin: 0 0 0 1%;
	padding: 20px 10px 10px;
	width: 24%;
	background: #fff;
	border: 1px solid #dcdcde;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.filter-group legend {
	position: absolute;
	top: 10px;
	display: block;
	margin: 0;
	padding: 0;
	font-size: 1em;
	font-weight: 600;
}

.filter-drawer .filter-group-feature {
	margin: 28px 0 0;
	list-style-type: none;
	font-size: 12px;
}

.filter-drawer .filter-group-feature input,
.filter-drawer .filter-group-feature label {
	line-height: 1.4;
}

.filter-drawer .filter-group-feature input {
	position: absolute;
	margin: 0;
}

.filter-group .filter-group-feature label {
	display: block;
	margin: 14px 23px 14px 0;
}

.filter-drawer .buttons {
	clear: both;
	margin-bottom: 20px;
}

.filter-drawer .filter-group + .buttons {
	margin-bottom: 0;
	padding-top: 20px;
}

.filter-drawer .buttons .button span {
	display: inline-block;
	opacity: 0.8;
	font-size: 12px;
	text-indent: 10px;
}

.wp-filter .button.clear-filters {
	display: none;
	margin-right: 10px;
}

.wp-filter .button-link.edit-filters {
	padding: 0 5px;
	line-height: 2.2;
}

.filtered-by {
	display: none;
	margin: 0;
}

.filtered-by > span {
	font-weight: 600;
}

.filtered-by a {
	margin-right: 10px;
}

.filtered-by .tags {
	display: flex;
	align-items: flex-start;
	flex-wrap: wrap;
	gap: 8px;
}

.filtered-by .tag {
	padding: 4px 8px;
	border: 1px solid #dcdcde;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	background: #fff;
	font-size: 11px;
}

.filters-applied .filter-group,
.filters-applied .filter-drawer .buttons,
.filters-applied .filter-drawer br {
	display: none;
}

.filters-applied .filtered-by {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 10px;
}

.filters-applied .filter-drawer {
	padding: 20px;
}

.show-filters .favorites-form,
.show-filters .content-filterable,
.show-filters.filters-applied.loading-content .content-filterable,
.loading-content .content-filterable,
.error .content-filterable {
	display: none;
}

.show-filters.filters-applied .content-filterable {
	display: block;
}

.loading-content .spinner {
	display: block;
	margin: 40px auto 0;
	float: none;
}

@media only screen and (max-width: 1138px) {
	.wp-filter .search-form {
		margin: 11px 0;
	}
}

@media only screen and (max-width: 1120px) {
	.filter-drawer {
		border-bottom: 1px solid #f0f0f1;
	}

	.filter-group {
		margin-bottom: 0;
		margin-top: 5px;
		width: 100%;
	}

	.filter-group li {
		margin: 10px 0;
	}
}

@media only screen and (max-width: 1000px) {
	.filter-items {
		float: none;
	}

	.wp-filter .media-toolbar-primary,
	.wp-filter .media-toolbar-secondary,
	.wp-filter .search-form {
		float: none; /* Remove float from media-views.css */
		position: relative;
		max-width: 100%;
	}
	.wp-filter .search-form {
		margin: 11px 0;
		flex-wrap: wrap;
		row-gap: 10px;
	}
}

@media only screen and (max-width: 782px) {
	.filter-group li {
		padding: 0;
		width: 50%;
	}
}

@media only screen and (max-width: 320px) {
	.filter-count {
		display: none;
	}

	.wp-filter .drawer-toggle {
		margin: 10px 0;
	}

	.filter-group li,
	.wp-filter .search-form input[type="search"] {
		width: 100%;
	}
}

/*------------------------------------------------------------------------------
  4.0 - Notifications
------------------------------------------------------------------------------*/

.notice,
div.updated,
div.error {
	background: #fff;
	border: 1px solid #c3c4c7;
	border-right-width: 4px;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	margin: 5px 15px 2px;
	padding: 1px 12px;
}

div[class="update-message"] { /* back-compat for pre-4.6 */
	padding: 0.5em 0 0.5em 12px;
}

.notice p,
.notice-title,
div.updated p,
div.error p,
.form-table td .notice p {
	margin: 0.5em 0;
	padding: 2px;
}

.error a {
	text-decoration: underline;
}

.updated a {
	padding-bottom: 2px;
}

.notice-alt {
	box-shadow: none;
}

.notice-large {
	padding: 10px 20px;
}

.notice-title {
	display: inline-block;
	color: #1d2327;
	font-size: 18px;
}

.wp-core-ui .notice.is-dismissible {
	padding-left: 38px;
	position: relative;
}

.notice-dismiss {
	position: absolute;
	top: 0;
	left: 1px;
	border: none;
	margin: 0;
	padding: 9px;
	background: none;
	color: #787c82;
	cursor: pointer;
}

.notice-dismiss:hover:before,
.notice-dismiss:active:before,
.notice-dismiss:focus:before {
	color: #d63638;
}

.notice-dismiss:focus {
	box-shadow: 0 0 0 2px #2271b1;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

.notice-success,
div.updated {
	border-right-color: #00a32a;
}

.notice-success.notice-alt {
	background-color: #edfaef;
}

.notice-warning {
	border-right-color: #dba617;
}

.notice-warning.notice-alt {
	background-color: #fcf9e8;
}

.notice-error,
div.error {
	border-right-color: #d63638;
}

.notice-error.notice-alt {
	background-color: #fcf0f1;
}

.notice-info {
	border-right-color: #72aee6;
}

.notice-info.notice-alt {
	background-color: #f0f6fc;
}

#plugin-information-footer .update-now:not(.button-disabled):before {
	color: #d63638;
	content: "\f463";
	display: inline-block;
	font: normal 20px/1 dashicons;
	margin: -3px -2px 0 5px;
	speak: never;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	vertical-align: middle;
}

#plugin-information-footer .notice {
    margin-top: -5px;
}

.update-message p:before,
.updating-message p:before,
.updated-message p:before,
.import-php .updating-message:before,
.button.updating-message:before,
.button.updated-message:before,
.button.installed:before,
.button.installing:before,
.button.activating-message:before,
.button.activated-message:before {
	display: inline-block;
	font: normal 20px/1 'dashicons';
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	vertical-align: top;
}

.wrap .notice,
.wrap div.updated,
.wrap div.error,
.media-upload-form .notice,
.media-upload-form div.error {
	margin: 5px 0 15px;
}

.wrap #templateside .notice {
	display: block;
	margin: 0;
	padding: 5px 8px;
	font-weight: 600;
	text-decoration: none;
}

.wrap #templateside span.notice {
	margin-right: -12px;
}

#templateside li.notice a {
	padding: 0;
}

/* Update icon. */
.update-message p:before,
.updating-message p:before,
.import-php .updating-message:before,
.button.updating-message:before,
.button.installing:before,
.button.activating-message:before {
	color: #d63638;
	content: "\f463";
}

/* Spins the update icon. */
.updating-message p:before,
.import-php .updating-message:before,
.button.updating-message:before,
.button.installing:before,
.button.activating-message:before,
.plugins .column-auto-updates .dashicons-update.spin,
.theme-overlay .theme-autoupdate .dashicons-update.spin {
	animation: rotation 2s infinite linear;
}

@media (prefers-reduced-motion: reduce) {
	.updating-message p:before,
	.import-php .updating-message:before,
	.button.updating-message:before,
	.button.installing:before,
	.button.activating-message:before,
	.plugins .column-auto-updates .dashicons-update.spin,
	.theme-overlay .theme-autoupdate .dashicons-update.spin {
		animation: none;
	}
}

.theme-overlay .theme-autoupdate .dashicons-update.spin {
	margin-left: 3px;
}

/* Updated icon (check mark). */
.updated-message p:before,
.installed p:before,
.button.updated-message:before,
.button.activated-message:before {
	color: #68de7c;
	content: "\f147";
}

/* Error icon. */
.update-message.notice-error p:before {
	color: #d63638;
	content: "\f534";
}

.wrap .notice p:before,
.import-php .updating-message:before {
	margin-left: 6px;
}

.import-php .updating-message:before {
	vertical-align: bottom;
}

#update-nag,
.update-nag {
	display: inline-block;
	line-height: 1.4;
	padding: 11px 15px;
	font-size: 14px;
	margin: 25px 2px 0 20px;
}

ul#dismissed-updates {
	display: none;
}

#dismissed-updates li > p {
	margin-top: 0;
}

#dismiss,
#undismiss {
	margin-right: 0.5em;
}

form.upgrade {
	margin-top: 8px;
}

form.upgrade .hint {
	font-style: italic;
	font-size: 85%;
	margin: -0.5em 0 2em;
}

.update-php .spinner {
	float: none;
	margin: -4px 0;
}

h2.wp-current-version {
	margin-bottom: .3em;
}

p.update-last-checked {
	margin-top: 0;
}

p.auto-update-status {
	margin-top: 2em;
	line-height: 1.8;
}

#ajax-loading,
.ajax-loading,
.ajax-feedback,
.imgedit-wait-spin,
.list-ajax-loading { /* deprecated */
	visibility: hidden;
}

#ajax-response.alignleft {
	margin-right: 2em;
}

.button.updating-message:before,
.button.updated-message:before,
.button.installed:before,
.button.installing:before,
.button.activated-message:before,
.button.activating-message:before {
	margin: 3px -2px 0 5px;
}

#plugin-information-footer .button {
	padding: 0 14px;
	line-height: 2.71428571; /* 38px */
	font-size: 14px;
	vertical-align: middle;
	min-height: 40px;
	margin-bottom: 4px;
}

#plugin-information-footer .button.installed:before,
#plugin-information-footer .button.installing:before,
#plugin-information-footer .button.updating-message:before,
#plugin-information-footer .button.updated-message:before,
#plugin-information-footer .button.activated-message:before,
#plugin-information-footer .button.activating-message:before {
	margin: 9px -2px 0 5px;
}

#plugin-information-footer .button.update-now.updating-message:before {
	margin: -3px -2px 0 5px;
}

.button-primary.updating-message:before,
.button-primary.activating-message:before {
	color: #fff;
}

.button-primary.updated-message:before,
.button-primary.activated-message:before {
	color: #9ec2e6;
}

.button.updated-message,
.button.activated-message {
	transition-property: border, background, color;
	transition-duration: .05s;
	transition-timing-function: ease-in-out;
}

@media aural {
	.wrap .notice p:before,
	.button.installing:before,
	.button.installed:before,
	.update-message p:before {
		speak: never;
	}
}


/* @todo: this does not need its own section anymore */
/*------------------------------------------------------------------------------
  6.0 - Admin Header
------------------------------------------------------------------------------*/
#adminmenu a,
#taglist a,
#catlist a {
	text-decoration: none;
}

/*------------------------------------------------------------------------------
  6.1 - Screen Options Tabs
------------------------------------------------------------------------------*/

#screen-options-wrap,
#contextual-help-wrap {
	margin: 0;
	padding: 8px 20px 12px;
	position: relative;
}

#contextual-help-wrap {
	overflow: auto;
	margin-right: 0;
}

#screen-meta-links {
	float: left;
	margin: 0 0 0 20px;
}

/* screen options and help tabs revert */
#screen-meta {
	display: none;
	margin: 0 0 -1px 20px;
	position: relative;
	background-color: #fff;
	border: 1px solid #c3c4c7;
	border-top: none;
	box-shadow: 0 0 0 transparent;
}

#screen-options-link-wrap,
#contextual-help-link-wrap {
	float: right;
	margin: 0 6px 0 0;
}

#screen-meta-links .screen-meta-toggle {
	position: relative;
	top: 0;
}

#screen-meta-links .show-settings {
	border: 1px solid #c3c4c7;
	border-top: none;
	height: auto;
	margin-bottom: 0;
	padding: 3px 16px 3px 6px;
	background: #fff;
	border-radius: 0 0 4px 4px;
	color: #646970;
	line-height: 1.7;
	box-shadow: 0 0 0 transparent;
	transition: box-shadow 0.1s linear;
}

#screen-meta-links .show-settings:hover,
#screen-meta-links .show-settings:active,
#screen-meta-links .show-settings:focus {
	color: #2c3338;
}

#screen-meta-links .show-settings:focus {
	border-color: #2271b1;
	box-shadow: 0 0 0 1px #2271b1;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

#screen-meta-links .show-settings:active {
	transform: none;
}

#screen-meta-links .show-settings:after {
	left: 0;
	content: "\f140";
	font: normal 20px/1 dashicons;
	speak: never;
	display: inline-block;
	padding: 0 0 0 5px;
	bottom: 2px;
	position: relative;
	vertical-align: bottom;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-decoration: none;
}

#screen-meta-links .screen-meta-active:after {
	content: "\f142";
}

/* end screen options and help tabs */

.toggle-arrow {
	background-repeat: no-repeat;
	background-position: top right;
	background-color: transparent;
	height: 22px;
	line-height: 22px;
	display: block;
}

.toggle-arrow-active {
	background-position: bottom right;
}

#screen-options-wrap h5, /* Back-compat for old plugins */
#screen-options-wrap legend,
#contextual-help-wrap h5 {
	margin: 0;
	padding: 8px 0;
	font-size: 13px;
	font-weight: 600;
}

.metabox-prefs label {
	display: inline-block;
	padding-left: 15px;
	line-height: 2.35;
}

#number-of-columns {
	display: inline-block;
	vertical-align: middle;
	line-height: 30px;
}

.metabox-prefs input[type=checkbox] {
	margin-top: 0;
	margin-left: 6px;
}

.metabox-prefs label input,
.metabox-prefs label input[type=checkbox] {
	margin: -4px 0 0 5px;
}

.metabox-prefs .columns-prefs label input {
	margin: -1px 0 0 2px;
}

.metabox-prefs label a {
	display: none;
}

.metabox-prefs .screen-options input,
.metabox-prefs .screen-options label {
	margin-top: 0;
	margin-bottom: 0;
	vertical-align: middle;
}

.metabox-prefs .screen-options .screen-per-page {
	margin-left: 15px;
	padding-left: 0;
}

.metabox-prefs .screen-options label {
	line-height: 2.2;
	padding-left: 0;
}

.screen-options + .screen-options {
	margin-top: 10px;
}

.metabox-prefs .submit {
	margin-top: 1em;
	padding: 0;
}

/*------------------------------------------------------------------------------
  6.2 - Help Menu
------------------------------------------------------------------------------*/

#contextual-help-wrap {
	padding: 0;
}

#contextual-help-columns {
	position: relative;
}

#contextual-help-back {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 150px;
	left: 170px;
	border: 1px solid #c3c4c7;
	border-top: none;
	border-bottom: none;
	background: #f0f6fc;
}

#contextual-help-wrap.no-sidebar #contextual-help-back {
	left: 0;
	border-left-width: 0;
	border-bottom-left-radius: 2px;
}

.contextual-help-tabs {
	float: right;
	width: 150px;
	margin: 0;
}

.contextual-help-tabs ul {
	margin: 1em 0;
}

.contextual-help-tabs li {
	margin-bottom: 0;
	list-style-type: none;
	border-style: solid;
	border-width: 0 2px 0 0;
	border-color: transparent;
}

.contextual-help-tabs a {
	display: block;
	padding: 5px 12px 5px 5px;
	line-height: 1.4;
	text-decoration: none;
	border: 1px solid transparent;
	border-left: none;
	border-right: none;
}

.contextual-help-tabs a:hover {
	color: #2c3338;
}

.contextual-help-tabs .active {
	padding: 0;
	margin: 0 0 0 -1px;
	border-right: 2px solid #72aee6;
	background: #f0f6fc;
	box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02), 0 1px 0 rgba(0, 0, 0, 0.02);
}

.contextual-help-tabs .active a {
	border-color: #c3c4c7;
	color: #2c3338;
}

.contextual-help-tabs-wrap {
	padding: 0 20px;
	overflow: auto;
}

.help-tab-content {
	display: none;
	margin: 0 0 12px 22px;
	line-height: 1.6;
}

.help-tab-content.active {
	display: block;
}

.help-tab-content ul li {
	list-style-type: disc;
	margin-right: 18px;
}

.contextual-help-sidebar {
	width: 150px;
	float: left;
	padding: 0 12px 0 8px;
	overflow: auto;
}

/*------------------------------------------------------------------------------
  8.0 - Layout Blocks
------------------------------------------------------------------------------*/

html.wp-toolbar {
	padding-top: var(--wp-admin--admin-bar--height);
	box-sizing: border-box;
	-ms-overflow-style: scrollbar; /* See ticket #48545 */
}

.widefat th,
.widefat td {
	color: #50575e;
}

.widefat th,
.widefat thead td,
.widefat tfoot td {
	font-weight: 400;
}

.widefat thead tr th,
.widefat thead tr td,
.widefat tfoot tr th,
.widefat tfoot tr td {
	color: #2c3338;
}

.widefat td p {
	margin: 2px 0 0.8em;
}

.widefat p,
.widefat ol,
.widefat ul {
	color: #2c3338;
}

.widefat .column-comment p {
	margin: 0.6em 0;
}

.widefat .column-comment ul {
	list-style: initial;
	margin-right: 2em;
}

/* Screens with postboxes */
.postbox-container {
	float: right;
}

.postbox-container .meta-box-sortables {
	box-sizing: border-box;
}

#wpbody-content .metabox-holder {
	padding-top: 10px;
}

.metabox-holder .postbox-container .meta-box-sortables {
	/* The jQuery UI Sortables need some initial height to work properly. */
	min-height: 1px;
	position: relative;
}

#post-body-content {
	width: 100%;
	min-width: 463px;
	float: right;
}

#post-body.columns-2 #postbox-container-1 {
	float: left;
	margin-left: -300px;
	width: 280px;
}

#post-body.columns-2 #side-sortables {
	min-height: 250px;
}

/* one column on the dash */
@media only screen and (max-width: 799px) {
	#wpbody-content .metabox-holder .postbox-container .empty-container {
		outline: none;
		height: 0;
		min-height: 0;
	}
}

.js .widget .widget-top,
.js .postbox .hndle {
	cursor: move;
}

.js .widget .widget-top.is-non-sortable,
.js .postbox .hndle.is-non-sortable {
	cursor: auto;
}

/* Configurable dashboard widgets "Configure" edit-box link. */
.hndle a {
	font-size: 12px;
	font-weight: 400;
}

.postbox-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid #c3c4c7;
}

.postbox-header .hndle {
	flex-grow: 1;
	/* Handle the alignment for the configurable dashboard widgets "Configure" edit-box link. */
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.postbox-header .handle-actions {
	flex-shrink: 0;
}

/* Post box order and toggle buttons. */
.postbox .handle-order-higher,
.postbox .handle-order-lower,
.postbox .handlediv {
	width: 1.62rem;
	height: 1.62rem;
	margin: 0;
	padding: 0;
	border: 0;
	background: none;
	cursor: pointer;
}

.postbox .handle-order-higher,
.postbox .handle-order-lower {
	color: #787c82;
	width: 1.62rem;
}

/* Post box order buttons in the block editor meta boxes area. */
.edit-post-meta-boxes-area .postbox .handle-order-higher,
.edit-post-meta-boxes-area .postbox .handle-order-lower {
	width: 44px;
	height: 44px;
	color: #1d2327
}

.postbox .handle-order-higher[aria-disabled="true"],
.postbox .handle-order-lower[aria-disabled="true"] {
	cursor: default;
	color: #a7aaad;
}

.sortable-placeholder {
	border: 1px dashed #c3c4c7;
	margin-bottom: 20px;
}

.postbox,
.stuffbox {
	margin-bottom: 20px;
	padding: 0;
	line-height: 1;
}

.postbox.closed {
	border-bottom: 0;
}

/* user-select is not a part of the CSS standard - may change behavior in the future */
.postbox .hndle,
.stuffbox .hndle {
	-webkit-user-select: none;
	user-select: none;
}

.postbox .inside {
	padding: 0 12px 12px;
	line-height: 1.4;
	font-size: 13px;
}

.stuffbox .inside {
	padding: 0;
	line-height: 1.4;
	font-size: 13px;
	margin-top: 0;
}

.postbox .inside {
	margin: 11px 0;
	position: relative;
}

.postbox .inside > p:last-child,
.rss-widget ul li:last-child {
	margin-bottom: 1px !important;
}

.postbox.closed h3 {
	border: none;
	box-shadow: none;
}

.postbox table.form-table {
	margin-bottom: 0;
}

.postbox table.widefat {
	box-shadow: none;
}

.temp-border {
	border: 1px dotted #c3c4c7;
}

.columns-prefs label {
	padding: 0 0 0 10px;
}

/* @todo: what is this doing here */
#dashboard_right_now .versions .b,
#post-status-display,
#post-visibility-display,
#adminmenu .wp-submenu li.current,
#adminmenu .wp-submenu li.current a,
#adminmenu .wp-submenu li.current a:hover,
.media-item .percent,
.plugins .name,
#pass-strength-result.strong,
#pass-strength-result.short,
#ed_reply_toolbar #ed_reply_strong,
.item-controls .item-order a,
.feature-filter .feature-name,
#comment-status-display {
	font-weight: 600;
}

/*------------------------------------------------------------------------------
  21.0 - Admin Footer
------------------------------------------------------------------------------*/

#wpfooter {
	position: absolute;
	bottom: 0;
	right: 0;
	left: 0;
	padding: 10px 20px;
	color: #50575e;
}

#wpfooter p {
	font-size: 13px;
	margin: 0;
	line-height: 1.55;
}

#footer-thankyou {
	font-style: italic;
}

/*------------------------------------------------------------------------------
  25.0 - Tabbed Admin Screen Interface (Experimental)
------------------------------------------------------------------------------*/

.nav-tab {
	float: right;
	border: 1px solid #c3c4c7;
	border-bottom: none;
	margin-right: 0.5em; /* half the font size so set the font size properly */
	padding: 5px 10px;
	font-size: 14px;
	line-height: 1.71428571;
	font-weight: 600;
	background: #dcdcde;
	color: #50575e;
	text-decoration: none;
	white-space: nowrap;
}

h3 .nav-tab, /* Back-compat for pre-4.4 */
.nav-tab-small .nav-tab {
	padding: 5px 14px;
	font-size: 12px;
	line-height: 1.33;
}

.nav-tab:hover,
.nav-tab:focus {
	background-color: #fff;
	color: #3c434a;
}

.nav-tab-active,
.nav-tab:focus:active {
	box-shadow: none;
}

.nav-tab-active {
	margin-bottom: -1px;
	color: #3c434a;
}

.nav-tab-active,
.nav-tab-active:hover,
.nav-tab-active:focus,
.nav-tab-active:focus:active {
	border-bottom: 1px solid #f0f0f1;
	background: #f0f0f1;
	color: #000;
}

h1.nav-tab-wrapper, /* Back-compat for pre-4.4 */
.wrap h2.nav-tab-wrapper, /* higher specificity to override .wrap > h2:first-child */
.nav-tab-wrapper {
	border-bottom: 1px solid #c3c4c7;
	margin: 0;
	padding-top: 9px;
	padding-bottom: 0;
	line-height: inherit;
}

/* Back-compat for plugins. Deprecated. Use .wp-clearfix instead. */
.nav-tab-wrapper:not(.wp-clearfix):after {
	content: "";
	display: table;
	clear: both;
}

/*------------------------------------------------------------------------------
  26.0 - Misc
------------------------------------------------------------------------------*/

.spinner {
	background: url(../images/spinner.gif) no-repeat;
	background-size: 20px 20px;
	display: inline-block;
	visibility: hidden;
	float: left;
	vertical-align: middle;
	opacity: 0.7;
	filter: alpha(opacity=70);
	width: 20px;
	height: 20px;
	margin: 4px 10px 0;
}

.spinner.is-active,
.loading-content .spinner {
	visibility: visible;
}

#template > div {
	margin-left: 16em;
}
#template .notice {
	margin-top: 1em;
	margin-left: 3%;
}
#template .notice p {
	width: auto;
}
#template .submit .spinner {
	float: none;
}

.metabox-holder .stuffbox > h3, /* Back-compat for pre-4.4 */
.metabox-holder .postbox > h3, /* Back-compat for pre-4.4 */
.metabox-holder h3.hndle, /* Back-compat for pre-4.4 */
.metabox-holder h2.hndle {
	font-size: 14px;
	padding: 8px 12px;
	margin: 0;
	line-height: 1.4;
}

/* Back-compat for nav-menus screen */
.nav-menus-php .metabox-holder h3 {
	padding: 0;
}

.accordion-container h3.accordion-section-title {
	padding: 0 !important;
}

.accordion-section-title button.accordion-trigger,
.nav-menus-php .metabox-holder .accordion-section-title button.accordion-trigger {
	background: inherit;
	color: #1d2327;
	display: block;
	position: relative;
	text-align: right;
	width: 100%;
	outline: none;
	border: 0;
	padding: 10px 14px 11px 10px;
	line-height: 1.5;
	cursor: pointer;
}

.accordion-section-title button.accordion-trigger:focus,
.nav-menus-php .metabox-holder .accordion-section-title button.accordion-trigger:focus {
	box-shadow: 0 0 0 2px #2271b1;
	outline: 2px solid transparent;
}

.accordion-section-title span.dashicons.dashicons-arrow-down,
.nav-menus-php .metabox-holder .accordion-section-title span.dashicons.dashicons-arrow-down {
	position: absolute;
	left: 10px;
	right: auto;
	color: #787c82;
	border-radius: 50px;
	top: 50%;
	transform: translateY(-50%);
}

.accordion-section-title:hover span.dashicons.dashicons-arrow-down,
.nav-menus-php .metabox-holder .accordion-section-title:hover span.dashicons.dashicons-arrow-down {
	color: #1d2327;
}

.accordion-section-title span.dashicons.dashicons-arrow-down::before,
.nav-menus-php .metabox-holder .accordion-section-title span.dashicons.dashicons-arrow-down::before {
	position: relative;
	right: -1px;
}

.accordion-section.open .accordion-section-title span.dashicons.dashicons-arrow-down,
.nav-menus-php .metabox-holder .accordion-section.open .accordion-section-title span.dashicons.dashicons-arrow-down {
	transform: rotate(-180deg) translate(0, 50%);
}

#templateside ul li a {
	text-decoration: none;
}

.plugin-install #description,
.plugin-install-network #description {
	width: 60%;
}

table .vers,
table .column-visible,
table .column-rating {
	text-align: right;
}

.attention,
.error-message {
	color: #d63638;
	font-weight: 600;
}

/* Scrollbar fix for bulk upgrade iframe */
body.iframe {
	height: 98%;
}

/* Upgrader styles, Specific to Language Packs */
.lp-show-latest p {
	display: none;
}
.lp-show-latest p:last-child,
.lp-show-latest .lp-error p {
	display: block;
}

/* - Only used once or twice in all of WP - deprecate for global style
------------------------------------------------------------------------------*/
.media-icon {
	width: 62px; /* icon + border */
	text-align: center;
}

.media-icon img {
	border: 1px solid #dcdcde;
	border: 1px solid rgba(0, 0, 0, 0.07);
}

#howto {
	font-size: 11px;
	margin: 0 5px;
	display: block;
}

.importers {
	font-size: 16px;
	width: auto;
}

.importers td {
	padding-left: 14px;
	line-height: 1.4;
}

.importers .import-system {
	max-width: 250px;
}

.importers td.desc {
	max-width: 500px;
}

.importer-title,
.importer-desc,
.importer-action {
	display: block;
}

.importer-title {
	color: #000;
	font-size: 14px;
	font-weight: 400;
	margin-bottom: .2em;
}

.importer-action {
	line-height: 1.55; /* Same as with .updating-message */
	color: #50575e;
	margin-bottom: 1em;
}

#post-body #post-body-content #namediv h3, /* Back-compat for pre-4.4 */
#post-body #post-body-content #namediv h2 {
	margin-top: 0;
}

.edit-comment-author {
	color: #1d2327;
	border-bottom: 1px solid #f0f0f1;
}

#namediv h3 label, /* Back-compat for pre-4.4 */
#namediv h2 label {
	vertical-align: baseline;
}

#namediv table {
	width: 100%;
}

#namediv td.first {
	width: 10px;
	white-space: nowrap;
}

#namediv input {
	width: 100%;
}

#namediv p {
	margin: 10px 0;
}

/* - Used - but could/should be deprecated with a CSS reset
------------------------------------------------------------------------------*/
.zerosize {
	height: 0;
	width: 0;
	margin: 0;
	border: 0;
	padding: 0;
	overflow: hidden;
	position: absolute;
}

br.clear {
	height: 2px;
	line-height: 0.15;
}

.checkbox {
	border: none;
	margin: 0;
	padding: 0;
}

fieldset {
	border: 0;
	padding: 0;
	margin: 0;
}

.post-categories {
	display: inline;
	margin: 0;
	padding: 0;
}

.post-categories li {
	display: inline;
}

/* Star Ratings - Back-compat for pre-3.8 */
div.star-holder {
	position: relative;
	height: 17px;
	width: 100px;
	background: url(../images/stars.png?ver=20121108) repeat-x bottom right;
}

div.star-holder .star-rating {
	background: url(../images/stars.png?ver=20121108) repeat-x top right;
	height: 17px;
	float: right;
}

/* Star Ratings */
.star-rating {
	white-space: nowrap;
}
.star-rating .star {
	display: inline-block;
	width: 20px;
	height: 20px;
	-webkit-font-smoothing: antialiased;
	font-size: 20px;
	line-height: 1;
	font-family: dashicons;
	text-decoration: inherit;
	font-weight: 400;
	font-style: normal;
	vertical-align: top;
	transition: color .1s ease-in;
	text-align: center;
	color: #dba617;
}

.star-rating .star-full:before {
	content: "\f155";
}

.star-rating .star-half:before {
	content: "\f459";
}

.rtl .star-rating .star-half {
	transform: rotateY(-180deg);
}

.star-rating .star-empty:before {
	content: "\f154";
}

div.action-links {
	font-weight: 400;
	margin: 6px 0 0;
}

/* Plugin install thickbox */
#plugin-information {
	background: #fff;
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	height: 100%;
	padding: 0;
}

#plugin-information-scrollable {
	overflow: auto;
	-webkit-overflow-scrolling: touch;
	height: 100%;
}

#plugin-information-title {
	padding: 0 26px;
	background: #f6f7f7;
	font-size: 22px;
	font-weight: 600;
	line-height: 2.4;
	position: relative;
	height: 56px;
}

#plugin-information-title.with-banner {
	margin-left: 0;
	height: 250px;
	background-size: cover;
}

#plugin-information-title h2 {
	font-size: 1em;
	font-weight: 600;
	padding: 0;
	margin: 0;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

#plugin-information-title.with-banner h2 {
	position: relative;
	font-family: "Helvetica Neue", sans-serif;
	display: inline-block;
	font-size: 30px;
	line-height: 1.68;
	box-sizing: border-box;
	max-width: 100%;
	padding: 0 15px;
	margin-top: 174px;
	color: #fff;
	background: rgba(29, 35, 39, 0.9);
	text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
	box-shadow: 0 0 30px rgba(255, 255, 255, 0.1);
	border-radius: 8px;
}

#plugin-information-title div.vignette {
	display: none;
}

#plugin-information-title.with-banner div.vignette {
	position: absolute;
	display: block;
	top: 0;
	right: 0;
	height: 250px;
	width: 100%;
	background: transparent;
	box-shadow: inset 0 0 50px 4px rgba(0, 0, 0, 0.2), inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

#plugin-information-tabs {
	padding: 0 16px;
	position: relative;
	left: 0;
	right: 0;
	min-height: 36px;
	font-size: 0;
	z-index: 1;
	border-bottom: 1px solid #dcdcde;
	background: #f6f7f7;
}

#plugin-information-tabs a {
	position: relative;
	display: inline-block;
	padding: 9px 10px;
	margin: 0;
	height: 18px;
	line-height: 1.3;
	font-size: 14px;
	text-decoration: none;
	transition: none;
}

#plugin-information-tabs a.current {
	margin: 0 -1px -1px;
	background: #fff;
	border: 1px solid #dcdcde;
	border-bottom-color: #fff;
	padding-top: 8px;
	color: #2c3338;
}

#plugin-information-tabs.with-banner a.current {
	border-top: none;
	padding-top: 9px;
}

#plugin-information-tabs a:active,
#plugin-information-tabs a:focus {
	outline: none;
}

#plugin-information-content {
	overflow: hidden; /* equal height column trick */
	background: #fff;
	position: relative;
	top: 0;
	left: 0;
	right: 0;
	min-height: 100%;
	/* Height of title + tabs + install now */
	min-height: calc( 100% - 152px );
}

#plugin-information-content.with-banner {
	/* Height of banner + tabs + install now */
	min-height: calc( 100% - 346px );
}

#section-holder {
	position: relative;
	top: 0;
	left: 250px;
	bottom: 0;
	right: 0;
	margin-top: 10px;
	margin-left: 250px; /* FYI box */
	padding: 10px 26px 99999px; /* equal height column trick */
	margin-bottom: -99932px; /* 67px less than the padding below to accommodate footer height */
}

#section-holder .notice {
	margin: 5px 0 15px;
}

#section-holder .updated {
	margin: 16px 0;
}

#plugin-information .fyi {
	float: left;
	position: relative;
	top: 0;
	left: 0;
	padding: 16px 16px 99999px; /* equal height column trick */
	margin-bottom: -99932px; /* 67px less than the padding below to accommodate footer height */
	width: 217px;
	border-right: 1px solid #dcdcde;
	background: #f6f7f7;
	color: #646970;
}

#plugin-information .fyi strong {
	color: #3c434a;
}

#plugin-information .fyi h3 {
	font-weight: 600;
	text-transform: uppercase;
	font-size: 12px;
	color: #646970;
	margin: 24px 0 8px;
}

#plugin-information .fyi h2 {
	font-size: 0.9em;
	margin-bottom: 0;
	margin-left: 0;
}

#plugin-information .fyi ul {
	padding: 0;
	margin: 0;
	list-style: none;
}

#plugin-information .fyi li {
	margin: 0 0 10px;
}

#plugin-information .fyi-description {
	margin-top: 0;
}

#plugin-information .counter-container {
	margin: 3px 0;
}

#plugin-information .counter-label {
	float: right;
	margin-left: 5px;
	min-width: 55px;
}

#plugin-information .counter-back {
	height: 17px;
	width: 92px;
	background-color: #dcdcde;
	float: right;
}

#plugin-information .counter-bar {
	height: 17px;
	background-color: #f0c33c; /* slightly lighter than stars due to larger expanse */
	float: right;
}

#plugin-information .counter-count {
	margin-right: 5px;
}

#plugin-information .fyi ul.contributors {
	margin-top: 10px;
}

#plugin-information .fyi ul.contributors li {
	display: inline-block;
	margin-left: 8px;
	vertical-align: middle;
}

#plugin-information .fyi ul.contributors li {
	display: inline-block;
	margin-left: 8px;
	vertical-align: middle;
}

#plugin-information .fyi ul.contributors li img {
	vertical-align: middle;
	margin-left: 4px;
}

#plugin-information-footer {
	padding: 13px 16px;
	position: absolute;
	left: 0;
	bottom: 0;
	right: 0;
	height: 40px; /* actual height: 40+13+13+1=67 */
	border-top: 1px solid #dcdcde;
	background: #f6f7f7;
}

/* rtl:ignore */
#plugin-information .section {
	direction: ltr;
}

/* rtl:ignore */
#plugin-information .section ul,
#plugin-information .section ol {
	list-style-type: disc;
	margin-left: 24px;
}

#plugin-information .section,
#plugin-information .section p {
	font-size: 14px;
	line-height: 1.7;
}

#plugin-information #section-screenshots ol {
	list-style: none;
	margin: 0;
}

#plugin-information #section-screenshots li img {
	vertical-align: text-top;
	margin-top: 16px;
	max-width: 100%;
	width: auto;
	height: auto;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* rtl:ignore */
#plugin-information #section-screenshots li p {
	font-style: italic;
	padding-left: 20px;
}

#plugin-information pre {
	padding: 7px;
	overflow: auto;
	border: 1px solid #c3c4c7;
}

#plugin-information blockquote {
	border-right: 2px solid #dcdcde;
	color: #646970;
	font-style: italic;
	margin: 1em 0;
	padding: 0 1em 0 0;
}

/* rtl:ignore */
#plugin-information .review {
	overflow: hidden; /* clearfix */
	width: 100%;
	margin-bottom: 20px;
	border-bottom: 1px solid #dcdcde;
}

#plugin-information .review-title-section {
	overflow: hidden; /* clearfix */
}

/* rtl:ignore */
#plugin-information .review-title-section h4 {
	display: inline-block;
	float: left;
	margin: 0 6px 0 0;
}

#plugin-information .reviewer-info p {
	clear: both;
	margin: 0;
	padding-top: 2px;
}

/* rtl:ignore */
#plugin-information .reviewer-info .avatar {
	float: left;
	margin: 4px 6px 0 0;
}

/* rtl:ignore */
#plugin-information .reviewer-info .star-rating {
	float: left;
}

/* rtl:ignore */
#plugin-information .review-meta {
	float: left;
	margin-left: 0.75em;
}

/* rtl:ignore */
#plugin-information .review-body {
	float: left;
	width: 100%;
}

.plugin-version-author-uri {
	font-size: 13px;
}

/* For non-js plugin installation screen ticket #36430. */
.update-php .button.button-primary {
	margin-left: 1em;
}

@media screen and (max-width: 771px) {
	#plugin-information-title.with-banner {
		height: 100px;
	}

	#plugin-information-title.with-banner h2 {
		margin-top: 30px;
		font-size: 20px;
		line-height: 2;
		max-width: 85%;
	}

	#plugin-information-title.with-banner div.vignette {
		height: 100px;
	}

	#plugin-information-tabs {
		overflow: hidden; /* clearfix */
		padding: 0;
		height: auto; /* let tabs wrap */
	}

	#plugin-information-tabs a.current {
		margin-bottom: 0;
		border-bottom: none;
	}

	#plugin-information .fyi {
		float: none;
		border: 1px solid #dcdcde;
		position: static;
		width: auto;
		margin: 26px 26px 0;
		padding-bottom: 0; /* reset from the two column height fix */
	}

	#section-holder {
		position: static;
		margin: 0;
		padding-bottom: 70px; /* reset from the two column height fix, plus accommodate footer */
	}

	#plugin-information .fyi h3,
	#plugin-information .fyi small {
		display: none;
	}

	#plugin-information-footer {
		padding: 12px 16px 0;
		height: 46px;
	}
}

/* Thickbox for the Plugin details modal. */
#TB_window.plugin-details-modal {
	background: #fff;
}

#TB_window.plugin-details-modal.thickbox-loading:before {
	content: "";
	display: block;
	width: 20px;
	height: 20px;
	position: absolute;
	right: 50%;
	top: 50%;
	z-index: -1;
	margin: -10px -10px 0 0;
	background: #fff url(../images/spinner.gif) no-repeat center;
	background-size: 20px 20px;
	transform: translateZ(0);
}

@media print,
	(min-resolution: 120dpi) {

	#TB_window.plugin-details-modal.thickbox-loading:before {
		background-image: url(../images/spinner-2x.gif);
	}
}

.plugin-details-modal #TB_title {
	float: right;
	height: 1px;
}

.plugin-details-modal #TB_ajaxWindowTitle {
	display: none;
}

.plugin-details-modal #TB_closeWindowButton {
	right: auto;
	left: -30px;
	color: #f0f0f1;
}

.plugin-details-modal #TB_closeWindowButton:hover,
.plugin-details-modal #TB_closeWindowButton:focus {
	outline: none;
	box-shadow: none;
}

.plugin-details-modal #TB_closeWindowButton:hover::after,
.plugin-details-modal #TB_closeWindowButton:focus::after {
	outline: 2px solid;
	outline-offset: -4px;
	border-radius: 4px;
}

.plugin-details-modal .tb-close-icon {
	display: none;
}

.plugin-details-modal #TB_closeWindowButton:after {
	content: "\f335";
	font: normal 32px/29px 'dashicons';
	speak: never;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* move plugin install close icon to top on narrow screens */
@media screen and (max-width: 830px) {
	.plugin-details-modal #TB_closeWindowButton {
		left: 0;
		top: -30px;
	}
}

/* @todo: move this. */
img {
	border: none;
}

/* Metabox collapse arrow indicators */
.sidebar-name .toggle-indicator::before,
.meta-box-sortables .postbox .toggle-indicator::before,
.meta-box-sortables .postbox .order-higher-indicator::before,
.meta-box-sortables .postbox .order-lower-indicator::before,
.bulk-action-notice .toggle-indicator::before,
.privacy-text-box .toggle-indicator::before {
	content: "\f142";
	display: inline-block;
	font: normal 20px/1 dashicons;
	speak: never;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-decoration: none;
}

.js .widgets-holder-wrap.closed .toggle-indicator::before,
.meta-box-sortables .postbox.closed .handlediv .toggle-indicator::before,
.bulk-action-notice .bulk-action-errors-collapsed .toggle-indicator::before,
.privacy-text-box.closed .toggle-indicator::before {
	content: "\f140";
}

.postbox .handle-order-higher .order-higher-indicator::before {
	content: "\f343";
	color: inherit;
}

.postbox .handle-order-lower .order-lower-indicator::before {
	content: "\f347";
	color: inherit;
}

.postbox .handle-order-higher .order-higher-indicator::before,
.postbox .handle-order-lower .order-lower-indicator::before {
	position: relative;
    top: 0.11rem;
	width: 20px;
	height: 20px;
}

.postbox .handlediv .toggle-indicator::before {
	width: 20px;
	border-radius: 50%;
}

.postbox .handlediv .toggle-indicator::before {
	position: relative;
	top: 0.05rem;
	text-indent: -1px; /* account for the dashicon glyph uneven horizontal alignment */
}

.rtl .postbox .handlediv .toggle-indicator::before {
	text-indent: 1px; /* account for the dashicon glyph uneven horizontal alignment */
}

.bulk-action-notice .toggle-indicator::before {
	line-height: 16px;
	vertical-align: top;
	color: #787c82;
}

.postbox .handle-order-higher:focus,
.postbox .handle-order-lower:focus,
.postbox .handlediv:focus {
	box-shadow: inset 0 0 0 2px #2271b1;
	border-radius: 50%;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

.postbox .handle-order-higher:focus .order-higher-indicator::before,
.postbox .handle-order-lower:focus .order-lower-indicator::before,
.postbox .handlediv:focus .toggle-indicator::before {
	box-shadow: none;
	/* Only visible in Windows High Contrast mode */
	outline: 1px solid transparent;
}

/* @todo: appears to be Press This only and overridden */
#photo-add-url-div input[type="text"] {
	width: 300px;
}

/* Theme/Plugin file editor */
.alignleft h2 {
	margin: 0;
}

#template textarea {
	font-family: Consolas, Monaco, monospace;
	font-size: 13px;
	background: #f6f7f7;
	tab-size: 4;
}

#template textarea,
#template .CodeMirror {
	width: 100%;
	min-height: 60vh;
	height: calc( 100vh - 295px );
	border: 1px solid #dcdcde;
	box-sizing: border-box;
}

#templateside > h2 {
	padding-top: 6px;
	padding-bottom: 7px;
	margin: 0;
}

#templateside ol,
#templateside ul {
	margin: 0;
	padding: 0;
}
#templateside > ul {
	box-sizing: border-box;
	margin-top: 0;
	overflow: auto;
	padding: 0;
	min-height: 60vh;
	height: calc(100vh - 295px);
	background-color: #f6f7f7;
	border: 1px solid #dcdcde;
	border-right: none;
}
#templateside ul ul {
	padding-right: 12px;
}
#templateside > ul > li > ul[role=group] {
	padding-right: 0;
}

/*
 * Styles for Theme and Plugin file editors.
 */

/* Hide collapsed items. */
[role="treeitem"][aria-expanded="false"] > ul {
	display: none;
}

/* Use arrow dashicons for folder states, but hide from screen readers. */
[role="treeitem"] span[aria-hidden] {
	display: inline;
	font-family: dashicons;
	font-size: 20px;
	position: absolute;
	pointer-events: none;
}
[role="treeitem"][aria-expanded="false"] > .folder-label .icon:after {
	content: "\f141";
}
[role="treeitem"][aria-expanded="true"] > .folder-label .icon:after {
	content: "\f140";
}
[role="treeitem"] .folder-label {
	display: block;
	padding: 3px 12px 3px 3px;
	cursor: pointer;
}

/* Remove outline, and create our own focus and hover styles */
[role="treeitem"] {
	outline: 0;
}

[role="treeitem"] a:focus,
[role="treeitem"] .folder-label.focus {
	color: #043959;
	/* Reset default focus style. */
	box-shadow: none;
	/* Use an inset outline instead, so it's visible also over the current file item. */
	outline: 2px solid #2271b1;
	outline-offset: -2px;
}

[role="treeitem"].hover,
[role="treeitem"] .folder-label.hover {
	background-color: #f0f0f1;
}

.tree-folder {
	margin: 0;
	position: relative;
}
[role="treeitem"] li {
	position: relative;
}

/* Styles for folder indicators/depth */
.tree-folder .tree-folder::after {
	content: "";
	display: block;
	position: absolute;
	right: 2px;
	border-right: 1px solid #c3c4c7;
	top: -13px;
	bottom: 10px;
}
.tree-folder > li::before {
	content: "";
	position: absolute;
	display: block;
	border-right: 1px solid #c3c4c7;
	right: 2px;
	top: -5px;
	height: 18px;
	width: 7px;
	border-bottom: 1px solid #c3c4c7;
}
.tree-folder > li::after {
	content: "";
	position: absolute;
	display: block;
	border-right: 1px solid #c3c4c7;
	right: 2px;
	bottom: -7px;
	top: 0;
}

/* current-file needs to adjustment for .notice styles */
#templateside .current-file {
	margin: -4px 0 -2px;
}
.tree-folder > .current-file::before {
	right: 4px;
	height: 15px;
	width: 0;
	border-right: none;
	top: 3px;
}
.tree-folder > .current-file::after {
	bottom: -4px;
	height: 7px;
	right: 2px;
	top: auto;
}

/* Lines shouldn't continue on last item */
.tree-folder > li:last-child::after,
.tree-folder li:last-child > .tree-folder::after {
	display: none;
}

#theme-plugin-editor-selector,
#theme-plugin-editor-label,
#documentation label {
	font-weight: 600;
}

#theme-plugin-editor-label {
	display: inline-block;
	margin-bottom: 1em;
}

/* rtl:ignore */
#template textarea,
#docs-list {
	direction: ltr;
}

.fileedit-sub #theme,
.fileedit-sub #plugin {
	max-width: 40%;
}
.fileedit-sub .alignright {
	text-align: left;
}

#template p {
	width: 97%;
}

#file-editor-linting-error {
	margin-top: 1em;
	margin-bottom: 1em;
}
#file-editor-linting-error > .notice {
	margin: 0;
	display: inline-block;
}
#file-editor-linting-error > .notice > p {
	width: auto;
}
#template .submit {
	margin-top: 1em;
	padding: 0;
}

#template .submit input[type=submit][disabled] {
	cursor: not-allowed;
}
#templateside {
	float: left;
	width: 16em;
	word-wrap: break-word;
}

#postcustomstuff p.submit {
	margin: 0;
}

#templateside h4 {
	margin: 1em 0 0;
}

#templateside li {
	margin: 4px 0;
}

#templateside li:not(.howto) a,
.theme-editor-php .highlight {
	display: block;
	padding: 3px 12px 3px 0;
	text-decoration: none;
}

#templateside li.current-file > a {
	padding-bottom: 0;
}

#templateside li:not(.howto) > a:first-of-type {
	padding-top: 0;
}

#templateside li.howto {
	padding: 6px 12px 12px;
}

.theme-editor-php .highlight {
	margin: -3px -12px -3px 3px;
}

#templateside .highlight {
	border: none;
	font-weight: 600;
}

.nonessential {
	color: #646970;
	font-size: 11px;
	font-style: italic;
	padding-right: 12px;
}

#documentation {
	margin-top: 10px;
}

#documentation label {
	line-height: 1.8;
	vertical-align: baseline;
}

.fileedit-sub {
	padding: 10px 0 8px;
	line-height: 180%;
}

#file-editor-warning .file-editor-warning-content {
	margin: 25px;
}

/* @todo: can we use a common class for these? */
.nav-menus-php .item-edit:before,
.wp-customizer .control-section .accordion-section-title:after,
.wp-customizer .accordion-section-title:after,
.widget-top .widget-action .toggle-indicator:before {
	content: "\f140";
	font: normal 20px/1 dashicons;
	speak: never;
	display: block;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-decoration: none;
}

.widget-top .widget-action .toggle-indicator:before {
	padding: 1px 0 1px 2px;
	border-radius: 50%;
}

.handlediv,
.postbox .handlediv.button-link,
.item-edit,
.toggle-indicator {
	color: #787c82;
}

.widget-action {
	color: #50575e; /* #fafafa background in the Widgets screen */
}

.widget-top:hover .widget-action,
.widget-action:focus,
.handlediv:hover,
.handlediv:focus,
.postbox .handlediv.button-link:hover,
.postbox .handlediv.button-link:focus,
.item-edit:hover,
.item-edit:focus,
.sidebar-name:hover .toggle-indicator {
	color: #1d2327;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

.widget-top .widget-action:focus .toggle-indicator:before {
	box-shadow: 0 0 0 2px #2271b1;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

#customize-info.open .accordion-section-title:after,
.nav-menus-php .menu-item-edit-active .item-edit:before,
.widget.open .widget-top .widget-action .toggle-indicator:before,
.widget.widget-in-question .widget-top .widget-action .toggle-indicator:before {
	content: "\f142";
}

/*!
 * jQuery UI Draggable/Sortable 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 */
.ui-draggable-handle,
.ui-sortable-handle {
	touch-action: none;
}

/* Accordion */
.accordion-section {
	border-bottom: 1px solid #dcdcde;
	margin: 0;
}

.accordion-section.open .accordion-section-content,
.no-js .accordion-section .accordion-section-content {
	display: block;
}

.accordion-section.open:hover {
	border-bottom-color: #dcdcde;
}

.accordion-section-content {
	display: none;
	padding: 10px 20px 15px;
	overflow: hidden;
	background: #fff;
}

.accordion-section-title {
	margin: 0;
	position: relative;
	border-right: 1px solid #dcdcde;
	border-left: 1px solid #dcdcde;
	-webkit-user-select: none;
	user-select: none;
}

.js .accordion-section-title {
	cursor: pointer;
}

.js .accordion-section-title:after {
	position: absolute;
	top: 12px;
	left: 10px;
	z-index: 1;
}

.accordion-section-title:focus {
	/* Only visible in Windows High Contrast mode */
	outline: 1px solid transparent;
}

.accordion-section-title:hover:after,
.accordion-section-title:focus:after {
	border-color: #a7aaad transparent;
	/* Only visible in Windows High Contrast mode */
	outline: 1px solid transparent;
}

.cannot-expand .accordion-section-title {
	cursor: auto;
}

.cannot-expand .accordion-section-title:after {
	display: none;
}

.control-section .accordion-section-title,
.customize-pane-child .accordion-section-title {
	border-right: none;
	border-left: none;
	padding: 10px 14px 11px 10px;
	line-height: 1.55;
	background: #fff;
}

.control-section .accordion-section-title:after,
.customize-pane-child .accordion-section-title:after {
	top: calc(50% - 10px); /* Arrow height is 20px, so use half of that to vertically center */
}

.js .control-section:hover .accordion-section-title,
.js .control-section .accordion-section-title:hover,
.js .control-section.open .accordion-section-title,
.js .control-section .accordion-section-title:focus {
	color: #1d2327;
	background: #f6f7f7;
}

.control-section.open .accordion-section-title {
	/* When expanded */
	border-bottom: 1px solid #dcdcde;
}

/* Edit Site */
.network-admin .edit-site-actions {
	margin-top: 0;
}

/* My Sites */
.my-sites {
	display: block;
	overflow: auto;
	zoom: 1;
}

.my-sites li {
	display: block;
	padding: 8px 3%;
	min-height: 130px;
	margin: 0;
}

@media only screen and (max-width: 599px) {
	.my-sites li {
		min-height: 0;
	}
}

@media only screen and (min-width: 600px) {
	.my-sites.striped li {
		background-color: #fff;
		position: relative;
	}
	.my-sites.striped li:after {
		content: "";
		width: 1px;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		background: #c3c4c7;
	}

}
@media only screen and (min-width: 600px) and (max-width: 699px) {
	.my-sites li{
		float: right;
		width: 44%;
	}
	.my-sites.striped li {
		background-color: #fff;
	}
	.my-sites.striped li:nth-of-type(2n+1) {
		clear: right;
	}
	.my-sites.striped li:nth-of-type(2n+2):after {
		content: none;
	}
	.my-sites li:nth-of-type(4n+1),
	.my-sites li:nth-of-type(4n+2) {
		background-color: #f6f7f7;
	}

}

@media only screen and (min-width: 700px) and (max-width: 1199px) {
	.my-sites li {
		float: right;
		width: 27.333333%;
		background-color: #fff;
	}
	.my-sites.striped li:nth-of-type(3n+3):after {
		content: none;
	}
	.my-sites li:nth-of-type(6n+1),
	.my-sites li:nth-of-type(6n+2),
	.my-sites li:nth-of-type(6n+3) {
		background-color: #f6f7f7;
	}
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
	.my-sites li {
		float: right;
		width: 21%;
		padding: 8px 2%;
		background-color: #fff;
	}
	.my-sites.striped li:nth-of-type(4n+1) {
		clear: right;
	}
	.my-sites.striped li:nth-of-type(4n+4):after {
		content: none;
	}
	.my-sites li:nth-of-type(8n+1),
	.my-sites li:nth-of-type(8n+2),
	.my-sites li:nth-of-type(8n+3),
	.my-sites li:nth-of-type(8n+4) {
		background-color: #f6f7f7;
	}
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
	.my-sites li {
		float: right;
		width: 16%;
		padding: 8px 2%;
		background-color: #fff;
	}
	.my-sites.striped li:nth-of-type(5n+1) {
		clear: right;
	}
	.my-sites.striped li:nth-of-type(5n+5):after {
		content: none;
	}
	.my-sites li:nth-of-type(10n+1),
	.my-sites li:nth-of-type(10n+2),
	.my-sites li:nth-of-type(10n+3),
	.my-sites li:nth-of-type(10n+4),
	.my-sites li:nth-of-type(10n+5) {
		background-color: #f6f7f7;
	}
}

@media only screen and (min-width: 1600px) {
	.my-sites li {
		float: right;
		width: 12.666666%;
		padding: 8px 2%;
		background-color: #fff;
	}
	.my-sites.striped li:nth-of-type(6n+1) {
		clear: right;
	}
	.my-sites.striped li:nth-of-type(6n+6):after {
		content: none;
	}
	.my-sites li:nth-of-type(12n+1),
	.my-sites li:nth-of-type(12n+2),
	.my-sites li:nth-of-type(12n+3),
	.my-sites li:nth-of-type(12n+4),
	.my-sites li:nth-of-type(12n+5),
	.my-sites li:nth-of-type(12n+6) {
		background-color: #f6f7f7;
	}
}

.my-sites li a {
	text-decoration: none;
}

/* =Media Queries
-------------------------------------------------------------- */

/**
 * HiDPI Displays
 */
@media print,
  (min-resolution: 120dpi) {
	/* Back-compat for pre-3.8 */
	div.star-holder,
	div.star-holder .star-rating {
		background: url(../images/stars-2x.png?ver=20121108) repeat-x bottom right;
		background-size: 21px 37px;
	}

	.spinner {
		background-image: url(../images/spinner-2x.gif);
	}

}

@media screen and (max-width: 782px) {
	html.wp-toolbar {
		padding-top: var(--wp-admin--admin-bar--height);
	}

	.screen-reader-shortcut:focus {
		top: -39px;
	}

	.block-editor-page .screen-reader-shortcut:focus {
		top: 7px;
	}

	body {
		min-width: 240px;
		overflow-x: hidden;
	}

	body * {
		-webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
	}

	#wpcontent {
		position: relative;
		margin-right: 0;
		padding-right: 10px;
	}

	#wpbody-content {
		padding-bottom: 100px;
	}

	.wrap {
		clear: both;
		margin-left: 12px;
		margin-right: 0;
	}

	/* categories */
	#col-left,
	#col-right {
		float: none;
		width: auto;
	}

	#col-left .col-wrap,
	#col-right .col-wrap {
		padding: 0;
	}

	/* Hidden Elements */
	#collapse-menu,
	.post-format-select {
		display: none !important;
	}

	.wrap h1.wp-heading-inline {
		margin-bottom: 0.5em;
	}

	.wrap .add-new-h2, /* deprecated */
	.wrap .add-new-h2:active, /* deprecated */
	.wrap .page-title-action,
	.wrap .page-title-action:active {
		padding: 10px 15px;
		font-size: 14px;
		white-space: nowrap;
	}

	/* Feedback Messages */
	.notice,
	.wrap div.updated,
	.wrap div.error,
	.media-upload-form div.error {
		margin: 20px 0 10px;
		padding: 5px 10px;
		font-size: 14px;
		line-height: 175%;
	}

	.wp-core-ui .notice.is-dismissible {
		padding-left: 46px;
	}

	.notice-dismiss {
		padding: 13px;
	}

	.wrap .icon32 + h2 {
		margin-top: -2px;
	}

	.wp-responsive-open #wpbody {
		left: -16em;
	}

	code {
		word-wrap: break-word;
		word-wrap: anywhere; /* Firefox. Allow breaking long words anywhere */
		word-break: break-word; /* Webkit: Treated similarly to word-wrap: break-word */
	}

	/* General Metabox */
	.postbox {
		font-size: 14px;
	}

	.metabox-holder h3.hndle, /* Back-compat for pre-4.4 */
	.metabox-holder .stuffbox > h3, /* Back-compat for pre-4.4 */
	.metabox-holder .postbox > h3, /* Back-compat for pre-4.4 */
	.metabox-holder h2 {
		padding: 12px;
	}

	.nav-menus-php .metabox-holder h3 {
		padding: 0;
	}

	.postbox .handlediv {
		margin-top: 3px;
	}

	/* Subsubsub Nav */
	.subsubsub {
		font-size: 16px;
		text-align: center;
		margin-bottom: 15px;
	}

	/* Theme/Plugin File Editor */

	#template textarea,
	#template .CodeMirror {
		box-sizing: border-box;
	}

	#templateside {
		float: none;
		width: auto;
	}

	#templateside > ul {
		border-right: 1px solid #dcdcde;
	}

	#templateside li {
		margin: 0;
	}

	#templateside li:not(.howto) a {
		display: block;
		padding: 5px;
	}
	#templateside li.howto {
		padding: 12px;
	}

	#templateside .highlight {
		padding: 5px;
		margin-right: -5px;
		margin-top: -5px;
	}

	#template > div,
	#template .notice {
		float: none;
		margin: 1em 0;
		width: auto;
	}

	#template .CodeMirror,
	#template textarea {
		width: 100%;
	}

	#templateside ul ul {
		padding-right: 1.5em;
	}
	[role="treeitem"] .folder-label {
		display: block;
		padding: 5px;
	}
	.tree-folder > li::before,
	.tree-folder > li::after,
	.tree-folder .tree-folder::after {
		right: -8px;
	}
	.tree-folder > li::before {
		top: 0;
		height: 13px;
	}
	.tree-folder > .current-file::before {
		right: -5px;
		top: 7px;
		width: 4px;
	}
	.tree-folder > .current-file::after {
		height: 9px;
		right: -8px;
	}
	.wrap #templateside span.notice {
		margin-right: -5px;
		width: 100%;
	}

	.fileedit-sub .alignright {
		float: right;
		margin-top: 15px;
		width: 100%;
		text-align: right;
	}

	.fileedit-sub .alignright label {
		display: block;
	}

	.fileedit-sub #theme,
	.fileedit-sub #plugin {
		margin-right: 0;
		max-width: 70%;
	}

	.fileedit-sub input[type="submit"] {
		margin-bottom: 0;
	}

	#documentation label[for="docs-list"] {
		display: block;
	}

	#documentation select[name="docs-list"] {
		margin-right: 0;
		max-width: 60%;
	}

	#documentation input[type="button"] {
		margin-bottom: 0;
	}

	#wpfooter {
		display: none;
	}

	#comments-form .checkforspam {
		display: none;
	}

	.edit-comment-author {
		margin: 2px 0 0;
	}

	.filter-drawer .filter-group-feature input,
	.filter-drawer .filter-group-feature label {
		line-height: 2.1;
	}

	.filter-drawer .filter-group-feature label {
		margin-right: 32px;
	}

	.wp-filter .button.drawer-toggle {
		font-size: 13px;
		line-height: 2;
		height: 28px;
	}

	/* Fix help tab columns for smaller screens */
	#screen-meta #contextual-help-wrap {
		overflow: visible;
	}

	#screen-meta #contextual-help-back,
	#screen-meta .contextual-help-sidebar {
		display: none;
	}

	#screen-meta .contextual-help-tabs {
		clear: both;
		width: 100%;
		float: none;
	}

	#screen-meta .contextual-help-tabs ul {
		margin: 0 0 1em;
		padding: 1em 0 0;
	}

	#screen-meta .contextual-help-tabs .active {
		margin: 0;
	}

	#screen-meta .contextual-help-tabs-wrap {
		clear: both;
		max-width: 100%;
		float: none;
	}

	#screen-meta,
	#screen-meta-links {
		margin-left: 10px;
	}

	#screen-meta-links {
		margin-bottom: 20px; /* Add margins beneath links for better spacing between boxes and elements */
	}

	.wp-filter .search-form input[type="search"] {
		font-size: 1rem;
	}

	.wp-filter .search-form.search-plugins {
		/* This element is a flex item. */
		min-width: 100%;
	}
}

/* Smartphone */
@media screen and (max-width: 600px) {
	/* Disable horizontal scroll when responsive menu is open
	   since we push the main content off to the right. */
	#wpwrap.wp-responsive-open {
		overflow-x: hidden;
	}

	html.wp-toolbar {
		padding-top: 0;
	}

	.screen-reader-shortcut:focus {
		top: 7px;
	}

	#wpbody {
		padding-top: 46px;
	}

	/* Keep full-width boxes on Edit Post page from causing horizontal scroll */
	div#post-body.metabox-holder.columns-1 {
		overflow-x: hidden;
	}

	h1.nav-tab-wrapper,
	.wrap h2.nav-tab-wrapper,
	.nav-tab-wrapper {
		border-bottom: 0;
	}

	h1 .nav-tab,
	h2 .nav-tab,
	h3 .nav-tab,
	nav .nav-tab {
		margin: 10px 0 0 10px;
		border-bottom: 1px solid #c3c4c7;
	}

	.nav-tab-active:hover,
	.nav-tab-active:focus,
	.nav-tab-active:focus:active {
		border-bottom: 1px solid #c3c4c7;
	}

	.wp-filter .search-form.search-plugins label {
		width: 100%;
	}
}

@media screen and (max-width: 480px) {
	.metabox-prefs-container {
		display: grid;
	}

	.metabox-prefs-container > * {
		display: inline-block;
		padding: 2px;
	}
}

@media screen and (max-width: 320px) {
	/* Prevent default center alignment and larger font for the Right Now widget when
	   the network dashboard is viewed on a small mobile device. */
	#network_dashboard_right_now .subsubsub {
		font-size: 14px;
		text-align: right;
	}
}
